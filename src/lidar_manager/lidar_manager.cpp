/******************************************************************************
 * Copyright 2024 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai
 *
 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.
 *
 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONS<PERSON>QUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#include "lidar_manager/lidar_manager.h"
#include "app_event.h"
#include "data_struct.h"
#include "mech_udp.h"
#include "mes_widget.h"
#include "protocol/protocol_common.h"
#include "protocol/protocol_parser.h"
#include "rsfsc_lib/include/widget_lidar_info.h"
#include "rsfsc_log/rsfsc_log_macro.h"
#include <QByteArray>
#include <QFile>
#include <QHostAddress>
#include <string>

// NOLINTNEXTLINE(modernize-concat-nested-namespaces)
namespace robosense
{
namespace lidar
{

LidarManager::LidarManager(rsfsc_lib::WidgetLidarInfo* _widget_lidar_info) :
  MechTcp(ProtocolType::MECH_AIRY, static_cast<int>(_widget_lidar_info->getLidarIndex())),
  lidar_info_(_widget_lidar_info),
  reg_csv_utils_ptr_(app()->getCsvUtils("airy_reg"))
{
  setLogIndex(getLidarIndex());
  init(ProtocolType::MECH_AIRY);
  if (reg_csv_utils_ptr_ == nullptr)
  {
    LOG_INDEX_ERROR("获取reg csv解析器失败");
    return;
  }
  getLidarInfo()->setFixedLidarInstallPosition(rsfsc_lib::LidarInstallPosition::LIDAR_INSTALL_POSITION_FL);

  tcpdump_exclude_msop_.setLogIndex(getLidarIndex());
  tcpdump_only_msop_.setLogIndex(getLidarIndex());
}

LidarManager::~LidarManager() { LOG_INDEX_INFO("destructed"); }

void LidarManager::abort()
{
  is_abort_ = true;
  cv_msop_.notify_all();
  MechTcp::abort();
}

int LidarManager::getLidarIndex() { return static_cast<int>(lidar_info_->getLidarIndex()); }
QString LidarManager::getLidarInfoSn() { return getLidarInfo()->getLidarSN(); }

uint32_t LidarManager::getLidarPsVersion()
{
  uint32_t ps_version = 0;
  std::memcpy(&ps_version, config_para_.ps_version.data(), 4);
  return ps_version;
}
uint32_t LidarManager::getLidarPlVersion()
{
  uint32_t pl_version = 0;
  std::memcpy(&pl_version, config_para_.pl_version.data(), 4);
  return pl_version;
}

bool LidarManager::readRegDataByKey(const QString& _key, uint32_t& _data, const int _timeout)
{
  if (reg_csv_utils_ptr_ == nullptr)
  {
    LOG_INDEX_ERROR("获取reg csv解析器失败, 读取{}寄存器失败", _key);
    return false;
  }
  auto reg_info = reg_csv_utils_ptr_->getRegisterInfo(_key.toStdString());
  if (!reg_info.is_ok)
  {
    LOG_INDEX_ERROR("获取{}寄存器信息失败", _key);
    return false;
  }

  if (_key.contains("cmd"))
  {
    if (!readCmd(reg_info.address, _data, _timeout))
    {
      LOG_INDEX_ERROR("[{0:}:{1:#x}] cmd读取失败", _key, reg_info.address);
      return false;
    }
  }

  // return MechTcp::readRegData(reg_info.address, _data, _timeout);
  if (!MechTcp::readRegData(reg_info.address, _data, _timeout))
  {
    LOG_INDEX_ERROR("[{:}:{:#x}] 读取失败", _key, reg_info.address);
    return false;
  }

  LOG_INDEX_INFO("[{:}:{:#x}] 读取成功, 数据为{:#x}", _key, reg_info.address, _data);
  return true;
}
bool LidarManager::readTopRegDataByKey(const QString& _key,
                                       uint32_t& _data,
                                       const uint32_t _byte_size,
                                       const int _timeout)
{
  if (reg_csv_utils_ptr_ == nullptr)
  {
    LOG_INDEX_ERROR("获取reg csv解析器失败, 读取{}寄存器失败", _key);
    return false;
  }
  auto reg_info = reg_csv_utils_ptr_->getRegisterInfo(_key.toStdString());
  if (!reg_info.is_ok)
  {
    LOG_INDEX_ERROR("获取{}寄存器信息失败", _key);
    return false;
  }

  if (_key.contains("cmd"))
  {
    if (!readCmd(reg_info.address, _data, _timeout))
    {
      LOG_INDEX_ERROR("[{0:}:{1:#x}] cmd读取失败", _key, reg_info.address);
      return false;
    }
  }

  std::vector<uint32_t> data;
  if (!MechTcp::readRegData(reg_info.address, _byte_size, data, _timeout))
  {
    LOG_INDEX_ERROR("[{:}*{}:{:#x}] 读取失败", _key, _byte_size, reg_info.address);
    return false;
  }

  _data = 0;

  for (auto& item : data)
  {
    _data = (_data << 8U) | (item & 0xffU);
  }

  LOG_INDEX_INFO("[{:}*{}:{:#x}] 读取成功, 数据为{:#x}", _key, _byte_size, reg_info.address, _data);
  return true;
}
bool LidarManager::writeRegDataByKey(const QString& _key, const uint32_t _data)
{
  auto reg_info = reg_csv_utils_ptr_->getRegisterInfo(_key.toStdString());
  if (!reg_info.is_ok)
  {
    LOG_INDEX_ERROR("获取{}寄存器信息失败", _key);
    return false;
  }
  if (!MechTcp::writeRegData(static_cast<uint32_t>(reg_info.address), _data))
  {
    LOG_INDEX_ERROR("[{:}:{:#x}] 写入{:#x}失败", _key, reg_info.address, _data);
    return false;
  }
  LOG_INDEX_INFO("[{:}:{:#x}] 写入成功, 数据为{:#x}", _key, reg_info.address, _data);
  return true;
}

bool LidarManager::readReg459(const uint32_t _reg_addr, uint32_t& _reg_val)
{
  if (!writeRegData(0x3001, (_reg_addr >> 8U) & 0xffU))
  {
    LOG_INDEX_ERROR("寄存器写入失败 0x3001: {:#x}", _reg_addr);
    return false;
  }
  if (!writeRegData(0x3002, (_reg_addr)&0xffU))
  {
    LOG_INDEX_ERROR("寄存器写入失败 0x3002: {:#x}", _reg_addr);
    return false;
  }
  if (!writeRegData(0x3000, 0x00))
  {
    LOG_INDEX_ERROR("寄存器写入失败 0x3000: {:#x}", _reg_addr);
    return false;
  }
  if (!writeRegData(0x3000, 0x02))
  {
    LOG_INDEX_ERROR("寄存器写入失败 0x3000: {:#x}", _reg_addr);
    return false;
  }

  uint32_t reg_val = 0;
  if (!readRegData(0x3006, reg_val))
  {
    LOG_INDEX_ERROR("读取reg_459寄存器0x3006失败");
    return false;
  }
  _reg_val = reg_val;
  return true;
}
bool LidarManager::readReg459Pair(const uint32_t _reg_addr, uint32_t& _reg_val)
{
  auto reg_addr_high = _reg_addr + 1;
  auto reg_addr_low  = _reg_addr;

  uint32_t reg_val_high = 0;
  if (!readReg459(reg_addr_high, reg_val_high))
  {
    LOG_INDEX_ERROR("读取reg_459寄存器失败");
    return false;
  }
  uint32_t reg_val_low = 0;
  if (!readReg459(reg_addr_low, reg_val_low))
  {
    LOG_INDEX_ERROR("读取reg_459寄存器失败");
    return false;
  }
  _reg_val = (reg_val_high << 8U) | reg_val_low;
  LOG_INDEX_INFO("读取reg_459寄存器成功, [{:#x}*2, {:#x}]", _reg_addr, _reg_val);
  return true;
}

bool LidarManager::readVbdCurve(uint16_t& _v0, uint16_t& _v1, uint16_t& _v2)
{
  // 获取vbd曲线的寄存器信息
  auto reg_info_v0 = reg_csv_utils_ptr_->getRegisterInfo("vbd_n40");
  auto reg_info_v1 = reg_csv_utils_ptr_->getRegisterInfo("vbd_85");
  auto reg_info_v2 = reg_csv_utils_ptr_->getRegisterInfo("vbd_125");
  if (!reg_info_v0.is_ok || !reg_info_v1.is_ok || !reg_info_v2.is_ok)
  {
    LOG_INDEX_ERROR("获取vbd曲线寄存器失败");
    return false;
  }

  const int MAX_ATTEMPTS = 5;  // 总共尝试5次
  for (int i = 0; i < MAX_ATTEMPTS; i++)
  {
    bool stable           = true;  // 标记本轮尝试中的3次读取是否完全一致
    uint16_t candidate_v0 = 0;
    uint16_t candidate_v1 = 0;
    uint16_t candidate_v2 = 0;

    // 连续读取3次
    for (int j = 0; j < 3; j++)
    {
      uint32_t temp_v0 = 0;
      uint32_t temp_v1 = 0;
      uint32_t temp_v2 = 0;
      if (!readReg459Pair(reg_info_v0.address, temp_v0))
      {
        stable = false;
        break;
      }
      std::this_thread::sleep_for(std::chrono::milliseconds(100));
      if (!readReg459Pair(reg_info_v1.address, temp_v1))
      {
        stable = false;
        break;
      }
      std::this_thread::sleep_for(std::chrono::milliseconds(100));
      if (!readReg459Pair(reg_info_v2.address, temp_v2))
      {
        stable = false;
        break;
      }
      std::this_thread::sleep_for(std::chrono::milliseconds(100));
      if (j == 0)
      {
        // 第一次读取，记录下候选值
        candidate_v0 = static_cast<uint16_t>(temp_v0);
        candidate_v1 = static_cast<uint16_t>(temp_v1);
        candidate_v2 = static_cast<uint16_t>(temp_v2);
      }
      else
      {
        // 后续读取与第一次结果进行对比
        if (candidate_v0 != static_cast<uint16_t>(temp_v0) || candidate_v1 != static_cast<uint16_t>(temp_v1) ||
            candidate_v2 != static_cast<uint16_t>(temp_v2))
        {
          stable = false;
          break;
        }
      }
      std::this_thread::sleep_for(std::chrono::milliseconds(200));
    }

    if (stable)
    {
      // 若3次连续读取一致，返回成功
      _v0 = candidate_v0;
      _v1 = candidate_v1;
      _v2 = candidate_v2;
      return true;
    }
  }

  LOG_INDEX_ERROR("连续读取vbd曲线不稳定，经过12次尝试均未连续读取3次一致的结果");
  return false;
}
bool LidarManager::readVbd(uint32_t& _vbd_intercept_hex, uint32_t& _vbd_err_hex)
{
  auto reg_info_vbd_intercept = reg_csv_utils_ptr_->getRegisterInfo("vbd_intercept");
  auto reg_info_vbd_err       = reg_csv_utils_ptr_->getRegisterInfo("vbd_err");
  if (!reg_info_vbd_intercept.is_ok || !reg_info_vbd_err.is_ok)
  {
    LOG_INDEX_ERROR("获取vbd寄存器失败");
    return false;
  }
  if (!readTopRegDataByKey("vbd_intercept", _vbd_intercept_hex, 3))
  {
    LOG_INDEX_ERROR("读取vbd截距寄存器失败");
    return false;
  }
  if (!readTopRegDataByKey("vbd_err", _vbd_err_hex, 2))
  {
    LOG_INDEX_ERROR("读取vbd误差寄存器失败");
    return false;
  }
  return true;
}
bool LidarManager::connectAndWaitForTop(const uint32_t _msec)
{
  if (!connect(_msec))
  {
    return false;
  }
  return waitForTopStartUp(_msec);
}
bool LidarManager::connect(const uint32_t _msec)
{
  if (!MechTcp::connect(_msec))
  {
    return false;
  }
  return getConfigParam(config_para_);
}
bool LidarManager::connect(const QString& _ip, const int _port, const int _timeout)
{
  if (!MechTcp::connect(_ip.toStdString(), _port, _timeout))
  {
    return false;
  }

  return getConfigParam(config_para_);
}
bool LidarManager::getConnectedIpPort(QString& _ip, int& _port)
{
  if (!isConnected())
  {
    return false;
  }
  _ip   = QString::fromStdString(getIP());
  _port = getPort();
  return true;
}

bool LidarManager::setPowerOn() { return true; }
// NOLINTNEXTLINE(bugprone-easily-swappable-parameters)
bool LidarManager::changeLidarNet(const QString& _ip, const int _msop_port, const int _difop_port)
{
  mech::NetPara net_para = config_para_.net_info;
  net_para.setIpLocal(_ip.toStdString());
  net_para.setMsopPort(_msop_port);
  net_para.setDifopPort(_difop_port);
  net_para.setNetmaskLocal("***********");
  if (!app()->getWidgetLogSetting()->isUseIPPool())
  {
    // 0x40.0x2C.0x76.0x8.0x4A.0xCC
    net_para.mac.at(0) = 0x40;
    net_para.mac.at(1) = 0x2c;
    net_para.mac.at(2) = 0x76;
    net_para.mac.at(3) = 0x8;
    if (_ip == "*************")
    {
      net_para.mac.at(4) = 0x4a;
      net_para.mac.at(5) = 0xcc;
    }
    else
    {
      net_para.mac.at(4) = net_para.ip_local.at(2);
      net_para.mac.at(5) = net_para.ip_local.at(3);
    }
  }
  else
  {
    net_para.setMacAddr(mes_mac_address_);
  }

  std::vector<uint8_t> net_data(sizeof(mech::NetPara), 0);
  std::memcpy(net_data.data(), &net_para, sizeof(mech::NetPara));
  return writeCmd(mech::NET_CMD_CONFIG_SET_NETWORK, net_data);
}

bool LidarManager::writeCsvData(const QString& _key)
{
  auto reg_info_map                 = reg_csv_utils_ptr_->getSelectIndexPropertyRegisterInfo(_key.toStdString());
  bool overall_verification_success = true;

  for (auto& [reg_name, reg_info] : reg_info_map)
  {
    bool write_success        = false;
    uint32_t read_back_value  = 0;
    bool verification_success = false;

    if (reg_name.find("cmd") != std::string::npos)
    {
      // TODO: 放到一个函数里边
      int data_size = 4;
      if (reg_info.extra_str_info.at(0) != "")
      {
        data_size = std::stoi(reg_info.extra_str_info.at(0));
      }
      std::vector<uint8_t> data(data_size, 0);
      for (size_t i = 0; i < data.size(); i++)
      {
        data[i] = static_cast<uint8_t>(static_cast<uint32_t>(reg_info.value_at_calibration) >> (i * 8U));
      }

      write_success = writeCmd(reg_info.address, data);
      if (!write_success)
      {
        LOG_INDEX_ERROR("[{0:}:{1:#x}]写入[{2:}:{2:#x}]失败", reg_name, reg_info.address,
                        reg_info.value_at_calibration);
        // 记录写入失败的情况
        writeVerificationDataToFile(QString::fromStdString(reg_name), reg_info.address, reg_info.value_at_calibration,
                                    0, false);
        return false;
      }
      LOG_INDEX_INFO("[{0:}:{1:#x}]写入[{2:}:{2:#x}]成功", reg_name, reg_info.address, reg_info.value_at_calibration);

      // CMD 寄存器跳过回读校验，不记录到文件
      continue;
    }

    // 写入普通寄存器
    write_success = MechTcp::writeRegData(reg_info.address, reg_info.value_at_calibration);
    if (!write_success)
    {
      LOG_INDEX_ERROR("[{0:}:{1:#x}]写入[{2:}:{2:#x}]失败", reg_name, reg_info.address, reg_info.value_at_calibration);
      // 记录写入失败的情况
      writeVerificationDataToFile(QString::fromStdString(reg_name), reg_info.address, reg_info.value_at_calibration, 0,
                                  false);
      return false;
    }
    LOG_INDEX_INFO("[{0:}:{1:#x}]写入[{2:}:{2:#x}]成功", reg_name, reg_info.address, reg_info.value_at_calibration);

    // 进行回读校验（仅当CSV校验文件路径不为空时）
    if (!csv_verification_file_path_.isEmpty())
    {
      // 等待一小段时间确保写入完成
      std::this_thread::sleep_for(std::chrono::milliseconds(10));

      // 回读寄存器数据
      if (MechTcp::readRegData(reg_info.address, read_back_value))
      {
        verification_success = (read_back_value == static_cast<uint32_t>(reg_info.value_at_calibration));
        if (verification_success)
        {
          LOG_INDEX_INFO("[{0:}:{1:#x}]回读校验成功, 写入值:0x{2:x}, 读取值:0x{3:x}", reg_name, reg_info.address,
                         reg_info.value_at_calibration, read_back_value);
        }
        else
        {
          LOG_INDEX_ERROR("[{0:}:{1:#x}]回读校验失败, 写入值:0x{2:x}, 读取值:0x{3:x}", reg_name, reg_info.address,
                          reg_info.value_at_calibration, read_back_value);
          overall_verification_success = false;
        }
      }
      else
      {
        LOG_INDEX_ERROR("[{0:}:{1:#x}]回读失败", reg_name, reg_info.address);
        verification_success         = false;
        overall_verification_success = false;
      }

      // 记录校验结果到文件
      writeVerificationDataToFile(QString::fromStdString(reg_name), reg_info.address, reg_info.value_at_calibration,
                                  read_back_value, verification_success);
    }
  }

  if (!csv_verification_file_path_.isEmpty() && !overall_verification_success)
  {
    LOG_INDEX_ERROR("CSV数据写入回读校验存在失败项，请检查校验文件: {}", csv_verification_file_path_);
  }

  return true;
}
bool LidarManager::writeCsvInitRegData() { return writeCsvData("init"); }

bool LidarManager::deInitLidarPara()
{
  if (!writeCsvData("deinit"))
  {
    LOG_INDEX_ERROR("写入deinit寄存器数据失败");
    return false;
  }
  LOG_INDEX_INFO("写入deinit寄存器数据成功");
  return true;
}

bool LidarManager::startUpOpticalErrorTest()
{
  // light_uptst_en set 1
  for (int i = 0; i < 3; ++i)
  {
    if (writeRegDataByKey("light_uptst_en", 1))
    {
      return true;
    }
    std::this_thread::sleep_for(std::chrono::milliseconds(20));
  }
  return false;
}
bool LidarManager::stopUpOpticalErrorTest()
{
  // light_uptst_en set 0
  for (int i = 0; i < 3; ++i)
  {
    if (writeRegDataByKey("light_uptst_en", 0))
    {
      return true;
    }
    std::this_thread::sleep_for(std::chrono::milliseconds(20));
  }
  return false;
}
bool LidarManager::getUpOpticalError(uint32_t& _error, uint32_t& _total)
{
  _error = 0;
  _total = 0;
  if (!readTopRegDataByKey("light_uptst_error", _error, 4) || !readTopRegDataByKey("light_uptst_total", _total, 4))
  {
    optical_up_error_rate_ = NAN;
    return false;
  }

  if (_total == 0)
  {
    optical_up_error_rate_ = NAN;
  }
  else
  {
    optical_up_error_rate_ = static_cast<double>(_error) / _total;
  }

  _error = static_cast<int>(_error);
  _total = static_cast<int>(_total);
  return true;
}
bool LidarManager::startDownOpticalErrorTest()
{
  // light_dwntst_en set 1
  for (int i = 0; i < 3; ++i)
  {
    if (writeRegDataByKey("light_dwntst_en", 1))
    {
      return true;
    }
    std::this_thread::sleep_for(std::chrono::milliseconds(20));
  }
  return false;
}
bool LidarManager::stopDownOpticalErrorTest()
{
  // light_dwntst_en set 0
  for (int i = 0; i < 3; ++i)
  {
    if (writeRegDataByKey("light_dwntst_en", 0))
    {
      return true;
    }
    std::this_thread::sleep_for(std::chrono::milliseconds(20));
  }
  return false;
}
bool LidarManager::getDownOpticalError(uint32_t& _error, uint32_t& _total)
{
  _error = 0;
  _total = 0;
  if (!readRegDataByKey("light_dwntst_error", _error) || !readRegDataByKey("light_dwntst_total", _total))
  {
    optical_down_error_rate_ = NAN;
    return false;
  }

  if (_total == 0)
  {
    optical_down_error_rate_ = NAN;
  }
  else
  {
    optical_down_error_rate_ = static_cast<double>(_error) / _total;
  }

  return true;
}

bool LidarManager::getDifopData(const int _timeout) { return isLastDifopNotTimeout(_timeout); }

std::vector<MsopPacket96> LidarManager::getMsopData(const int _count, const int _timeout)
{
  if (!getConfigParam(config_para_))
  {
    return {};
  }

  uint32_t ip_addr_uint = 0;
  uint16_t port         = 0;
  std::reverse(config_para_.net_info.ip_local.begin(), config_para_.net_info.ip_local.end());
  std::memcpy(&ip_addr_uint, config_para_.net_info.ip_local.data(), 4);
  std::memcpy(&port, &config_para_.net_info.msop_port, 2);

  QHostAddress ip_addr(ip_addr_uint);
  if (ip_addr.isNull())
  {
    LOG_INDEX_ERROR("雷达ip地址{}不合法，请联系开发者", ip_addr.toString().toStdString());
    return {};
  }

  // 条件变量等待包，设置超时时间
  std::vector<MsopPacket96> msop_pkt_vec;
  std::shared_ptr<MechUdp> msop_udp_client = std::make_shared<MechUdp>(1248);
  msop_udp_client->setLogIndex(getLidarIndex());
  msop_udp_client->regRecvCallback([&](const char* _udp_data) {
    uint32_t pkt_head = 0;
    std::memcpy(&pkt_head, _udp_data, 4);
    pkt_head = BSWAP32(pkt_head);
    if (pkt_head != MSOP_PKT_HEAD)
    {
      return;
    }
    msop_pkt_vec.emplace_back();
    std::memcpy(&msop_pkt_vec.back(), _udp_data, sizeof(MsopPacket96));
    msop_pkt_vec.back().toLittleEndian();

    last_msop_time_ = QDateTime::currentDateTime();
    if (static_cast<int>(msop_pkt_vec.size()) >= _count)
    {
      cv_msop_.notify_one();
    }
  });

  if (!msop_udp_client->start(ip_addr.toString().toStdString(), port))
  {
    LOG_INDEX_ERROR("msop监控失败 [{}:{}]", ip_addr.toString().toStdString(), port);
    return {};
  }

  std::unique_lock<std::mutex> lock(mtx_msop_);
  cv_msop_.wait_for(lock, std::chrono::milliseconds(_timeout));

  msop_udp_client->stop();
  if (static_cast<int>(msop_pkt_vec.size()) < _count)
  {
    LOG_INDEX_ERROR("获取msop数据失败，msop size: {}", msop_pkt_vec.size());
  }

  msop_pkt_vec.resize(std::min(static_cast<int>(msop_pkt_vec.size()), _count));

  return msop_pkt_vec;
}

bool LidarManager::getConfigParam(mech::ConfigPara& _config_para)
{
  if (!readConfigParamater(_config_para) && !readConfigParamater(_config_para))
  {
    LOG_INDEX_ERROR("获取雷达参数失败");
    return false;
  }

  lidar_sn_         = _config_para.getSn();
  pl_version_       = _config_para.getPlVersion();
  ps_version_       = _config_para.getPsVersion();
  software_version_ = _config_para.getSoftwareVersion();
  motor_version_    = _config_para.getMotorVersion();
  ip_addr_          = _config_para.getIpLocal();
  msop_port_        = _config_para.getMsopPort();
  difop_port_       = _config_para.getDifopPort();

  app()->getWidgetLogSetting()->setFirmwareRevision(getLidarIndex(), static_cast<qint32>(getPsVersion()),
                                                    static_cast<qint32>(getPlVersion()));

  LOG_INDEX_DEBUG("获取雷达参数成功 sn: {}, pl_version: {:#x}, ps_version: {:#x}, software_version: {:#x}, "
                  "motor_version: {:#x}, 实时转速: {}rpm",
                  lidar_sn_, pl_version_, ps_version_, software_version_, motor_version_,
                  _config_para.getMotorRealTimeSpeed());
  LOG_INDEX_DEBUG("ip: {}, msop_port: {}, difop_port: {}", ip_addr_, msop_port_, difop_port_);
  return true;
}

std::string LidarManager::getConfigParamLidarSN() { return config_para_.getSn(); }

bool LidarManager::isNetNeedChange(bool& _is_need_change,
                                   const QString& _ip,
                                   const int _msop_port,
                                   const int _difop_port)
{
  if (!getConfigParam(config_para_) && !getConfigParam(config_para_))
  {
    return false;
  }

  if (config_para_.getIpLocal() != _ip.toStdString() || config_para_.getMsopPort() != _msop_port ||
      config_para_.getDifopPort() != _difop_port || mes_mac_address_ != config_para_.getMacAddr())
  {
    LOG_INDEX_INFO("雷达网络需要修改, 当前网络 [{}:{}, difop:{}] , 目标网络 [{}:{}, difop:{}]",
                   config_para_.getIpLocal(), config_para_.getMsopPort(), config_para_.getDifopPort(), _ip, _msop_port,
                   _difop_port);
    _is_need_change = true;
    return true;
  }

  _is_need_change = false;
  LOG_INDEX_INFO("雷达网络不需要修改, 当前网络 [{}:{}, difop:{}] , 目标网络 [{}:{}, difop:{}]",
                 config_para_.getIpLocal(), config_para_.getMsopPort(), config_para_.getDifopPort(), _ip, _msop_port,
                 _difop_port);
  return true;
}
bool LidarManager::startMonitorDifop()
{
  if (difop_udp_client_ != nullptr)
  {
    difop_udp_client_->stop();
    difop_udp_client_.reset();
    // std::this_thread::sleep_for(std::chrono::milliseconds(5000));
  }
  if (!getConfigParam(config_para_))
  {
    return false;
  }
  if (config_para_.getIpLocal() != getIP())
  {
    LOG_INDEX_ERROR("雷达ip地址不一致，请查看是否修改IP后未进行重启，连接IP[{}:{}], 配置文件ip[{}:{}]", getIP(),
                    getPort(), config_para_.getIpLocal(), config_para_.getMsopPort());
    return false;
  }

  difop_udp_client_ = std::make_shared<MechUdp>(1248);
  difop_udp_client_->setLogIndex(getLogIndex());
  difop_udp_client_->regRecvCallback([this](const char* _udp_data) {
    // LOG_INDEX_INFO("接收到difop数据");
    std::memcpy(&difop_pkt_, _udp_data, sizeof(DifopPacket));
    difop_pkt_.toBigEndian();
    last_difop_time_ = QDateTime::currentDateTime();
    if (difop_cb_)
    {
      difop_cb_(difop_pkt_);
    }
  });

  uint32_t ip_addr_uint = 0;
  uint16_t port         = 0;
  std::reverse(config_para_.net_info.ip_local.begin(), config_para_.net_info.ip_local.end());
  std::memcpy(&ip_addr_uint, config_para_.net_info.ip_local.data(), 4);
  std::memcpy(&port, &config_para_.net_info.difop_port, 2);

  QHostAddress ip_addr(ip_addr_uint);
  if (ip_addr.isNull())
  {
    LOG_INDEX_ERROR("雷达ip地址不合法，请联系开发者");
    return false;
  }

  if (!difop_udp_client_->start(ip_addr.toString().toStdString(), port))
  {
    LOG_INDEX_ERROR("difop监控失败 [{}:{}]", ip_addr.toString().toStdString(), port);
    return false;
  }

  LOG_INDEX_INFO("开始接收 difop数据 [{}:{}]", ip_addr.toString().toStdString(), port);
  return true;
}

bool LidarManager::stopMonitorDifop()
{
  if (difop_udp_client_ != nullptr)
  {
    difop_udp_client_->stop();
  }
  return true;
}

bool LidarManager::firmwareUpdate(const uint16_t& _cmd_type, const QString& _file_path)
{
  QFile file(_file_path);
  int timeout = 30000;
  if (!file.open(QIODevice::ReadOnly))
  {
    LOG_INDEX_ERROR("打开App文件失败，err_msg: {}，文件路径：{}", file.errorString(), _file_path.toStdString());
    return false;
  }

  uint32_t check_sum = 0;

  uint32_t data_size = file.size();
  uint32_t pkt_count = 1;
  uint32_t pkt_size  = 1024;
  // uint32_t pkt_num   = data_size / pkt_size + (data_size % pkt_size == 0 ? 0 : 1);

  std::vector<uint8_t> data;
  ProtoParser::pushToPayload(data, static_cast<uint8_t>(0xc1), data_size);
  if (!writeCmd(_cmd_type, data, timeout))
  {
    LOG_INDEX_ERROR("发送开始升级命令失败");
    return false;
  }

  QByteArray raw_data = file.readAll();
  uint32_t offset     = 0;

  RSFSCLog::getInstance(getLidarIndex())->setFileLogLevel(RSFSCLog::RSFSCLOG_LEVEL_INFO);
  while (offset < static_cast<uint32_t>(raw_data.size()) && !is_abort_)
  {
    std::vector<uint8_t> data;
    ProtoParser::pushToPayload(data, static_cast<uint8_t>(0xc2), pkt_count++);
    uint32_t chunk_size = std::min(pkt_size, static_cast<uint32_t>(raw_data.size() - offset));
    data.insert(data.end(), raw_data.begin() + offset, raw_data.begin() + offset + chunk_size);
    offset += chunk_size;

    if (!writeCmd(_cmd_type, data))
    {
      LOG_INDEX_ERROR("发送升级数据失败");
      return false;
    }
  }

  for (char item : raw_data)
  {
    check_sum += static_cast<uint8_t>(item);
  }
  RSFSCLog::getInstance(getLidarIndex())->setFileLogLevel(RSFSCLog::RSFSCLOG_LEVEL_DEBUG);
  data.clear();
  ProtoParser::pushToPayload(data, static_cast<uint8_t>(0xc3), check_sum);
  if (!writeCmd(_cmd_type, data, timeout))
  {
    LOG_INDEX_ERROR("发送结束升级命令失败", _cmd_type);
    return false;
  }

  return true;
}

bool LidarManager::firmwareUpdateApp(const QString& _file_path)
{
  if (!firmwareUpdate(mech::NET_CMD_FIRMWARE_UPDATE_APP, _file_path))
  {
    LOG_INDEX_ERROR("App升级失败, {}", _file_path);
    return false;
  }
  return true;
}
bool LidarManager::firmwareUpdateBot(const QString& _file_path)
{
  if (!firmwareUpdate(mech::NET_CMD_FIRMWARE_UPDATE_BOT, _file_path))
  {
    LOG_INDEX_ERROR("Bot升级失败, {}", _file_path);
    return false;
  }
  LOG_INDEX_INFO("Bot升级成功, {}", _file_path);
  return true;
}
bool LidarManager::firmwareUpdateTop(const QString& _file_path)
{
  if (!firmwareUpdate(mech::NET_CMD_FIRMWARE_UPDATE_TOP, _file_path))
  {
    LOG_INDEX_ERROR("Top升级失败, {}", _file_path);
    return false;
  }
  LOG_INDEX_INFO("Top升级成功, {}", _file_path);
  return true;
}
bool LidarManager::firmwareUpdateWriteConfig(const QString& _file_path)
{
  QFile file(_file_path);
  if (!file.open(QIODevice::ReadOnly | QIODevice::Text))
  {
    LOG_INDEX_ERROR("打开固化Config文件失败，err_msg: {}，文件路径：{}", file.errorString(), _file_path.toStdString());
    return false;
  }
  uint32_t time_out = 100000;

  std::vector<uint32_t> bot_reg_addr;
  std::vector<uint32_t> bot_reg_val;
  std::vector<uint32_t> top_reg_addr;
  std::vector<uint32_t> top_reg_val;
  std::vector<uint32_t> reg_459_addr;
  std::vector<uint32_t> reg_459_val;
  QTextStream inp(&file);
  QStringList lines;
  while (!inp.atEnd())
  {
    auto line = inp.readLine();
    if (!line.contains("bottom_regs=") && !line.contains("top_regs=") && !line.contains("459_regs="))
    {
      continue;
    }
    auto reg_info = line.split("=");
    if (reg_info.size() != 2)
    {
      LOG_INDEX_ERROR("解析{}失败", line.toStdString());
      return false;
    }
    auto reg_num = reg_info[1].toInt();
    auto& reg_addr =
      line.contains("bottom_regs=") ? bot_reg_addr : (line.contains("top_regs=") ? top_reg_addr : reg_459_addr);
    auto& reg_val =
      line.contains("bottom_regs=") ? bot_reg_val : (line.contains("top_regs=") ? top_reg_val : reg_459_val);
    auto split_size     = 4;
    auto reg_addr_index = 2;
    auto reg_val_index  = 3;
    if (reg_info.at(0) == "459_regs")
    {
      split_size     = 3;
      reg_addr_index = 1;
      reg_val_index  = 2;
    }

    while ((reg_num--) > 0 && !inp.atEnd())
    {
      auto reg_line = inp.readLine();
      auto reg_data = reg_line.split("_");
      if (reg_data.size() != split_size)
      {
        LOG_INDEX_ERROR("解析{}数据失败", line.toStdString());
        return false;
      }
      reg_addr.push_back(reg_data[reg_addr_index].toUInt(nullptr, 16));
      reg_val.push_back(reg_data[reg_val_index].toUInt(nullptr, 16));
    }
  }

  uint16_t pkt_count = 0;
  auto size          = bot_reg_val.size();
  for (size_t i = 0; (i < size) || (size == 0);)
  {
    std::vector<uint8_t> data;
    uint16_t count = (size - i) > 100 ? 100 : (size - i);
    ProtoParser::pushToPayload(data, static_cast<uint16_t>(size), pkt_count++, static_cast<uint16_t>(count));
    for (; i < size && count > 0; i++, count--)
    {
      ProtoParser::pushToPayload(data, bot_reg_addr[i], bot_reg_val[i]);
    }
    if (!writeCmd(mech::NET_CMD_OTHER_FIRM_BOT_WRITE, data, time_out))
    {
      LOG_INDEX_ERROR("写入固化Config数据失败");
      return false;
    }
    if (size == 0)
    {
      break;
    }
  }

  pkt_count = 0;
  size      = top_reg_val.size();
  for (size_t i = 0; (i < size) || (size == 0);)
  {
    std::vector<uint8_t> data;
    uint16_t count = (size > 0 && (size - i) > 100) ? 100 : (size - i);

    ProtoParser::pushToPayload(data, static_cast<uint16_t>(size), pkt_count++, count);

    for (; i < size && count > 0; i++, count--)
    {
      ProtoParser::pushToPayload(data, top_reg_addr[i], top_reg_val[i]);
    }

    if (!writeCmd(mech::NET_CMD_OTHER_FIRM_TOP_WRITE, data, time_out))
    {
      LOG_INDEX_ERROR("写入固化顶板Config数据失败");
      return false;
    }

    if (size == 0)
    {
      break;
    }
  }

  pkt_count = 0;
  size      = reg_459_val.size();
  for (size_t i = 0; (i < size) || (size == 0);)
  {
    std::vector<uint8_t> data;
    uint16_t count = (size - i) > 100 ? 100 : (size - i);

    ProtoParser::pushToPayload(data, static_cast<uint16_t>(size), pkt_count++, count);
    for (; i < size && count > 0; i++, count--)
    {
      ProtoParser::pushToPayload(data, reg_459_addr[i], reg_459_val[i]);
    }
    if (!writeCmd(mech::NET_CMD_OTHER_FIRM_459_WRITE, data, time_out))
    {
      LOG_INDEX_ERROR("写入固化459Config数据失败");
      return false;
    }
    if (size == 0)
    {
      break;
    }
  }

  LOG_INDEX_INFO("固化配置成功, 文件路径: {}", _file_path.toStdString());
  return true;
}

bool LidarManager::firmwareUpdateCheckConfig(const QString& _file_path, const QString& _read_val_file_path)
{
  QFile file(_file_path);
  if (!file.open(QIODevice::ReadOnly | QIODevice::Text))
  {
    LOG_INDEX_ERROR("打开固化Config文件失败，err_msg: {}，文件路径：{}", file.errorString(), _file_path.toStdString());
    return false;
  }
  // uint32_t time_out = 100000;

  std::vector<uint32_t> bot_reg_addr;
  std::vector<uint32_t> bot_reg_val;
  std::vector<uint32_t> top_reg_addr;
  std::vector<uint32_t> top_reg_val;
  std::vector<uint32_t> reg_459_addr;
  std::vector<uint32_t> reg_459_val;
  QTextStream inp(&file);
  QStringList lines;
  while (!inp.atEnd())
  {
    auto line = inp.readLine();
    if (!line.contains("bottom_regs=") && !line.contains("top_regs=") && !line.contains("459_regs="))
    {
      continue;
    }
    auto reg_info = line.split("=");
    if (reg_info.size() != 2)
    {
      LOG_INDEX_ERROR("解析{}失败", line.toStdString());
      return false;
    }
    auto reg_num = reg_info[1].toInt();
    auto& reg_addr =
      line.contains("bottom_regs=") ? bot_reg_addr : (line.contains("top_regs=") ? top_reg_addr : reg_459_addr);
    auto& reg_val =
      line.contains("bottom_regs=") ? bot_reg_val : (line.contains("top_regs=") ? top_reg_val : reg_459_val);
    auto split_size     = 4;
    auto reg_addr_index = 2;
    auto reg_val_index  = 3;
    if (reg_info.at(0) == "459_regs")
    {
      split_size     = 3;
      reg_addr_index = 1;
      reg_val_index  = 2;
    }

    while ((reg_num--) > 0 && !inp.atEnd())
    {
      auto reg_line = inp.readLine();
      auto reg_data = reg_line.split("_");
      if (reg_data.size() != split_size)
      {
        LOG_INDEX_ERROR("解析{}数据失败", line.toStdString());
        return false;
      }
      reg_addr.push_back(reg_data[reg_addr_index].toUInt(nullptr, 16));
      reg_val.push_back(reg_data[reg_val_index].toUInt(nullptr, 16));
    }
  }
  // 先读取底板后顶板最后459
  std::vector<uint32_t> bot_reg_addr_read;
  std::vector<uint32_t> bot_reg_val_read;
  std::vector<uint32_t> top_reg_addr_read;
  std::vector<uint32_t> top_reg_val_read;
  std::vector<uint32_t> reg_459_addr_read;
  std::vector<uint32_t> reg_459_val_read;

  if (!readConfigRegister(mech::NET_CMD_OTHER_FIRM_BOT_READ, bot_reg_addr_read, bot_reg_val_read))
  {
    LOG_INDEX_ERROR("读取底板配置失败");
    return false;
  }

  if (!readConfigRegister(mech::NET_CMD_OTHER_FIRM_TOP_READ, top_reg_addr_read, top_reg_val_read))
  {
    LOG_INDEX_ERROR("读取顶板配置失败");
    return false;
  }

  if (!readConfigRegister(mech::NET_CMD_OTHER_FIRM_459_READ, reg_459_addr_read, reg_459_val_read))
  {
    LOG_INDEX_ERROR("读取459配置失败");
    return false;
  }

  // 根据格式，写入到文件当中
  QFile read_val_file(_read_val_file_path);
  if (!read_val_file.open(QIODevice::WriteOnly | QIODevice::Text))
  {
    LOG_INDEX_ERROR("打开文件失败");
    return false;
  }

  QTextStream out(&read_val_file);
  out << "bottom_regs=" << bot_reg_addr_read.size() << "\n";
  for (size_t i = 0; i < bot_reg_addr_read.size(); ++i)
  {
    out << fmt::format("R_{:#x}_{:#x}\n", bot_reg_addr_read[i], bot_reg_val_read[i]).c_str();
  }
  out << "\n";
  out << "top_regs=" << top_reg_addr_read.size() << "\n";
  for (size_t i = 0; i < top_reg_addr_read.size(); ++i)
  {
    out << fmt::format("R_{:#x}_{:#x}\n", top_reg_addr_read[i], top_reg_val_read[i]).c_str();
  }
  out << "\n";
  out << "459_regs=" << reg_459_addr_read.size() << "\n";
  for (size_t i = 0; i < reg_459_addr_read.size(); ++i)
  {
    out << fmt::format("R_{:#x}_{:#x}\n", reg_459_addr_read[i], reg_459_val_read[i]).c_str();
  }
  read_val_file.close();

  if (bot_reg_addr_read.size() != bot_reg_addr.size() || top_reg_addr_read.size() != top_reg_addr.size() ||
      reg_459_addr_read.size() != reg_459_addr.size())
  {
    LOG_INDEX_ERROR("读取配置失败，配置长度不一致, bot[write:{} read:{}], top[write:{} read:{}], 459[write:{} read:{}]",
                    bot_reg_addr.size(), bot_reg_addr_read.size(), top_reg_addr.size(), top_reg_addr_read.size(),
                    reg_459_addr.size(), reg_459_addr_read.size());
    return false;
  }

  // 比较寄存器数值并记录到 verification 文件
  bool overall_verification_success = true;

  // 检查底板寄存器
  for (size_t i = 0; i < bot_reg_addr.size(); ++i)
  {
    bool is_match    = (bot_reg_val[i] == bot_reg_val_read[i]);
    QString reg_name = QString("bottom_reg_0x%1").arg(bot_reg_addr[i], 0, 16);

    // 记录校验结果到 verification 文件
    writeVerificationDataToFile(reg_name, bot_reg_addr[i], bot_reg_val[i], bot_reg_val_read[i], is_match);

    if (!is_match)
    {
      LOG_INDEX_ERROR("底板寄存器0x{:x}值不相等，文件中的值为:0x{:x}, 机器中的值为:0x{:x}", bot_reg_addr[i],
                      bot_reg_val[i], bot_reg_val_read[i]);
      overall_verification_success = false;
    }
  }

  // 检查顶板寄存器
  for (size_t i = 0; i < top_reg_addr.size(); ++i)
  {
    bool is_match    = (top_reg_val[i] == top_reg_val_read[i]);
    QString reg_name = QString("top_reg_0x%1").arg(top_reg_addr[i], 0, 16);

    // 记录校验结果到 verification 文件
    writeVerificationDataToFile(reg_name, top_reg_addr[i], top_reg_val[i], top_reg_val_read[i], is_match);

    if (!is_match)
    {
      LOG_INDEX_ERROR("顶板寄存器0x{:x}值不相等，文件中的值为:0x{:x}, 机器中的值为:0x{:x}", top_reg_addr[i],
                      top_reg_val[i], top_reg_val_read[i]);
      overall_verification_success = false;
    }
  }

  // 检查459寄存器
  for (size_t i = 0; i < reg_459_addr.size(); ++i)
  {
    bool is_match    = (reg_459_val[i] == reg_459_val_read[i]);
    QString reg_name = QString("reg_459_0x%1").arg(reg_459_addr[i], 0, 16);

    // 记录校验结果到 verification 文件
    writeVerificationDataToFile(reg_name, reg_459_addr[i], reg_459_val[i], reg_459_val_read[i], is_match);

    if (!is_match)
    {
      LOG_INDEX_ERROR("459寄存器0x{:x}值不相等，文件中的值为:0x{:x}, 机器中的值为:0x{:x}", reg_459_addr[i],
                      reg_459_val[i], reg_459_val_read[i]);
      overall_verification_success = false;
    }
  }

  // 如果有校验失败的情况，返回 false
  if (!overall_verification_success)
  {
    LOG_INDEX_ERROR("固件配置校验失败，存在寄存器值不匹配的情况");
    return false;
  }

  LOG_INDEX_INFO("配置文件检验OK, 机器中的配置值与文件中的配置相等");
  return true;
}

bool LidarManager::startEncodCalib(const int _timeout_secs)
{
  LOG_INDEX_INFO("码盘标定状态位: {:x}", config_para_.status_of_code_wheel_cali);
  uint8_t start_encode = 0;
  if (!writeCmd(mech::NET_CMD_MOTOR_CALIBRATION, start_encode, _timeout_secs * 1000))
  {
    LOG_INDEX_ERROR("等待电机稳定失败 {}s", _timeout_secs);
    if (getConfigParam(config_para_))
    {
      LOG_INDEX_ERROR("当前转速: {}rpm", config_para_.getMotorRealTimeSpeed());
    }
    return false;
  }
  if (!readConfigParamater(config_para_))
  {
    LOG_INDEX_ERROR("电机稳定后读取状态失败");
    return false;
  }

  auto read_value = config_para_.status_of_code_wheel_cali;
  if (!writeVerificationDataToFile("start_encode", mech::NET_CMD_MOTOR_CALIBRATION, start_encode, read_value,
                                   start_encode == read_value))
  {
    LOG_INDEX_ERROR("启动码盘标定时候，校验失败，写入{}与回读{}值不一致", start_encode, read_value);
    return false;
  }

  return true;
}
bool LidarManager::writeTopFlash(const QByteArray& _data, const uint32_t _addr_start)
{
  uint32_t data_size = _data.size();
  // 封装尝试开始写入的方法
  auto try_start_write = [&]() -> bool { return startWriteTopFlash(_addr_start, data_size, 8000); };

  // 确保成功开始写入，否则尝试结束并重启
  if (!try_start_write())
  {
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    if (!finishWriteTopFlash())
    {
      LOG_INDEX_ERROR("结束写入TopFlash失败");
      return false;
    }
    std::this_thread::sleep_for(std::chrono::milliseconds(1000));

    if (!try_start_write())
    {
      LOG_INDEX_ERROR("开始写入TopFlash失败");
      return false;
    }
  }

  std::this_thread::sleep_for(std::chrono::milliseconds(100));

  // 每帧1024字节发送数据
  uint32_t pkt_count = 1;
  uint32_t offset    = 0;
  while (offset < data_size)
  {
    if (is_abort_)
    {
      LOG_WARN("写入TopFlash被中断");
      return false;
    }

    // 读取一帧数据
    QByteArray raw_data = _data.mid(offset, 1024);
    offset += raw_data.size();
    std::vector<uint8_t> data(raw_data.begin(), raw_data.end());

    if (!MechTcp::writeTopFlash(pkt_count++, data))
    {
      LOG_INDEX_ERROR("写入TopFlash失败, pkt_count: {}", pkt_count);
      return false;
    }
  }

  // 确保完成写入过程
  if (!finishWriteTopFlash() && !finishWriteTopFlash(8000))
  {
    LOG_INDEX_ERROR("结束写入TopFlash失败");
    return false;
  }

  LOG_INDEX_INFO("写入TopFlash成功");
  return true;
}
bool LidarManager::stopEncodCalib()
{
  uint8_t write_value = 1;
  if (!writeCmd(mech::NET_CMD_MOTOR_CALIBRATION, write_value))
  {
    LOG_INDEX_ERROR("停止保存码盘标定失败");
    return false;
  }
  if (!readConfigParamater(config_para_))
  {
    LOG_INDEX_ERROR("码盘标定完成后读取状态失败");
    return false;
  }

  auto read_value = config_para_.status_of_code_wheel_cali;
  if (!writeVerificationDataToFile("save_encode", mech::NET_CMD_MOTOR_CALIBRATION, write_value, read_value,
                                   write_value == read_value))
  {
    LOG_INDEX_ERROR("保存码盘标定时候，校验失败，写入{}与回读{}值不一致", write_value, read_value);
    return false;
  }

  return true;
}
bool LidarManager::sendScaleAvgToMotor(const std::vector<uint32_t>& _scale_avg)
{
  uint8_t size_high            = static_cast<uint8_t>((_scale_avg.size() * 4) >> 8U);
  uint8_t size_low             = static_cast<uint8_t>((_scale_avg.size() * 4) & static_cast<uint16_t>(0xff));
  std::vector<uint8_t> payload = { 0xff, 0xaa, 0x2b, size_high, size_low };
  uint32_t crc                 = 0;

  for (const auto& val : _scale_avg)
  {
    payload.push_back(static_cast<uint8_t>(val >> 24U));
    payload.push_back(static_cast<uint8_t>(val >> 16U));
    payload.push_back(static_cast<uint8_t>(val >> 8U));
    payload.push_back(static_cast<uint8_t>(val));
  }

  for (size_t i = 2; i < payload.size(); ++i)
  {
    crc += payload[i];
  }

  crc &= 0xffffU;
  payload.push_back(static_cast<uint8_t>((crc & 0xffU) >> 8U));
  payload.push_back(static_cast<uint8_t>(crc & 0xffU));

  payload.push_back(0xaa);
  payload.push_back(0xff);

  if (!writeCmd(mech::NET_CMD_MOTOR_SEND_CMD, payload))
  {
    LOG_INDEX_ERROR("发送码盘标定数据失败");
    return false;
  }

  return true;
};

bool LidarManager::getEncodCalibData(std::vector<uint32_t>& _coeff_vec, std::vector<uint32_t>& _insert_step)
{
  if (!readConfigRegister(mech::NET_CMD_OTHER_ENCOD_CALIB_READ, _insert_step, _coeff_vec))
  {
    LOG_INDEX_ERROR("读取码盘标定数据失败");
    return false;
  }
  return true;
}

bool LidarManager::isLastDifopNotTimeout(const int _timeout_s)
{
  if (last_difop_time_.isNull())
  {
    LOG_INDEX_ERROR("未接收到difop包");
    return false;
  }
  QDateTime current_time = QDateTime::currentDateTime();
  auto time_to_last      = last_difop_time_.secsTo(current_time);
  if (time_to_last > _timeout_s)
  {
    LOG_INDEX_ERROR("已超过{}s未接收到新的difop包 (要求时间: <{}s)", time_to_last, _timeout_s);
    return false;
  }
  return true;
}
bool LidarManager::startTcpdumpBothOrgAndObjIPExcludeMSOP(const std::string& _dump_file_path)
{
  auto filter      = TcpdumpUtils::Filter();
  filter.file_size = "20";  // 限制抓包20MB
  filter.custom_filter =
    fmt::format("(not udp port {} and not udp port {} and not udp port 6688 and not icmp) and (host {} or host {})",
                getPort(), getLidarInfo()->getMSOPPort(), getIP(), getLidarInfo()->getIP());
  filter.network_interface = TcpdumpUtils::getNetworkInterFaceByIP("*************");
  return tcpdump_exclude_msop_.startTcpdump(_dump_file_path, filter);
}
bool LidarManager::startTcpdumpExcludeMSOP(const std::string& _dump_file_path)
{
  auto filter              = TcpdumpUtils::Filter();
  filter.file_size         = "20";  // 限制抓包20MB
  filter.custom_filter     = fmt::format("not (udp port {} or udp port 6688 or icmp)", getPort());
  filter.host_ip           = getIP();
  filter.network_interface = TcpdumpUtils::getNetworkInterFaceByIP("*************");
  return tcpdump_exclude_msop_.startTcpdump(_dump_file_path, filter);
}
void LidarManager::stopTcpdumpExcludeMSOP() { tcpdump_exclude_msop_.stopTcpdump(); }

bool LidarManager::startTcpdumpOnlyMSOP(const std::string& _dump_file_path)
{
  auto filter              = TcpdumpUtils::Filter();
  filter.file_size         = "20";  // 限制抓包10MB
  filter.custom_filter     = fmt::format("udp port {} or udp port {}", getPort(), config_para_.getDifopPort());
  filter.host_ip           = getIP();
  filter.network_interface = TcpdumpUtils::getNetworkInterFaceByIP("*************");
  return tcpdump_only_msop_.startTcpdump(_dump_file_path, filter);
}
void LidarManager::stopTcpdumpOnlyMSOP() { tcpdump_only_msop_.stopTcpdump(); }

bool LidarManager::writeVerificationDataToFile(const QString& _reg_name,
                                               uint32_t _address,
                                               uint32_t _write_value,
                                               uint32_t _read_value,
                                               bool _is_match)
{
  // 如果CSV校验文件路径为空，则不进行记录
  if (csv_verification_file_path_.isEmpty())
  {
    return true;
  }

  QFile verification_file(csv_verification_file_path_);
  if (!verification_file.open(QIODevice::WriteOnly | QIODevice::Append | QIODevice::Text))
  {
    LOG_INDEX_ERROR("无法打开CSV校验文件: {}", csv_verification_file_path_.toStdString());
    return false;
  }

  QTextStream stream(&verification_file);

  // 如果文件为空，写入表头
  if (verification_file.size() == 0)
  {
    stream << "Timestamp,Register_Name,Address,Write_Value,Read_Value,Match_Status\n";
  }

  // 使用 fmt 格式化数据记录
  QString timestamp        = QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss.zzz");
  std::string match_status = _is_match ? "PASS" : "FAIL";

  // 使用 fmt::format 构建 CSV 行
  std::string csv_line = fmt::format("{},{},0x{:x},0x{:x},0x{:x},{}\n", timestamp.toStdString(),
                                     _reg_name.toStdString(), _address, _write_value, _read_value, match_status);

  stream << QString::fromStdString(csv_line);
  verification_file.close();

  // LOG_INDEX_INFO("CSV校验记录: {} [0x{:x}] 写入:0x{:x} 读取:0x{:x} 状态:{}", _reg_name.toStdString(), _address,
  //                _write_value, _read_value, match_status);

  return true;
}

}  // namespace lidar
}  // namespace robosense