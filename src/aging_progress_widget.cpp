﻿/******************************************************************************
 * Copyright 2020 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#include "aging_progress_widget.h"
#include "app_event.h"
#include "data_struct.h"
#include "rsfsc_log/rsfsc_log_macro.h"
#include "work_model/aging_work_model.h"
#include "work_model/work_model_factory.h"
#include <QDesktopServices>
#include <QMetaType>
#include <lidar_ctl.h>
#include <memory>
#include <qlabel.h>
#include <qobject.h>
#include <rsfsc_msg.h>
#include <vector>

Q_DECLARE_METATYPE(robosense::lidar::AgingState);
// NOLINTNEXTLINE(modernize-concat-nested-namespaces)
namespace robosense
{
namespace lidar
{

AgingProgressWidget::AgingProgressWidget(const ParaInfo& _para_info, QWidget* _parent) :
  QWidget(_parent),
  label_index_(new QLabel(this)),
  label_sn_(new QLabel(this)),
  label_project_(new QLabel(this)),
  label_relay_state_(new QLabel(this)),
  label_run_state_(new QLabel(this)),
  button_firmware_update_(new QPushButton(this)),
  button_code_wheel_calib_(new QPushButton(this)),
  button_aging_state_(new QPushButton(this)),
  button_stress_state_(new QPushButton(this)),
  progress_aging_(new QProgressBar(this)),
  aging_timer_(new QTimer(this)),
  lidar_index_(_para_info.lidar_index),
  relay_state_(ConnectionStatus::UNINITIALIZED)
{
  qRegisterMetaType<AgingState>("AgingState");
  qRegisterMetaType<StressState>("StressState");
  qRegisterMetaType<FirmwareUpdateState>("FirmwareUpdateState");
  qRegisterMetaType<EncodeCalibState>("EncodeCalibState");
  qRegisterMetaType<RunState>("RunState");

  label_index_->setText(QString("序号 %1").arg(lidar_index_, 2, 10, QLatin1Char('0')));

  connect(button_aging_state_, &QPushButton::clicked, this, &AgingProgressWidget::slotShowAgingLog);

  QHBoxLayout* layout_index       = new QHBoxLayout;
  QHBoxLayout* layout_project     = new QHBoxLayout;
  QHBoxLayout* layout_ready_state = new QHBoxLayout;
  QHBoxLayout* layout_test_state  = new QHBoxLayout;

  label_relay_state_->setToolTip("继电器状态: 灰色-未初始化，绿色-闭合，红色-断开");
  label_relay_state_->setSizePolicy(QSizePolicy::Fixed, QSizePolicy::Fixed);

  label_run_state_->setMaximumHeight(30);
  label_run_state_->setAlignment(Qt::AlignCenter);
  label_run_state_->setContentsMargins(2, 0, 2, 0);

  int fixed_width = 17;
  pixmap_uninitialized_.load(":img/status_uninitialized.svg");
  pixmap_connected_.load(":img/status_connected.svg");
  pixmap_disconnected_.load(":img/status_disconnected.svg");
  pixmap_lagging_.load(":img/status_lagging.svg");
  pixmap_uninitialized_ = pixmap_uninitialized_.scaledToWidth(fixed_width, Qt::SmoothTransformation);
  pixmap_connected_     = pixmap_connected_.scaledToWidth(fixed_width, Qt::SmoothTransformation);
  pixmap_disconnected_  = pixmap_disconnected_.scaledToWidth(fixed_width, Qt::SmoothTransformation);
  pixmap_lagging_       = pixmap_lagging_.scaledToWidth(fixed_width, Qt::SmoothTransformation);

  layout_index->addWidget(label_index_);
  layout_index->addWidget(label_project_);
  layout_index->addWidget(label_relay_state_);
  layout_project->addWidget(label_sn_);
  layout_project->addSpacerItem(new QSpacerItem(20, 20, QSizePolicy::Expanding, QSizePolicy::Minimum));
  layout_project->addWidget(label_run_state_);

  layout_ready_state->addWidget(button_firmware_update_);
  layout_ready_state->addWidget(button_code_wheel_calib_);

  layout_test_state->addWidget(button_stress_state_);
  layout_test_state->addWidget(button_aging_state_);

  progress_aging_->setValue(0);
  label_index_->setSizePolicy(QSizePolicy::MinimumExpanding, QSizePolicy::Fixed);
  label_sn_->setSizePolicy(QSizePolicy::MinimumExpanding, QSizePolicy::Fixed);

  // button_state_->setSizePolicy(QSizePolicy::Fixed, QSizePolicy::Fixed);
  // button_stress_->setSizePolicy(QSizePolicy::Fixed, QSizePolicy::Fixed);

  QFrame* line = new QFrame(this);
  line->setFrameShape(QFrame::HLine);
  line->setFrameShadow(QFrame::Sunken);
  // 设置分隔线的大小策略，增加上下留白
  QWidget* bottom_spacer = new QWidget(this);
  bottom_spacer->setSizePolicy(QSizePolicy::Preferred, QSizePolicy::Expanding);
  bottom_spacer->setFixedHeight(3);

  QVBoxLayout* layout = new QVBoxLayout;
  layout->addLayout(layout_index);
  layout->addLayout(layout_project);

  layout->addWidget(bottom_spacer);
  layout->addWidget(line);
  layout->addWidget(bottom_spacer);

  layout->addLayout(layout_ready_state);
  layout->addLayout(layout_test_state);

  layout->addWidget(bottom_spacer);

  layout->addWidget(progress_aging_);

  this->setLayout(layout);

  running_widget_ = new RunningWidget(lidar_index_);
  connect(running_widget_, &RunningWidget::signalRunning, this, &AgingProgressWidget::slotStartAging);
  connect(aging_timer_, &QTimer::timeout, this, &AgingProgressWidget::slotAgingTimer);
  connect(app(), &AppEvent::signalUpdateRelayState, this, [this](const int _lidar_index, const bool _is_turn_on) {
    if (_lidar_index == getLidarIndex() - 1)
    {
      auto state = _is_turn_on ? ConnectionStatus::CONNECTED : ConnectionStatus::DISCONNECTED;
      setRelayState(state);
    }
  });

  dialog_aging_data_          = new QDialog(this);
  aging_data_model_           = new AgingDataModel(dialog_aging_data_);
  label_aging_pass_time_      = new QLabel(dialog_aging_data_);
  QTableView* aging_data_view = new QTableView(dialog_aging_data_);
  aging_data_view->setModel(aging_data_model_);
  QVBoxLayout* layout_aging_data = new QVBoxLayout;
  layout_aging_data->addWidget(label_aging_pass_time_);
  layout_aging_data->addWidget(aging_data_view);
  dialog_aging_data_->setLayout(layout_aging_data);
  dialog_aging_data_->hide();

  resetState();
  setParaInfo(_para_info);
}

AgingProgressWidget::~AgingProgressWidget()
{
  quit();
  delete running_widget_;
}

void AgingProgressWidget::slotExit()
{
  if (fsm_ptr_ != nullptr)
  {
    if (auto curr_state_handler = fsm_ptr_->getCurrStateHandler(); curr_state_handler != nullptr)
    {
      curr_state_handler->abort();
    }
  }
}

void AgingProgressWidget::slotAgingTimerStop()
{
  if (aging_timer_->isActive())
  {
    aging_timer_->stop();
  }
}

void AgingProgressWidget::setLidarProject(QString _project)
{
  label_project_->setText(QString("项目 %1").arg(_project));
}

void AgingProgressWidget::setAgingProgress(int _counts)
{
  int rest_counts = total_time_secs_ - _counts;
  progress_aging_->setFormat(secsToTime(rest_counts));
  progress_aging_->setValue(_counts);
}

// void AgingProgressWidget::setAgingProtocolType(ProtocolType _type)
// {
//   protocol_type_ = _type;
//   running_widget_->setProtocolType(_type);
// }

void AgingProgressWidget::quit()
{
  if (fsm_ptr_ == nullptr)
  {
    return;
  }
  if (fsm_ptr_->currentState() != ActionState::END)
  {
    fsm_ptr_->quit();
  }
}

void AgingProgressWidget::setParaInfo(const ParaInfo& _para_info)
{
  para_info_                      = _para_info;
  para_info_.fsm_change_ip        = running_widget_->checkbox_fsm_change_ip_->isChecked();
  para_info_.fsm_encoding_calib   = running_widget_->checkbox_fsm_encoding_calib_->isChecked();
  para_info_.fsm_chn_angle_write  = running_widget_->checkbox_fsm_chn_angle_write_->isChecked();
  para_info_.fsm_clear_calib_data = running_widget_->checkbox_fsm_clear_calib_data_->isChecked();
  para_info_.fsm_vbd_calib        = running_widget_->checkbox_fsm_vbd_calib_->isChecked();
  para_info_.fsm_firmware_update  = running_widget_->checkbox_fsm_firmware_update_->isChecked();
  para_info_.fsm_stress_test      = running_widget_->checkbox_fsm_stress_test_->isChecked();
  para_info_.fsm_aging            = running_widget_->checkbox_fsm_aging_->isChecked();
  if (para_info_.aging_time_secs < para_info_.cooling_time_secs)
  {
    para_info_.cooling_time_secs = 0;
  }
  if (work_model_ptr_ != nullptr)
  {
    work_model_ptr_->setParaInfo(para_info_);
  }
  initProgress();
}

void AgingProgressWidget::setRelayState(ConnectionStatus _state)
{
  relay_state_ = _state;
  updateRelayState();
}

void AgingProgressWidget::updateRelayState()
{
  switch (relay_state_)
  {
  case ConnectionStatus::CONNECTED: label_relay_state_->setPixmap(pixmap_connected_); break;
  case ConnectionStatus::DISCONNECTED: label_relay_state_->setPixmap(pixmap_disconnected_); break;
  case ConnectionStatus::LAGGING: label_relay_state_->setPixmap(pixmap_lagging_); break;
  case ConnectionStatus::UNINITIALIZED:
  default: label_relay_state_->setPixmap(pixmap_uninitialized_);
  }
}

void AgingProgressWidget::resetState()
{
  if (work_model_ptr_)
  {
    if (AGING_BUSY == work_model_ptr_->getAgingState() || STRESS_BUSY == work_model_ptr_->getStressState())
    {
      QMessageBox::warning(this, "警告", "请先停止老化", QMessageBox::Yes, QMessageBox::Yes);
      return;
    }
  }

  updateRelayState();
  slotSwitchFirmwareUpdateState(FIRMWARE_UPDATE_IDLE);
  slotSwitchCodeWheelCalibState(ENCODE_CALIB_IDLE);
  slotSwitchRunState(RUN_IDLE);
  slotSwitchAgingState(AGING_IDLE);
  slotSwitchStressState(STRESS_IDLE);
  setAgingProgress(0);
  // TODO: 三色灯
  // TricolorLightController::getInstance()->setAgingState(lidar_index_, AGING_IDLE);
  setLidarProject("");
}

int AgingProgressWidget::getLidarIndex() const { return lidar_index_; }

void AgingProgressWidget::setOperateEnabled(bool _enabled)
{
  if (running_widget_ == nullptr)
  {
    return;
  }
  running_widget_->setOperateEnabled(_enabled);
}

void AgingProgressWidget::slotSwitchFirmwareUpdateState(const FirmwareUpdateState _state)
{
  QString state_color;
  QString state_description;

  //   FIRMWARE_UPDATE_UNZIP,
  // FIRMWARE_UPDATE_APP,
  // FIRMWARE_UPDATE_BOT,
  // FIRMWARE_UPDATE_TOP,
  // FIRMWARE_UPDATE_WRITE_CONFIG,

  switch (_state)
  {
  case FIRMWARE_UPDATE_IDLE:
    button_firmware_update_->setEnabled(false);
    state_color       = "background-color:rgb(135,206,235)";
    state_description = "升级空闲";
    break;
  case FIRMWARE_UPDATE_WAIT:
    state_color = "background-color:rgb(255,255,0)";
    button_firmware_update_->setEnabled(true);
    state_description = "等待升级";
    break;
  case FIRMWARE_UPDATE_BUSY:
    state_color       = "background-color:rgb(255,255,0)";
    state_description = "升级中";
    break;
  case FIRMWARE_UPDATE_UNZIP:
    state_color       = "background-color:rgb(255,255,0)";
    state_description = "解压中";
    break;
  case FIRMWARE_UPDATE_APP:
    state_color       = "background-color:rgb(255,255,0)";
    state_description = "APP升级";
    break;
  case FIRMWARE_UPDATE_BOT:
    state_color       = "background-color:rgb(255,255,0)";
    state_description = "底板升级";
    break;
  case FIRMWARE_UPDATE_TOP:
    state_color       = "background-color:rgb(255,255,0)";
    state_description = "顶板升级";
    break;
  case FIRMWARE_UPDATE_WRITE_CONFIG:
    state_color       = "background-color:rgb(255,255,0)";
    state_description = "写配置中";
    break;
  case FIRMWARE_UPDATE_REBOOT:
    state_color       = "background-color:rgb(255,255,0)";
    state_description = "重启中";
    break;
  case FIRMWARE_UPDATE_CHECK:
    state_color       = "background-color:rgb(255,255,0)";
    state_description = "校验中";
    break;
  case FIRMWARE_UPDATE_PASS:
    state_color       = "background-color:rgb(0,255,0)";
    state_description = "升级通过";
    break;
  case FIRMWARE_UPDATE_NG:
    state_color       = "background-color:rgb(255,0,0)";
    state_description = "升级失败";
    break;
  case FIRMWARE_UPDATE_SKIP:
    state_color       = "background-color:rgb(200,200,200)";
    state_description = "升级跳过";
    break;
  case FIRMWARE_UPDATE_ABORT:
    state_color       = "background-color:rgb(255,69,0)";
    state_description = "升级中断";
    break;
  }
  button_firmware_update_->setStyleSheet(state_color);
  button_firmware_update_->setText(state_description);
}
void AgingProgressWidget::slotSwitchCodeWheelCalibState(const EncodeCalibState _state)
{
  QString state_color;
  QString state_description;

  switch (_state)
  {
  case ENCODE_CALIB_IDLE:
    button_code_wheel_calib_->setEnabled(false);
    state_color       = "background-color:rgb(135,206,235)";
    state_description = "码盘空闲";
    break;
  case ENCODE_CALIB_WAIT:
    button_code_wheel_calib_->setEnabled(true);
    state_color       = "background-color:rgb(255,255,0)";
    state_description = "等待码盘";
    break;
  case ENCODE_CALIB_BUSY:
    button_code_wheel_calib_->setEnabled(true);
    state_color       = "background-color:rgb(255,255,0)";
    state_description = "码标中";
    break;
  case ENCODE_CALIB_COLLECT:
    button_code_wheel_calib_->setEnabled(true);
    state_color       = "background-color:rgb(255,255,0)";
    state_description = "码盘采集";
    break;
  case ENCODE_CALIB_PASS:
    button_code_wheel_calib_->setEnabled(true);
    state_color       = "background-color:rgb(0,255,0)";
    state_description = "码盘通过";
    break;
  case ENCODE_CALIB_NG:
    button_code_wheel_calib_->setEnabled(true);
    state_color       = "background-color:rgb(255,0,0)";
    state_description = "码盘失败";
    break;
  case ENCODE_CALIB_SKIP:
    button_code_wheel_calib_->setEnabled(true);
    state_color       = "background-color:rgb(200,200,200)";
    state_description = "码盘跳过";
    break;
  case ENCODE_CALIB_ABORT:
    button_code_wheel_calib_->setEnabled(true);
    state_color       = "background-color:rgb(255,69,0)";
    state_description = "码盘中断";
    break;
  }
  button_code_wheel_calib_->setStyleSheet(state_color);
  button_code_wheel_calib_->setText(state_description);
}

void AgingProgressWidget::slotSwitchAgingState(const AgingState _state)
{
  QString state_color;
  QString state_description;

  switch (_state)
  {
  case AGING_IDLE:
    button_aging_state_->setEnabled(false);
    state_color       = "background-color:rgb(135,206,235)";
    state_description = "老化空闲";
    break;
  case AGING_WAIT:
    button_aging_state_->setEnabled(true);
    state_color       = "background-color:rgb(255,255,0)";
    state_description = "等待老化";
    break;
  case AGING_BUSY:
    button_aging_state_->setEnabled(true);
    state_color       = "background-color:rgb(255,255,0)";
    state_description = "老化中";
    break;
  case AGING_COOLING:
    button_aging_state_->setEnabled(true);
    state_color       = "background-color:rgb(0,116,217)";
    state_description = "冷却中";
    break;
  case AGING_PASS:
    button_aging_state_->setEnabled(true);
    state_color       = "background-color:rgb(0,255,0)";
    state_description = "老化通过";
    break;
  case AGING_SKIP:
    button_aging_state_->setEnabled(true);
    state_color       = "background-color:rgb(200,200,200)";
    state_description = "老化跳过";
    break;
  case AGING_NG:
    button_aging_state_->setEnabled(true);
    state_color       = "background-color:rgb(255,0,0)";
    state_description = "老化失败";
    break;
  case AGING_ABORT:
    button_aging_state_->setEnabled(true);
    state_color       = "background-color:rgb(255,69,0)";
    state_description = "老化中断";
    break;
  }
  button_aging_state_->setStyleSheet(state_color);
  button_aging_state_->setText(state_description);

  // signalAgingState(lidar_index_, _state);
  // TODO: 查看更新aging state会触发什么
}

void AgingProgressWidget::slotSwitchStressState(const StressState _state)
{
  QString stress_color;
  QString stress_description;

  switch (_state)
  {
  case STRESS_IDLE:
    button_stress_state_->setEnabled(false);
    stress_color       = "background-color:rgb(135,206,235)";
    stress_description = "压测空闲";
    break;
  case STRESS_WAIT:
    button_stress_state_->setEnabled(true);
    stress_color       = "background-color:rgb(255,255,0)";
    stress_description = "等待压测";
    break;
  case STRESS_BUSY:
    button_stress_state_->setEnabled(true);
    stress_color       = "background-color:rgb(255,255,0)";
    stress_description = "压测中";
    break;
  case STRESS_PASS:
    button_stress_state_->setEnabled(true);
    stress_color       = "background-color:rgb(0,255,0)";
    stress_description = "压测通过";
    break;

  case STRESS_NG:
    button_stress_state_->setEnabled(true);
    stress_color       = "background-color:rgb(255,0,0)";
    stress_description = "压测失败";
    break;

  case STRESS_ABORT:
    button_stress_state_->setEnabled(true);
    stress_color       = "background-color:rgb(255,69,0)";
    stress_description = "压测中断";
    break;
  case STRESS_SKIP:
    button_stress_state_->setEnabled(true);
    stress_color       = "background-color:rgb(200,200,200)";
    stress_description = "压测跳过";
    break;
  }
  button_stress_state_->setStyleSheet(stress_color);
  button_stress_state_->setText(stress_description);
}

void AgingProgressWidget::slotSwitchRunState(const RunState _state)
{
  QString color;
  QString description;
  QFont font;
  font.setPointSize(10);

  switch (_state)
  {
  case RUN_BUSY:
    color       = "background-color:#fdfd5f; color:black";
    description = "运行中";
    break;
  case RUN_PASS:
    color       = "background-color:#45BF55; color:black";
    description = "通过";
    break;
  case RUN_NG:
    color       = "background-color:#fb6969; color:black";
    description = "失败";
    break;
  case RUN_ABORT:
    color       = "background-color:#fe9c78; color:black";
    description = "中断";
    break;
  case RUN_IDLE:
    color       = "background-color:#87ceeb; color:white";
    description = "空闲";
    break;
  }

  label_run_state_->setStyleSheet("border-radius: 5px;" + color);
  label_run_state_->setText(description);
  label_run_state_->setFont(font);
}

void AgingProgressWidget::slotUpdateStressCount(int _count)
{
  stress_counts_ = _count;
  button_stress_state_->setText(QString("通过%1次").arg(QString::number(stress_counts_)));
  button_stress_state_->setStyleSheet("background-color:rgb(255,255,0)");
}

void AgingProgressWidget::slotStartStressTest() {}

void AgingProgressWidget::slotShowAgingLog()
{
  QString lidar_sn = label_sn_->text();
  if (work_model_ptr_ == nullptr)
  {
    return;
  }
  QString path = work_model_ptr_->getPath().result_dir.absolutePath();
  if (path.isEmpty())
  {
    LOG_ERROR("无法生成文件路径,找不到对应的数据文件!");
    return;
  }
  // QDesktopServices::openUrl(QUrl(path));
  QProcess::startDetached("xdg-open", QStringList() << path);
}

void AgingProgressWidget::slotShowLidarInfoWidget()
{
  bool is_running = false;
  if ((fsm_ptr_ != nullptr) && fsm_ptr_->currentState() != ActionState::END)
  {
    is_running = true;
  }
  running_widget_->showWidget(is_running);
}
void AgingProgressWidget::slotShowAgingData() { dialog_aging_data_->show(); }

void AgingProgressWidget::slotStartAging(bool _is_start)
{
  running_widget_->hide();

  if (!_is_start)
  {
    if (fsm_ptr_->currentState() == ActionState::RESTORE_LIDAR_IP)
    {
      LOG_WARN("再次触发老化停止按钮,终止IP的恢复...");
      fsm_ptr_->abort();
      work_model_ptr_->notifyDataCheck();
    }
    else
    {
      LOG_WARN("触发老化停止按钮,老化流程停止中...");
      fsm_ptr_->changeState(ActionState::RESTORE_LIDAR_IP);
      fsm_ptr_->abort();
      work_model_ptr_->notifyDataCheck();
    }
    return;
  }

  if (app()->getChangingIpIndex() > 0)
  {
    QString msg = "<font color='red'>序号%1正在修改IP，请稍后再试</font>";
    QMessageBox::warning(this, "警告", msg.arg(QString::number(app()->getChangingIpIndex())), QMessageBox::Yes,
                         QMessageBox::Yes);
    return;
  }

  initFsm();
  QString lidar_sn = work_model_ptr_->getLidarManager()->getLidarInfoSn();
  if (aging_sn_set_ptr_ != nullptr && aging_sn_set_ptr_->find(lidar_sn) != aging_sn_set_ptr_->end())
  {
    QMessageBox::warning(this, "警告", "该SN已经在老化中", QMessageBox::Yes, QMessageBox::Yes);
    return;
  }
  label_sn_->setText(lidar_sn);
  setLidarProject(running_widget_->getLidarProject());

  work_model_ptr_->getLidarManager()->setReadDifopCallBack([this](const DifopPacket& _difop_pkt) {
    aging_data_pass_time_ = 0;
    // LOG_INFO("read difop");
    if (_difop_pkt.pkt_head != DIFOP_PKT_HEAD)
    {
      return;
    }

    aging_data_model_->updateModel(_difop_pkt);
  });

  initProgress();
  aging_timer_->start(1000);
}

void AgingProgressWidget::slotAgingTimer()
{
  ++aging_data_pass_time_;
  if (++curr_time_secs_ >= total_time_secs_)
  {
    aging_timer_->stop();
  }
  if (!work_model_ptr_->getAgingFinish() && (curr_time_secs_ > aging_time_secs_))
  {
    work_model_ptr_->notifyDataCheck();
    work_model_ptr_->setAgingFinish(true);
  }
  if (++last_time_check_secs_ > para_info_.aging_check_interval)
  {
    last_time_check_secs_ = 0;
    work_model_ptr_->notifyDataCheck();
  }

  setAgingProgress(curr_time_secs_);
}

QString AgingProgressWidget::secsToTime(int _secs)
{
  int hour    = _secs / 3600;
  int minute  = (_secs % 3600) / 60;
  int seconds = (_secs % 3600) % 60;

  return QString("剩余时间 %1:%2:%3")
    .arg(hour, 2, 10, QLatin1Char('0'))
    .arg(minute, 2, 10, QLatin1Char('0'))
    .arg(seconds, 2, 10, QLatin1Char('0'));
}
void AgingProgressWidget::initProgress()
{
  auto change_lidar_ip_time_secs = 60;
  auto firmware_update_time_secs = 300;
  auto encode_calib_time_secs    = 300;

  total_time_secs_      = 0;
  curr_time_secs_       = 0;
  stress_counts_        = 0;
  last_time_check_secs_ = para_info_.aging_check_interval;
  stress_time_secs_     = para_info_.stress_num * para_info_.stress_wait_start_time;

  if (para_info_.fsm_change_ip)
  {
    total_time_secs_ += change_lidar_ip_time_secs;
  }
  if (para_info_.fsm_firmware_update)
  {
    total_time_secs_ += firmware_update_time_secs;
  }
  if (para_info_.fsm_encoding_calib)
  {
    total_time_secs_ += encode_calib_time_secs;
  }
  if (para_info_.fsm_stress_test)
  {
    total_time_secs_ += stress_time_secs_;
  }

  if (para_info_.fsm_aging)
  {
    if (total_time_secs_ < para_info_.aging_time_secs)
    {
      total_time_secs_ = para_info_.aging_time_secs;
      aging_time_secs_ = para_info_.aging_time_secs - para_info_.cooling_time_secs;
    }
    else
    {
      aging_time_secs_ = para_info_.aging_time_secs;
      total_time_secs_ += para_info_.aging_time_secs;
    }
  }

  LOG_INDEX_INFO("老化总时间: {}s, aging_time_secs_: {}", total_time_secs_, aging_time_secs_);
  progress_aging_->setRange(0, total_time_secs_);
  setAgingProgress(curr_time_secs_);
}
void AgingProgressWidget::initFsm()
{
  para_info_                      = app()->getParaInfo();
  para_info_.fsm_change_ip        = running_widget_->checkbox_fsm_change_ip_->isChecked();
  para_info_.fsm_firmware_update  = running_widget_->checkbox_fsm_firmware_update_->isChecked();
  para_info_.fsm_encoding_calib   = running_widget_->checkbox_fsm_encoding_calib_->isChecked();
  para_info_.fsm_chn_angle_write  = running_widget_->checkbox_fsm_chn_angle_write_->isChecked();
  para_info_.fsm_clear_calib_data = running_widget_->checkbox_fsm_clear_calib_data_->isChecked();
  para_info_.fsm_vbd_calib        = running_widget_->checkbox_fsm_vbd_calib_->isChecked();
  para_info_.fsm_stress_test      = running_widget_->checkbox_fsm_stress_test_->isChecked();
  para_info_.fsm_aging            = running_widget_->checkbox_fsm_aging_->isChecked();

  rsfsc_lib::ProjectCode project_code =
    static_cast<rsfsc_lib::ProjectCode>(running_widget_->project_code_combo_box_->currentData().toInt());
  running_widget_->getLidarInfoWidgetPtr()->setProjectCode(project_code);

  work_model_ptr_ = WorkModelFactory::createWorkModel(running_widget_->getLidarInfoWidgetPtr());
  work_model_ptr_->setUpdateStateCb(
    [this](const AgingState _state) {
      QMetaObject::invokeMethod(this, "slotSwitchAgingState", Qt::QueuedConnection, Q_ARG(AgingState, _state));
    },
    [this](const StressState _state) {
      QMetaObject::invokeMethod(this, "slotSwitchStressState", Qt::QueuedConnection, Q_ARG(StressState, _state));
    },
    [this](const FirmwareUpdateState _state) {
      QMetaObject::invokeMethod(this, "slotSwitchFirmwareUpdateState", Qt::QueuedConnection,
                                Q_ARG(FirmwareUpdateState, _state));
    },
    [this](const EncodeCalibState _state) {
      QMetaObject::invokeMethod(this, "slotSwitchCodeWheelCalibState", Qt::QueuedConnection,
                                Q_ARG(EncodeCalibState, _state));
    },
    [this](const RunState _state) {
      QMetaObject::invokeMethod(this, "slotSwitchRunState", Qt::QueuedConnection, Q_ARG(RunState, _state));
    });
  work_model_ptr_->setParaInfo(para_info_);
  fsm_ptr_ = std::make_shared<FiniteStateMachine<AgingWorkModel>>();
  fsm_ptr_->setIndex(getLidarIndex());
  fsm_ptr_->registerHandler(std::make_shared<CheckMes>());

  // 注册固件升级流程
  fsm_ptr_->registerHandler(std::make_shared<FirmwareUpdateUnzip>());
  fsm_ptr_->registerHandler(std::make_shared<FirmwareUpdateApp>());
  fsm_ptr_->registerHandler(std::make_shared<FirmwareUpdateBot>());
  fsm_ptr_->registerHandler(std::make_shared<FirmwareUpdateTop>());
  fsm_ptr_->registerHandler(std::make_shared<FirmwareUpdateWriteConfig>());
  fsm_ptr_->registerHandler(std::make_shared<FirmwareUpdateReboot>());
  fsm_ptr_->registerHandler(std::make_shared<FirmwareUpdateCheck>());

  // 注册清除标定数据流程
  fsm_ptr_->registerHandler(std::make_shared<ClearCalibData>());

  // 注册码盘标定流程
  fsm_ptr_->registerHandler(std::make_shared<EncodingMotorStable>());
  fsm_ptr_->registerHandler(std::make_shared<EncodingCollect>());
  fsm_ptr_->registerHandler(std::make_shared<EncodingProcessAndWrite>());

  // 注册垂直角度写入流程
  fsm_ptr_->registerHandler(std::make_shared<ChnAngleWrite>());
  fsm_ptr_->registerHandler(std::make_shared<VbdCalib>());

  // 注册老化压测流程
  fsm_ptr_->registerHandler(std::make_shared<ConnectLidar>());
  fsm_ptr_->registerHandler(std::make_shared<ChangeLidarIp>());
  fsm_ptr_->registerHandler(std::make_shared<InitLidar>());
  fsm_ptr_->registerHandler(std::make_shared<InitReadAgingData>());
  fsm_ptr_->registerHandler(std::make_shared<ReadAgingData>());
  fsm_ptr_->registerHandler(std::make_shared<DeInitReadAgingData>());
  fsm_ptr_->registerHandler(std::make_shared<StressTest>());
  fsm_ptr_->registerHandler(std::make_shared<AgingCooling>());
  fsm_ptr_->registerHandler(std::make_shared<RestoreLidarIp>());

  fsm_ptr_->registerHandler(std::make_shared<SuccessHandler>());
  fsm_ptr_->registerHandler(std::make_shared<FailHandler>());
  fsm_ptr_->registerHandler(std::make_shared<AbortHandler>());
  fsm_ptr_->registerHandler(std::make_shared<FinalHandler>());
  fsm_ptr_->setWorkModelVec({ work_model_ptr_ });

  fsm_ptr_->setStateEndCallback([this]() {
    if (work_model_ptr_->getAgingState() == AGING_NG || work_model_ptr_->getAgingState() == AGING_ABORT ||
        work_model_ptr_->getStressState() == STRESS_NG || work_model_ptr_->getStressState() == STRESS_ABORT ||
        work_model_ptr_->getFirmwareUpdateState() == FIRMWARE_UPDATE_NG ||
        work_model_ptr_->getFirmwareUpdateState() == FIRMWARE_UPDATE_ABORT ||
        work_model_ptr_->getEncodeCalibState() == ENCODE_CALIB_NG ||
        work_model_ptr_->getEncodeCalibState() == ENCODE_CALIB_ABORT)
    {
      QMetaObject::invokeMethod(this, "slotAgingTimerStop", Qt::QueuedConnection);
    }

    work_model_ptr_->getLidarManager()->stopMonitorDifop();
  });
  fsm_ptr_->startup(ActionState::CHECK_MES);
}

}  // namespace lidar

}  // namespace robosense
