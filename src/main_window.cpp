﻿/******************************************************************************
 * Copyright 2020 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#include "main_window.h"
#include "aging_progress_widget.h"
#include "app_event.h"
#include "config.h"
#include "label_test_state.h"
#include "relay_controller.h"
#include "rsfsc_log/rsfsc_log_macro.h"
#include "widget_about.h"
#include "widget_log_setting.h"
#include <QDesktopWidget>
#include <QMessageBox>
#include <QProgressDialog>
#include <QSerialPortInfo>
#include <QtWidgets>
#include <cstddef>
#include <iostream>
#include <qnamespace.h>
#include <qobject.h>
#include <string>
#include <unistd.h>

namespace robosense
{
namespace lidar
{
using namespace Qt;

MainWindow::MainWindow(int _argc, char** _argv, QWidget* _parent) : QMainWindow(_parent)
{
  qRegisterMetaType<ErrorT>("ErrorT");
  qRegisterMetaType<ErrorT>("ErrorT");
  qRegisterMetaType<uint32_t>("uint32_t");
  qRegisterMetaType<uint16_t>("uint16_t");
  TcpdumpUtils::killAllTcpdump();
  registerAgingPara();

  setupLayout();

  slotUpdateAllWidgetState();
  readSettings();

  relayPortInit();

  loadCsvAndAppConfig();

  app()->setMainWindow(this);
  slotShowMESWidget();
}

MainWindow::~MainWindow()
{
  cleanBeforeQuit();
  delete para_setting_;
}

void MainWindow::setupLayout()
{
  QString version_str = QString::fromUtf8(FRAMEWORK_TEMPLATE_VERSION_STR);
  QString version     = QString("%1.%2.%3")
                      .arg(FRAMEWORK_TEMPLATE_VERSION_MAJOR)
                      .arg(FRAMEWORK_TEMPLATE_VERSION_MINOR)
                      .arg(FRAMEWORK_TEMPLATE_VERSION_PATCH);
  QString compile_datetime_str = QString::fromUtf8(PROJECT_COMPILE_DATETIME);
  if (std::string(CMAKE_BUILD_TYPE) == "Debug")
  {
    version_str += QString::fromUtf8(" Debug");
  }
  app()->setVersionStr(version_str);
  setWindowTitle(QString::fromUtf8("MECH老化测试软件 v") + version_str);
  setWindowIcon(QIcon(":/img/icon.png"));
  // admin layout

  QMenu* menu_setting = new QMenu("设置", this);

  QAction* action_cali_setting = menu_setting->addAction("老化参数设置");
  QObject::connect(action_cali_setting, &QAction::triggered, this, &MainWindow::slotShowSetting);

  QAction* action_log_setting = menu_setting->addAction("MES参数设置");
  QObject::connect(action_log_setting, &QAction::triggered, this, &MainWindow::slotShowMESWidget);

  QObject::connect(AppEvent::getInstance(), &AppEvent::signalShowErrorMessageBox, this,
                   &MainWindow::slotShowMessageBox);
  QObject::connect(app(), &AppEvent::signalUpdateParaInfo, this, &MainWindow::slotUpdateParaInfo);

  mes_widget_ = MesWidget::getInst();
  app()->setMesWidget(mes_widget_);

  QObject::connect(mes_widget_->getWidgetLogSettingPtr(), &rsfsc_lib::WidgetLogSetting::signalAuthorityUpdate, this,
                   &MainWindow::slotUpdateAllWidgetState);

  connect(app(), &AppEvent::signalLidarChangeIpIsReady, this, &MainWindow::slotLidarChangeIpIsReady);

  connect(app(), &AppEvent::signalFsmStarted, this, &MainWindow::slotFsmStarted);
  connect(app(), &AppEvent::signalFsmStopped, this, &MainWindow::slotFsmStopped);

  QMenu* menu_help      = new QMenu("帮助", this);
  QAction* action_about = menu_help->addAction("关于");
  widget_about_         = new WidgetAbout(this);
  widget_about_->setName(QString::fromUtf8("MECH老化测试软件"));
  widget_about_->setBrief(QString::fromUtf8("此软件用于RS雷达老化测试"));
  widget_about_->setVersionStr(QString("v") + version_str);
  widget_about_->setBuildTime(QString::fromUtf8(PROJECT_COMPILE_TIME));
  widget_about_->setBuildCommit(QString::fromUtf8(PROJECT_COMPILE_COMMIT));
  widget_about_->setYearStartCopyright(2023);
  widget_about_->setContactEmail("<EMAIL>");
  QObject::connect(action_about, &QAction::triggered, this, &MainWindow::slotShowAbout);

  this->menuBar()->addMenu(menu_setting);
  this->menuBar()->addMenu(menu_help);

  QVBoxLayout* layout_control = new QVBoxLayout;
  QHBoxLayout* layout_heard   = new QHBoxLayout;
  QLabel* label_logo          = new QLabel(this);
  label_logo->setPixmap(QPixmap(":/img/logo.png"));
  label_logo->setFixedHeight(60);
  layout_heard->addWidget(label_logo);
#ifdef CURRENT_DATE_TIME
  version_label_ = new QLabel(QString("<h2>Airy老化软件 v%1<br>").arg(version) + "编译时间: " + CURRENT_DATE_TIME +
                              QString::fromUtf8("</h2>"));
#else   // CURRENT_DATE_TIME
  version_label_ = new QLabel(QString("<h2>Airy老化软件 v%1<br>").arg(version) + "编译时间: " + compile_datetime_str +
                              QString::fromUtf8("</h2>"));
#endif  // CURRENT_DATE_TIME
  QFont font;
  font.setWeight(QFont::Bold);
  version_label_->setFont(font);
  version_label_->setAlignment(Qt::AlignCenter);
  layout_heard->addWidget(version_label_, 3, Qt::AlignHCenter);

  int aging_row = 0;
  int aging_col = 0;
  para_setting_->getPara("老化参数", "shelf_row", aging_row);
  para_setting_->getPara("老化参数", "shelf_col", aging_col);
  aging_num_   = aging_row * aging_col;
  aging_count_ = new QLabel(QString("正在老化台数： 0 | 剩余空位： %1").arg(aging_num_), this);
  aging_count_->setFont(font);
  aging_count_->setAlignment(Qt::AlignRight);
  aging_count_->setMaximumHeight(30);
  layout_heard->addWidget(aging_count_, 1, Qt::AlignRight);

  layout_control->addLayout(layout_heard);

  agingListWidgetInit();
  layout_control->addWidget(aging_list_widget_);

  layout_control->setContentsMargins(3, 0, 0, 0);

  browser_message_ = new rsfsc_lib::MessageBrowser(PROJECT_NAME,
                                                   this);  // NOTE must new first cause it define many show event
  RSFSCLog::getInstance()->setQtLogWidget(browser_message_, "slotShowMessage");

  QHBoxLayout* layout_main = new QHBoxLayout;
  layout_main->addLayout(layout_control, 4);
  layout_main->addWidget(browser_message_, 1);

  QWidget* widget_main = new QWidget;
  widget_main->setLayout(layout_main);
  this->setCentralWidget(widget_main);

  user_authority_ = new robosense::lidar::rsfsc_lib::UserAuthority();
}  // MainWindow::setupLayout

void MainWindow::readSettings()
{
  RSFSCLog::getInstance()->info("FUC: read settings");
  QString suffix(PROJECT_NAME);
  suffix +=
    (!is_window_position_fix_) ? QString::fromUtf8("") : (QString::fromUtf8("/") + QString::number(window_index_));
  suffix += QString::fromUtf8("/mainwindow");
  QSettings settings("RoboSense", suffix);

  if (!is_window_position_fix_)
  {
    restoreGeometry(settings.value("geometry").toByteArray());
    restoreState(settings.value("window_state").toByteArray());
  }
}

void MainWindow::writeSettings()
{
  RSFSCLog::getInstance()->info("write settings");
  QString suffix(PROJECT_NAME);
  suffix +=
    (!is_window_position_fix_) ? QString::fromUtf8("") : (QString::fromUtf8("/") + QString::number(window_index_));
  suffix += QString::fromUtf8("/mainwindow");
  QSettings settings("RoboSense", suffix);
  settings.setValue("geometry", saveGeometry());
  settings.setValue("window_state", saveState());
}

void MainWindow::registerAgingPara()
{
  para_setting_ = new ParameterSetting();

  std::list<std::string> lidar_type_list;
  lidar_type_list.emplace_back("Airy");
  lidar_type_vec_.emplace_back(ProtocolType::MECH_AIRY);

  para_setting_->registerPathPara("固件升级", "firmware_dir", "", true, "固件目录");
  para_setting_->registerBoolPara("固件升级", "is_use_zip_file", false, "升级指定zip文件");
  para_setting_->registerPathPara("固件升级", "firmware_zip_file_path", "", false, "固件zip文件路径");

  para_setting_->registerOptionPara("老化参数", "lidar_type", lidar_type_list, 0, "雷达类型");
  para_setting_->registerDoublePara("老化参数", "aging_time", 0, 96, 48, 2, 0.01, "老化时长(h)");
  para_setting_->registerIntPara("老化参数", "aging_time_interval", 30, 604800, 1200, 10, "数据读取频率(s)");
  para_setting_->registerIntPara("老化参数", "difop_timeout", 1, 1000, 60, 1, "difop超时时间(s)");
  para_setting_->registerIntPara("老化参数", "cooling_time", 1, 20, 10, 1, "冷却时长(min)");

  QList<QSerialPortInfo> port_info = QSerialPortInfo::availablePorts();
  std::list<std::string> serial_port_list;
  for (int i = 0; i < G_MAX_RELAY_NUM + 1; i++)
  {
    serial_port_list.push_back(port_info.value(i).portName().toStdString());
  }
  para_setting_->registerOptionPara("老化参数", "relay_layout", { "HHL", "SS", "NORMAL" }, 0, "继电器布局");
  para_setting_->registerOptionPara("老化参数", "relay_port_1", serial_port_list, 0, "继电器1串口");
  para_setting_->registerOptionPara("老化参数", "relay_port_2", serial_port_list, 1, "继电器2串口");
  para_setting_->registerOptionPara("老化参数", "relay_port_3", serial_port_list, 2, "继电器3串口");
  para_setting_->registerOptionPara("老化参数", "relay_port_4", serial_port_list, 3, "继电器4串口");
  para_setting_->registerOptionPara("老化参数", "relay_port_5", serial_port_list, 4, "继电器5串口");
  para_setting_->registerOptionPara("老化参数", "relay_port_6", serial_port_list, 5, "继电器6串口");

  para_setting_->registerOptionPara("老化参数", "relay_port_7", serial_port_list, 6, "继电器7串口");

  para_setting_->registerIntPara("老化参数", "shelf_row", 1, 15, 4, 1, "老化架规格(行)");
  para_setting_->registerIntPara("老化参数", "shelf_col", 1, 15, 8, 1, "老化架规格(列)");

  para_setting_->registerBoolPara("流程参数", "is_maintain_fail_env", false, "是否保留失败现场");

  para_setting_->registerBoolPara("三色灯参数", "is_use_tricolor_light", false, "是否启用三色灯");
  para_setting_->registerOptionPara("三色灯参数", "tricolor_port_name", serial_port_list, 8, "三色灯串口");
  para_setting_->registerIntPara("三色灯参数", "tricolor_red_index", 0, 128, 0, 1, "红灯序号");
  para_setting_->registerIntPara("三色灯参数", "tricolor_yellow_index", 0, 128, 0, 1, "黄灯序号");
  para_setting_->registerIntPara("三色灯参数", "tricolor_green_index", 0, 128, 0, 1, "绿灯序号");
  para_setting_->registerIntPara("三色灯参数", "tricolor_beep_index", 0, 128, 0, 1, "蜂鸣器序号");

  para_setting_->registerIntPara("压测参数", "stress_time", 0, 1000000, 30, 1, "压力测试次数");
  para_setting_->registerIntPara("压测参数", "stress_wait_time", 0, 1000000, 50, 1, "上电后等待时间(s)");
  para_setting_->registerIntPara("压测参数", "stress_restart_time", 0, 10000000, 50, 50, "重新上电时间(ms)");
  para_setting_->registerBoolPara("压测参数", "stress_allow_fail", true, "压力测试允许失败");

  para_setting_->loadPara("job");
}

void MainWindow::loadCsvAndAppConfig()
{
  QString root_path = INSTALL_PREFIX_SHARE;
  if (QDir::currentPath().contains("build"))
  {
    root_path = QDir::currentPath() + "/../";
  }
  else if (QDir::current().exists("build"))
  {
    root_path = QDir::currentPath() + "/";
  }

  loadCsvLimit(root_path);
  loadAppConfig(root_path);
}

void MainWindow::loadCsvLimit(const QString& _root_path)
{
  QDir file_dir                    = QDir(_root_path);
  QStringList support_project_list = { "airy" };

  for (const auto& project : support_project_list)
  {
    QString register_file_name  = project + "_register.csv";
    QString limit_file_name     = project + "_limit.csv";
    QString sim_angle_file_name = project + "_sim_angle.csv";
    QString exp_angle_file_name = project + "_exp_angle.csv";

    QString register_file_path  = file_dir.filePath("config/" + register_file_name);
    QString limit_file_path     = file_dir.filePath("config/" + limit_file_name);
    QString sim_angle_file_path = file_dir.filePath("config/" + sim_angle_file_name);
    QString exp_angle_file_path = file_dir.filePath("config/" + exp_angle_file_name);

    std::shared_ptr<CsvUtils> reg_csv_parser_ptr       = std::make_shared<CsvUtils>();
    std::shared_ptr<CsvUtils> limit_csv_parser_ptr     = std::make_shared<CsvUtils>();
    std::shared_ptr<CsvUtils> sim_angle_csv_parser_ptr = std::make_shared<CsvUtils>();
    std::shared_ptr<CsvUtils> exp_angle_csv_parser_ptr = std::make_shared<CsvUtils>();
    try
    {
      reg_csv_parser_ptr->loadRegisterCsvInfo(register_file_path);
      limit_csv_parser_ptr->loadLimitCsvInfo(limit_file_path);
      sim_angle_csv_parser_ptr->loadAngleCsvInfo(sim_angle_file_path);
      exp_angle_csv_parser_ptr->loadAngleCsvInfo(exp_angle_file_path);
      app()->setCsvUtils(project + "_reg", reg_csv_parser_ptr);
      app()->setCsvUtils(project + "_limit", limit_csv_parser_ptr);
      app()->setCsvUtils(project + "_sim_angle", sim_angle_csv_parser_ptr);
      app()->setCsvUtils(project + "_exp_angle", exp_angle_csv_parser_ptr);
    }
    catch (const std::exception& e)
    {
      LOG_ERROR("加载csv文件失败: {}, {}", register_file_path.toStdString(), e.what());
      continue;
    }
  }
}

void MainWindow::loadAppConfig(const QString& _root_path)
{
  QString app_config_path = _root_path + "/config/app_config.json";
  QFile file(app_config_path);
  if (!file.open(QIODevice::ReadOnly))
  {
    LOG_ERROR("打开app_config.json文件失败: {}", app_config_path.toStdString());
    return;
  }

  QJsonDocument doc = QJsonDocument::fromJson(file.readAll());
  app_config_       = doc.object();
  app()->setAppConfig(app_config_);
}

void MainWindow::slotAgingCounts()
{
  int shelf_row = 4;
  para_setting_->getPara("老化参数", "shelf_row", shelf_row);
  int shelf_col = 8;
  para_setting_->getPara("老化参数", "shelf_col", shelf_col);

  int all_num = shelf_row * shelf_col;

  aging_count_->setText(QString("正在老化台数： %1 | 剩余空位： %2").arg(running_count_).arg(all_num - running_count_));
}

void MainWindow::slotRightClickedAgingListWidget(QPoint _pt)
{
  // 获得鼠标点击的x，y坐标点
  int x             = _pt.x();
  int y             = _pt.y();
  QModelIndex index = aging_list_widget_->indexAt(QPoint(x, y));

  int shelf_row = 4;
  para_setting_->getPara("老化参数", "shelf_row", shelf_row);
  int shelf_col = 8;
  para_setting_->getPara("老化参数", "shelf_col", shelf_col);

  int row         = index.row();  //获得QTableWidget列表点击的行数
  int col         = index.column();
  int lidar_index = row * shelf_col + col;

  QMenu* menu                = new QMenu(aging_list_widget_);
  QAction* reset_aging_state = new QAction("状态重置", aging_list_widget_);
  QAction* aging_data        = new QAction("显示老化数据", aging_list_widget_);
  QAction* aging_log         = new QAction("打开结果文件夹", aging_list_widget_);
  connect(reset_aging_state, &QAction::triggered, [=] {
    MainWindow::slotClickedMenuResetLidarStatus(lidar_index);
    TricolorLightController::getInstance()->setAgingState(lidar_index + 1, RUN_IDLE);
  });
  connect(aging_data, &QAction::triggered, [=] { MainWindow::slotClickedMenuShowAgingData(lidar_index); });
  connect(aging_log, &QAction::triggered, [=] { aging_progress_array_.at(lidar_index)->slotShowAgingLog(); });
  menu->addAction(reset_aging_state);
  menu->addAction(aging_data);
  menu->addAction(aging_log);
  menu->move(cursor().pos());
  menu->show();
}

void MainWindow::slotClickedMenuResetLidarStatus(int _lidar_index)
{
  aging_progress_array_.at(_lidar_index)->resetState();
}
void MainWindow::slotClickedMenuShowAgingData(int _lidar_index)
{
  aging_progress_array_.at(_lidar_index)->slotShowAgingData();
}
void MainWindow::slotUpdateParaInfo(const int _lidar_index)
{
  auto para_info        = getParaInfo();
  para_info.lidar_index = _lidar_index;
  aging_progress_array_.at(_lidar_index - 1)->setParaInfo(para_info);
}
void MainWindow::slotFsmStarted(const int _lidar_index)
{
  running_count_++;
  if (TricolorLightController::getInstance()->getController() != nullptr)
  {
    TricolorLightController::getInstance()->setAgingState(_lidar_index, RUN_BUSY);
  }
  slotAgingCounts();
}
void MainWindow::slotFsmStopped(const int _lidar_index)
{
  running_count_--;

  if (TricolorLightController::getInstance()->getController() != nullptr)
  {
    TricolorLightController::getInstance()->setAgingState(
      _lidar_index, aging_progress_array_.at(_lidar_index - 1)->getWorkModelPtr()->getRunState());
  }
  slotAgingCounts();
}

void MainWindow::slotShowRunningWidget(int _row, int _col)
{
  int lidar_index = (_row * aging_list_widget_->columnCount()) + _col;
  if (aging_progress_array_.at(lidar_index)->isEnabled())
  {
    aging_progress_array_.at(lidar_index)->slotShowLidarInfoWidget();
  }
}

void MainWindow::slotLidarChangeIpIsReady(const bool _is_ready, const int _lidar_index)
{
  int count = 1;
  for (auto& progress_widget : aging_progress_array_)
  {
    if (count++ == _lidar_index)
    {
      continue;
    }
    if (progress_widget == nullptr)
    {
      continue;
    }
    progress_widget->setEnabled(_is_ready);
  }

  if ((app()->getChangingIpIndex() == _lidar_index) && _is_ready)
  {
    app()->setChangingIpIndex(0);
  }

  if (!_is_ready)
  {
    app()->setChangingIpIndex(_lidar_index);
  }
}

void MainWindow::closeEvent(QCloseEvent* _event)
{
  RSFSCLog::getInstance()->info("FUC: closeEvent call back");
  QMessageBox::StandardButton msg_box = QMessageBox::warning(this, "警告", "<font color='red'>确定退出?</font>",
                                                             QMessageBox::Yes | QMessageBox::No, QMessageBox::No);

  if (msg_box == QMessageBox::No)
  {
    _event->ignore();
    return;
  }

  for (auto* ptr : aging_progress_array_)
  {
    if (ptr != nullptr)
    {
      ptr->quit();
    }
  }
  // aging_list_widget_->deleteLater();
  // mes_widget_->releaseMESWidget();
  mes_widget_->releaseMESWidget();

  for (auto* ptr : aging_progress_array_)
  {
    ptr->deleteLater();
  }
  aging_list_widget_->deleteLater();

  if (msg_box == QMessageBox::No)
  {
    _event->ignore();
    return;
  }

  if (browser_message_->isVisible())
  {
    browser_message_->close();
  }

  QMainWindow::closeEvent(_event);
}

void MainWindow::cleanBeforeQuit()
{
  RSFSCLog::getInstance()->info("FUC: clean before quit");
  writeSettings();

  RSFSCLog::getInstance()->setQtLogWidget(Q_NULLPTR);
  delete browser_message_;
}

void MainWindow::slotShowSetting()
{
  bool is_technician_can_change =
    user_authority_->isNotLessThan(robosense::lidar::rsfsc_lib::UserAuthority::Level::LEVEL_OPERATOR);
  std::cout << "is_technician_can_change:" << is_technician_can_change << std::endl;
  if (!is_technician_can_change)
  {
    RSFSCLog::getInstance()->warn("当前用户权限不足，不能打开设置界面");
    return;
  }
  para_setting_->showParaTree();
}

void MainWindow::slotShowMESWidget()
{
  if (mes_widget_ != nullptr)
  {
    mes_widget_->showWidget();
  }
}

void MainWindow::slotShowMessageBox(const QString& _msg)
{
  QMessageBox* message_box = new QMessageBox(this);
  message_box->setAttribute(Qt::WA_DeleteOnClose);  // 确保对话框关闭时被删除
  message_box->setWindowTitle("警告");
  message_box->setText(_msg);
  message_box->setStandardButtons(QMessageBox::Ok);
  message_box->setModal(false);  // 设置为非模态
  message_box->show();
}

void MainWindow::slotShowAbout()
{
  if (widget_about_->isVisible())
  {
    widget_about_->raise();
    widget_about_->activateWindow();
  }
  else
  {
    widget_about_->show();
  }
}

void MainWindow::moveEvent(QMoveEvent* _event)
{
  if (is_window_position_fix_ && !is_window_max_)
  {
    this->setGeometry(geometry_);  // already move before this function called
  }
  else
  {
    QMainWindow::moveEvent(_event);
  }
}

void MainWindow::resizeEvent(QResizeEvent* _event)
{
  if (is_window_position_fix_ && !is_window_max_)
  {
    this->setGeometry(geometry_);  // already move before this function called
  }
  else
  {
    // int width_window = (QMainWindow::size().width() > 1200) ? QMainWindow::size().width() : 1200;
    QMainWindow::resizeEvent(_event);
  }
}

void MainWindow::agingListWidgetInit()
{
  aging_list_widget_ = new AgingTableWidget(this);

  int shelf_row = 4;
  para_setting_->getPara("老化参数", "shelf_row", shelf_row);

  int shelf_col = 8;
  para_setting_->getPara("老化参数", "shelf_col", shelf_col);

  aging_list_widget_->setSelectionBehavior(QAbstractItemView::SelectRows);
  aging_list_widget_->setEditTriggers(QAbstractItemView::NoEditTriggers);
  aging_list_widget_->verticalHeader()->setVisible(false);
  aging_list_widget_->horizontalHeader()->setVisible(false);
  aging_list_widget_->horizontalHeader()->setSectionResizeMode(QHeaderView::Stretch);  //最后一行自适应列宽
  aging_list_widget_->verticalHeader()->setSectionResizeMode(QHeaderView::Stretch);    //自适应行宽
  int row_num = shelf_row;
  int col_num = shelf_col;
  aging_list_widget_->setRowCount(row_num);
  aging_list_widget_->setColumnCount(col_num);

  ParaInfo para_info = getParaInfo();

  for (int i = 0; i < row_num; i++)
  {
    for (int j = 0; j < col_num; j++)
    {
      aging_list_widget_->setItem(i, j, new QTableWidgetItem());
      aging_list_widget_->item(i, j)->setTextAlignment(Qt::AlignHCenter | Qt::AlignVCenter);
      aging_list_widget_->item(i, j)->setFlags(aging_list_widget_->item(i, j)->flags() & ~Qt::ItemIsSelectable);

      int arr_index                       = i * col_num + j;
      para_info.lidar_index               = arr_index + 1;
      aging_progress_array_.at(arr_index) = new AgingProgressWidget(para_info, aging_list_widget_);

      aging_list_widget_->setCellWidget(i, j, aging_progress_array_.at(arr_index));

      connect(this, &MainWindow::signalExitAgingWidget, aging_progress_array_.at(arr_index),
              &AgingProgressWidget::slotExit, Qt::DirectConnection);
    }
  }
  connect(aging_list_widget_, &AgingTableWidget::cellDoubleClicked,
          [=](int _row, int _col) { slotShowRunningWidget(_row, _col); });
  connect(aging_list_widget_, &AgingTableWidget::signalPressReturnKey,
          [=](int _row, int _col) { slotShowRunningWidget(_row, _col); });

  aging_list_widget_->setContextMenuPolicy(Qt::CustomContextMenu);
  connect(aging_list_widget_, &AgingTableWidget::customContextMenuRequested, this,
          &MainWindow::slotRightClickedAgingListWidget);
  aging_list_widget_->setEnabled(false);
}

ParaInfo MainWindow::getParaInfo()
{
  ParaInfo para_info;

  para_setting_->getPara("固件升级", "firmware_dir", para_info.firmware_dir);
  para_setting_->getPara("固件升级", "is_use_zip_file", para_info.is_use_zip_file);
  para_setting_->getPara("固件升级", "firmware_zip_file_path", para_info.firmware_zip_file_path);

  para_setting_->getPara("流程参数", "is_maintain_fail_env", para_info.is_maintain_fail_env);
  para_setting_->getPara("老化参数", "shelf_row", para_info.row_num);
  para_setting_->getPara("老化参数", "shelf_col", para_info.col_num);

  int lidar_type_index = 0;
  para_setting_->getPara("老化参数", "lidar_type", lidar_type_index);
  para_info.lidar_type    = lidar_type_vec_.at(lidar_type_index);
  double aging_time_hours = 0;
  para_setting_->getPara("老化参数", "aging_time", aging_time_hours);
  para_info.aging_time_secs = static_cast<int>(aging_time_hours * 3600.F);
  para_setting_->getPara("老化参数", "aging_time_interval", para_info.aging_check_interval);
  para_setting_->getPara("老化参数", "difop_timeout", para_info.difop_timeout);
  para_setting_->getPara("老化参数", "cooling_time", para_info.cooling_time_secs);
  para_setting_->getPara("压测参数", "stress_time", para_info.stress_num);
  para_setting_->getPara("压测参数", "stress_wait_time", para_info.stress_wait_start_time);
  para_setting_->getPara("压测参数", "stress_restart_time", para_info.stress_toggle_interval);
  para_setting_->getPara("压测参数", "stress_allow_fail", para_info.stress_allow_fail);

  para_info.start_up_max_time = 50000;
  para_info.ping_timeout      = 50000;
  para_info.org_ip            = "*************";
  para_info.org_msop_port     = 6699;
  para_info.org_difop_port    = 7788;

  para_info.check_msop_size         = app_config_["check_msop_size"].toInt();
  para_info.encod_calib_collect_num = app_config_["encod_calib_collect_num"].toInt();

  return para_info;
}

void MainWindow::relayPortInit()
{
  std::array<int, G_MAX_RELAY_NUM + 1> port_idx = {};
  para_setting_->getPara("老化参数", "relay_port_1", port_idx[0]);
  para_setting_->getPara("老化参数", "relay_port_2", port_idx[1]);
  para_setting_->getPara("老化参数", "relay_port_3", port_idx[2]);
  para_setting_->getPara("老化参数", "relay_port_4", port_idx[3]);
  para_setting_->getPara("老化参数", "relay_port_5", port_idx[4]);
  para_setting_->getPara("老化参数", "relay_port_6", port_idx[5]);
  para_setting_->getPara("老化参数", "relay_port_7", port_idx[6]);

  int relay_layout_index = 0;
  para_setting_->getPara("老化参数", "relay_layout", relay_layout_index);
  LOG_INFO("relay_layout_index: {}", relay_layout_index);

  bool is_use_tricolor_light = false;
  int tricolor_red_index     = 0;
  int tricolor_yellow_index  = 0;
  int tricolor_green_index   = 0;
  int tricolor_beep_index    = 0;
  // QString tricolor_port_name;
  int tricolor_port_index = 0;
  para_setting_->getPara("三色灯参数", "is_use_tricolor_light", is_use_tricolor_light);
  para_setting_->getPara("三色灯参数", "tricolor_port_name", tricolor_port_index);
  para_setting_->getPara("三色灯参数", "tricolor_red_index", tricolor_red_index);
  para_setting_->getPara("三色灯参数", "tricolor_yellow_index", tricolor_yellow_index);
  para_setting_->getPara("三色灯参数", "tricolor_green_index", tricolor_green_index);
  para_setting_->getPara("三色灯参数", "tricolor_beep_index", tricolor_beep_index);

  std::vector<int> tricolor_index(
    { tricolor_red_index, tricolor_yellow_index, tricolor_green_index, tricolor_beep_index });

  int relay_num = aging_num_ / 16;
  if (aging_num_ % 16 > 0)
  {
    relay_num += 1;
  }

  RSFSCLog::getInstance()->info("初始化继电器数量{0}", relay_num);

  QList<QSerialPortInfo> port_info = QSerialPortInfo::availablePorts();
  std::vector<std::string> serial_port_list;
  serial_port_list.reserve(relay_num);
  for (int i = 0; i < port_info.size(); i++)
  {
    serial_port_list.push_back(port_info.value(i).portName().toStdString());
  }

  std::vector<std::string> port_name {};
  for (int i = 0; i < relay_num; i++)
  {
    if (static_cast<size_t>(port_idx[i]) >= serial_port_list.size())
    {
      port_idx[i] = 0;
    }
    port_name.push_back(serial_port_list[port_idx[i]]);
  }

  if (static_cast<size_t>(tricolor_port_index) >= serial_port_list.size())
  {
    tricolor_port_index = 0;
  }
  std::string tri_color_port_name = serial_port_list[tricolor_port_index];
  LOG_INFO("三色灯串口号: {}", tri_color_port_name);

  int lidar_type_index = 0;
  para_setting_->getPara("老化参数", "lidar_type", lidar_type_index);
  RelayController* relay_controller = RelayController::getInstance();
  relay_controller->initRelayController(static_cast<RelayLayout>(relay_layout_index), port_name);

  TricolorLightController::getInstance()->setDisable(!is_use_tricolor_light);
  if (is_use_tricolor_light)
  {
    if (!TricolorLightController::getInstance()->init(tri_color_port_name, aging_num_, tricolor_red_index,
                                                      tricolor_yellow_index, tricolor_green_index, tricolor_beep_index))
    {
      LOG_ERROR("三色灯初始化失败{}", tri_color_port_name);
    }
  }
}

void MainWindow::slotUpdateAllWidgetState(robosense::lidar::rsfsc_lib::UserAuthority* _user_authority)
{
  if (_user_authority != nullptr)
  {
    *user_authority_ = *(_user_authority);
  }

  bool is_developer =
    user_authority_->isNotLessThan(robosense::lidar::rsfsc_lib::UserAuthority::Level::LEVEL_DEVELOPER) ||
    user_authority_->isNotLessThan(robosense::lidar::rsfsc_lib::UserAuthority::Level::LEVEL_MANAGER);
  if (is_developer)
  {
    aging_list_widget_->setEnabled(true);
    para_setting_->setEnabled(true);
    for (auto* widget : aging_progress_array_)
    {
      if (widget != nullptr)
      {
        widget->setOperateEnabled(true);
      }
    }
    return;
  }

  para_setting_->setEnabled(false);
  bool is_technician =
    user_authority_->isNotLessThan(robosense::lidar::rsfsc_lib::UserAuthority::Level::LEVEL_TECHNICIAN) ||
    user_authority_->isNotLessThan(robosense::lidar::rsfsc_lib::UserAuthority::Level::LEVEL_OPERATOR);
  if (is_technician)
  {
    aging_list_widget_->setEnabled(true);
  }
  else
  {
    aging_list_widget_->setEnabled(false);
  }

  for (auto* widget : aging_progress_array_)
  {
    if (nullptr != widget)
    {
      widget->setOperateEnabled(false);
    }
  }
}
}  // namespace lidar
}  // namespace robosense
