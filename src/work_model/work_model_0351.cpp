﻿/******************************************************************************
 * Copyright 2024 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai
 *
 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.
 *
 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONS<PERSON>QUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#include "work_model/work_model_0351.h"
#include "app_event.h"
#include "common_struct.h"

// NOLINTNEXTLINE(modernize-concat-nested-namespaces)
namespace robosense
{
namespace lidar
{

bool WorkModel0351::humanConfirm()
{
  HumanConfirmResult human_confirm_result;
  app()->signalHumanConfirmResult(path_.verification_file_path, &human_confirm_result);
  std::unique_lock<std::mutex> lock(human_confirm_result.mutex);
  human_confirm_result.cv.wait(lock, [&human_confirm_result, this] { return isAbort() || human_confirm_result.done; });

  if (!addMeasureMessage("human_confirm", human_confirm_result.ret))
  {
    LOG_INDEX_ERROR("人工确认回读校验异常");
    return false;
  }
  return true;
}

bool WorkModel0351::writeVbdData()
{
  auto data_vec = generateVbdBit();
  if (data_vec.empty())
  {
    return false;
  }

  if (!getLidarManager()->writeTopFlashWithVer("write_vbd", data_vec, CustomGdiFeild::getStartAddr()))
  {
    setFailMsg("写入VBD数据失败");
    addMeasureMessage("vbd_write_data", false);
    updateAgingState(AGING_NG);
    return false;
  }
  addMeasureMessage("vbd_write_data", true);

  return true;
}
bool WorkModel0351::clearCalibData()
{
  constexpr uint32_t START_ADDR                      = 0xf00000;
  constexpr uint32_t END_ADDR                        = 0xffffff;
  std::array<char, (END_ADDR - START_ADDR + 1)> data = { 0 };

  std::vector<uint8_t> data_vec(data.begin(), data.end());
  if (!getLidarManager()->writeTopFlashWithVer("clear_calib_data", data_vec, START_ADDR))
  {
    setFailMsg("清除标定数据失败, all zero大小: {}kB", data.size() / 1024);
    addMeasureMessage("clear_calib_data", false);
    updateAgingState(AGING_NG);
    return false;
  }
  addMeasureMessage("clear_calib_data", true);

  return true;
}

}  // namespace lidar
}  // namespace robosense