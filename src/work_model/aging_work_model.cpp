﻿/******************************************************************************
 * Copyright 2024 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai
 *
 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.
 *
 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONS<PERSON>QUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#include "work_model/aging_work_model.h"
#include "app_event.h"
#include "comm_func/mes.h"
#include "mes_widget.h"
#include "relay_controller.h"
#include "rsfsc_log/rsfsc_log_macro.h"
#include "utils/crc_utils.h"
#include <QCryptographicHash>
#include <QJsonDocument>
#include <QProcess>

// NOLINTNEXTLINE(modernize-concat-nested-namespaces)
namespace robosense
{
namespace lidar
{

AgingWorkModel::AgingWorkModel(rsfsc_lib::WidgetLidarInfo* _lidar_info) : WorkModel(_lidar_info)
{
  limit_csv_utils_ptr_     = app()->getCsvUtils("airy_limit");
  sim_angle_csv_utils_ptr_ = app()->getCsvUtils("airy_sim_angle");
  exp_angle_csv_utils_ptr_ = app()->getCsvUtils("airy_exp_angle");
  if (limit_csv_utils_ptr_ == nullptr)
  {
    LOG_INDEX_ERROR("获取limit csv解析器失败");
    app()->signalShowErrorMessageBox("加载limit文件失败");
    return;
  }
  if (sim_angle_csv_utils_ptr_ == nullptr || exp_angle_csv_utils_ptr_ == nullptr)
  {
    LOG_INDEX_ERROR("获取sim_angle csv解析器失败");
    app()->signalShowErrorMessageBox("加载经验水平角失败");
    return;
  }
  if (auto angle_vec_pair = sim_angle_csv_utils_ptr_->getAngleData())
  {
    ver_angle_vec_     = std::move(angle_vec_pair->first);
    hor_angle_vec_     = std::move(angle_vec_pair->second);
    sim_ver_angle_vec_ = ver_angle_vec_;
  }
  else
  {
    LOG_INDEX_ERROR("获取sim_angle数据失败");
    app()->signalShowErrorMessageBox("加载经验水平角失败");
    return;
  }

  if (auto angle_vec_pair = exp_angle_csv_utils_ptr_->getAngleData())
  {
    ver_angle_vec_     = std::move(angle_vec_pair->first);
    hor_angle_vec_     = std::move(angle_vec_pair->second);
    exp_ver_angle_vec_ = ver_angle_vec_;
  }
  else
  {
    LOG_INDEX_ERROR("获取exp_angle数据失败");
    app()->signalShowErrorMessageBox("加载经验水平角失败");
    return;
  }

  QString project_code_str = getLidarManager()->getLidarInfo()->getProjectCodeStr();
  QString vbd_cal_k_str    = fmt::format("{}_vbd_cal_k", project_code_str).c_str();
  if (!app()->getAppConfig().contains(vbd_cal_k_str))
  {
    LOG_INDEX_ERROR("未找到VBD标定系数: {}", vbd_cal_k_str);
    app()->signalShowErrorMessageBox("加载到VBD标定系数");
    return;
  }
  vbd_cal_k_ = static_cast<float>(app()->getAppConfig()[vbd_cal_k_str].toDouble());
  LOG_INDEX_INFO("VBD标定系数k: {}", vbd_cal_k_);

  if (!app()->getAppConfig().contains("exp_zero_angle"))
  {
    LOG_INDEX_ERROR("未找到经验零度角");
    app()->signalShowErrorMessageBox("加载到经验零度角");
    return;
  }
  exp_zero_angle_ = static_cast<float>(app()->getAppConfig()["exp_zero_angle"].toDouble());
  LOG_INDEX_INFO("经验零度角: {}", exp_zero_angle_);
}
// AgingWorkModel::~AgingWorkModel() {}

void AgingWorkModel::abort()
{
  WorkModel::abort();
  getLidarManager()->stopMonitorDifop();
  updateAgingState(AGING_ABORT);
}

bool AgingWorkModel::addMeasureMessage(const QString& _name, const bool _data)
{
  return addMeasureMessage(_name, _data ? 1 : 0, rsfsc_lib::MeasureDataType::MEASURE_DATA_TYPE_INT);
}
bool AgingWorkModel::addMeasureMessage(const QString& _name,
                                       const double _data,
                                       const rsfsc_lib::MeasureDataType _data_type)
{
  auto limit_info = limit_csv_utils_ptr_->getLimitInfo(_name.toStdString());

  // 特殊处理
  if (limit_info.getName() == "fsm_stress_success_count")
  {
    limit_info.min_th = para_info_.stress_num;
    limit_info.max_th = para_info_.stress_num;
  }

  if (!limit_info.is_ok)
  {
    LOG_INDEX_ERROR("未找到该limit信息 name: {}", _name);
    return false;
  }

  if (_data_type == rsfsc_lib::MEASURE_DATA_TYPE_INT)
  {
    LOG_INDEX_INFO("addMeasure int name: {}, data: {}", _name.toStdString(), static_cast<int64_t>(_data));
  }
  else if (_data_type == rsfsc_lib::MEASURE_DATA_TYPE_HEX)
  {
    LOG_INDEX_INFO("addMeasure hex name: {}, data: 0x{:x}", _name.toStdString(), static_cast<int64_t>(_data));
  }
  else
  {
    LOG_INDEX_INFO("addMeasure float name: {}, data: {}", _name.toStdString(), _data);
  }

  return app()->getWidgetLogSetting()->addMeasureMessage(getLidarIndex(), limit_info, _data, _data_type);
}
bool AgingWorkModel::addMeasureMessage(const QString& _name, const std::string& _data)
{
  auto limit_info = limit_csv_utils_ptr_->getLimitInfo(_name.toStdString());
  if (!limit_info.is_ok)
  {
    LOG_INDEX_ERROR("未找到该limit信息 name: {}", _name);
    return false;
  }
  LOG_INDEX_INFO("addMeasure str name: {}, data: {}", _name.toStdString(), _data);
  return app()->getWidgetLogSetting()->addMeasureMessage(getLidarIndex(), limit_info, _data);
}

void AgingWorkModel::notifyDataCheck()
{
  std::unique_lock<std::mutex> lock(mutex_data_check_);
  is_data_check_ = true;
  cv_data_check_.notify_all();
}
bool AgingWorkModel::waitForDataCheck()
{
  std::unique_lock<std::mutex> lock(mutex_data_check_);
  if (is_data_check_)
  {
    is_data_check_ = false;
    return true;
  }
  LOG_INDEX_DEBUG("休眠等待进行下一次数据检查");
  cv_data_check_.wait(lock);
  LOG_INDEX_DEBUG("被唤醒");
  is_data_check_ = false;
  return true;
}

bool AgingWorkModel::turnOnRelay() { return turnRelay(true); }
bool AgingWorkModel::turnOffRelay() { return turnRelay(false); }
bool AgingWorkModel::turnRelay(const bool _is_open)
{
  if (!_is_open)
  {
    getLidarManager()->disconnect();
    std::this_thread::sleep_for(std::chrono::milliseconds(1000));
  }
  bool ret = RelayController::getInstance()->relayTurn(getLidarIndex() - 1, _is_open);
  auto msg = fmt::format("继电器{}", (_is_open ? "上电" : "断电"));
  if (ret)
  {
    LOG_INDEX_INFO(msg);
  }
  else
  {
    LOG_INDEX_ERROR(msg);
  }
  return ret;
}
bool AgingWorkModel::toggleRelay(const int _interval)
{
  bool ret = turnOffRelay();
  std::this_thread::sleep_for(std::chrono::milliseconds(_interval));
  ret &= turnOnRelay();
  return ret;
}
bool AgingWorkModel::rebootAndWait()
{
  std::this_thread::sleep_for(std::chrono::milliseconds(1000));
  toggleRelay();
  if (!getLidarManager()->connectAndWaitForTop())
  {
    LOG_INDEX_ERROR("重启雷达失败");
    addMeasureMessage("reboot_lidar", false);
    return false;
  }
  LOG_INDEX_INFO("重启雷达成功");
  return true;
}

bool AgingWorkModel::checkAllState()
{
  QString data_path;
  QString result_path;
  QString temp_path;
  if (!app()->getMesWidget()->checkMesIsOK(getLidarIndex(), data_path, result_path, temp_path))
  {
    return false;
  }
  app()->getWidgetLogSetting()->setFirmwareRevision(getLidarIndex(), 0, 0);
  getLidarManager()->setLogIndex(getLidarIndex());

  path_.data_dir   = data_path;
  path_.result_dir = result_path;
  path_.temp_dir   = temp_path;

  path_.exclude_msop_dump_file_path = path_.data_dir.absoluteFilePath("exclude_msop_dump.pcap");
  path_.only_msop_dump_file_path    = path_.data_dir.absoluteFilePath("only_msop_dump.pcap");
  path_.connect_dump_file_path      = path_.data_dir.absoluteFilePath("connect_dump.pcap");
  path_.chn_angle_file_path         = path_.data_dir.absoluteFilePath("chn_angle.csv");
  path_.vbd_file_path =
    path_.data_dir.absoluteFilePath(fmt::format("vbd_calib_{:#x}.bit", CustomGdiFeild::getStartAddr()).c_str());
  return true;
}
bool AgingWorkModel::initPath(const Path& _path)
{
  path_ = _path;
  return true;
}

void AgingWorkModel::updateFirmwareUpdateState(const FirmwareUpdateState _state)
{
  firmware_update_state_ = _state;
  if (update_firmware_update_state_cb_)
  {
    update_firmware_update_state_cb_(firmware_update_state_);
  }
}
FirmwareUpdateState AgingWorkModel::getFirmwareUpdateState() const { return firmware_update_state_; }
void AgingWorkModel::updateEncodeCalibState(const EncodeCalibState _state)
{
  encode_calib_state_ = _state;
  if (update_code_wheel_calib_state_cb_)
  {
    update_code_wheel_calib_state_cb_(encode_calib_state_);
  }
}
EncodeCalibState AgingWorkModel::getEncodeCalibState() const { return encode_calib_state_; }

void AgingWorkModel::updateAgingState(const AgingState _state)
{
  aging_state_ = _state;
  if (update_aging_state_cb_)
  {
    update_aging_state_cb_(aging_state_);
  }
}
AgingState AgingWorkModel::getAgingState() const { return aging_state_; }
void AgingWorkModel::updateStressState(const StressState _state)
{
  stress_state_ = _state;
  if (update_stress_state_cb_)
  {
    update_stress_state_cb_(stress_state_);
  }
}
StressState AgingWorkModel::getStressState() const { return stress_state_; }
void AgingWorkModel::updateRunState(const RunState _state)
{
  run_state_ = _state;
  if (update_run_state_cb_)
  {
    update_run_state_cb_(run_state_);
  }
}
void AgingWorkModel::updateRunState()
{
  if (aging_state_ == AGING_ABORT || stress_state_ == STRESS_ABORT || firmware_update_state_ == FIRMWARE_UPDATE_ABORT ||
      encode_calib_state_ == ENCODE_CALIB_ABORT)
  {
    run_state_ = RUN_ABORT;
  }
  else if (aging_state_ == AGING_NG || stress_state_ == STRESS_NG || firmware_update_state_ == FIRMWARE_UPDATE_NG ||
           encode_calib_state_ == ENCODE_CALIB_NG)
  {
    run_state_ = RUN_NG;
  }
  else if (aging_state_ == AGING_PASS || stress_state_ == STRESS_PASS ||
           firmware_update_state_ == FIRMWARE_UPDATE_PASS || encode_calib_state_ == ENCODE_CALIB_PASS)
  {
    run_state_ = RUN_PASS;
  }
  else if (aging_state_ == AGING_SKIP && stress_state_ == STRESS_SKIP &&
           firmware_update_state_ == FIRMWARE_UPDATE_SKIP && encode_calib_state_ == ENCODE_CALIB_SKIP)
  {
    run_state_ = RUN_PASS;
  }
  else
  {
    run_state_ = RUN_BUSY;
  }
  if (update_run_state_cb_)
  {
    update_run_state_cb_(run_state_);
  }
}
RunState AgingWorkModel::getRunState() const { return run_state_; }

bool AgingWorkModel::findAndSetPath(const QString& _folder,
                                    const QStringList& _file_list,
                                    const QString& _keyword,
                                    QString& _path)
{
  for (const auto& file : _file_list)
  {
    if (file.contains(_keyword))
    {
      QDir dir(_folder);
      _path = dir.absoluteFilePath(file);
      return true;
    }
  }
  LOG_INDEX_ERROR("固件升级失败，未找到关键字{}, 文件列表: {}", _keyword, fmt::join(_file_list, ","));
  return false;
}

bool AgingWorkModel::unzipFirmware(QString _file_dir)
{
  auto firm_ware_zip_path = getFirmwareZipPath(firmware_md5_);
  if (firm_ware_zip_path.isEmpty())
  {
    return false;
  }
  QFileInfo file_info(firm_ware_zip_path);
  auto unzip_path = file_info.absolutePath() + "/" + file_info.baseName();

  if (!QDir(unzip_path).exists())
  {
    QDir().mkdir(unzip_path);
    QProcess process;
    process.start("unzip", QStringList() << "-o" << firm_ware_zip_path << "-d" << unzip_path);
    process.waitForFinished();
    if (process.exitCode() != 0)
    {
      LOG_INDEX_ERROR("固件升级失败，解压固件文件失败: {}, 错误信息: {}", firm_ware_zip_path.toStdString(),
                      process.readAllStandardError().toStdString());
      return false;
    }
  }

  QDir unzip_dir(unzip_path);
  QStringList file_list = unzip_dir.entryList(QDir::Files);

  return findAndSetPath(unzip_path, file_list, ".hs_fs", path_.firmware_app_file_path) &&
         findAndSetPath(unzip_path, file_list, "_bot_fpga_", path_.firmware_bot_file_path) &&
         findAndSetPath(unzip_path, file_list, "_top_", path_.firmware_top_file_path) &&
         findAndSetPath(unzip_path, file_list, ".txt", path_.firmware_config_file_path);
}

QString AgingWorkModel::getFirmwareZipPath(const QString& _md5)
{
  QString path;
  if (para_info_.is_use_zip_file)
  {
    path = para_info_.firmware_zip_file_path;
  }
  else
  {
    path = para_info_.firmware_dir;
  }

  if (path.isEmpty())
  {
    LOG_INDEX_ERROR("固件升级失败，当前固件路径为空");
    app()->signalShowErrorMessageBox(fmt::format("[{}] 固件升级失败，当前固件路径为空", getLogIndex()).c_str());
    return "";
  }

  if (para_info_.is_use_zip_file)
  {
    if (!QFile::exists(path))
    {
      LOG_INDEX_ERROR("固件升级失败，zip文件不存在: {}", path.toStdString());
      return "";
    }
    return path;
  }

  auto zip_md5_map = getZipMd5(path);
  if (_md5.isEmpty())
  {
    LOG_INDEX_ERROR("固件升级失败，md5为空，无法找到合适的zip文件");
    return "";
  }

  if (zip_md5_map.empty())
  {
    LOG_INDEX_ERROR("固件升级失败，未找到任何固件zip文件, 目录: {}", path);
    return "";
  }
  if (zip_md5_map.find(_md5) == zip_md5_map.end())
  {
    LOG_INDEX_ERROR("固件升级失败，未找到固件zip文件, md5: {}", _md5);
    LOG_INDEX_ERROR("未找到固件zip文件, 请检查固件路径是否正确，{}", zip_md5_map);
    return "";
  }

  LOG_INDEX_INFO("找到固件zip文件: {}, md5: {}", zip_md5_map[_md5], _md5);
  QDir dir(path);
  return dir.absoluteFilePath(zip_md5_map[_md5]);
}

std::map<QString, QString> AgingWorkModel::getZipMd5(QString _dir_path)
{
  if (_dir_path.isEmpty())
  {
    _dir_path = para_info_.firmware_dir;
  }
  if (_dir_path.isEmpty())
  {
    LOG_INDEX_ERROR("固件升级失败，当前固件路径为空, {}", _dir_path.toStdString());
    return {};
  }

  QDir dir(_dir_path);
  QStringList file_list = dir.entryList(QDir::Files);
  auto zip_file_list    = std::vector<QString>();
  for (const auto& file : file_list)
  {
    if (file.endsWith(".zip"))
    {
      zip_file_list.push_back(file);
    }
  }
  if (zip_file_list.empty())
  {
    LOG_INDEX_ERROR("固件升级失败,未找到固件zip文件, 目录: {}", _dir_path.toStdString());
    return {};
  }

  QFile info_file(dir.absoluteFilePath("firmware_info.json"));
  // 1. 读取（如果失败，json_obj 还是空）
  QJsonDocument json_doc;
  if (info_file.open(QIODevice::ReadOnly))
  {
    json_doc = QJsonDocument::fromJson(info_file.readAll());
    info_file.close();
  }

  QJsonObject json_obj = json_doc.object();
  QJsonObject new_json_obj;

  bool is_write_json = false;
  // 2. 更新缺失的 MD5
  for (const auto& zip_name : zip_file_list)
  {
    for (auto iter = json_obj.begin(); iter != json_obj.end(); ++iter)
    {
      if (iter.value().toString() == zip_name)
      {
        continue;
      }
    }
    QFile zip_file(dir.absoluteFilePath(zip_name));
    if (!zip_file.open(QIODevice::ReadOnly))
    {
      LOG_INDEX_ERROR("固件升级失败, 打开文件失败: {}", zip_file.fileName().toStdString());
      return {};
    }
    QByteArray data = zip_file.readAll();
    zip_file.close();
    QString md5       = QCryptographicHash::hash(data, QCryptographicHash::Md5).toHex();
    new_json_obj[md5] = zip_name;
    is_write_json     = true;
  }

  // 3. 如果有更新，先把 json_obj 装回 json_doc，再写回文件
  if (is_write_json)
  {
    QJsonDocument new_doc(new_json_obj);
    if (!info_file.open(QIODevice::WriteOnly | QIODevice::Truncate))
    {
      LOG_INDEX_ERROR("无法写入 firmware_info.json: {}", info_file.fileName().toStdString());
      return {};
    }
    QByteArray out = new_doc.toJson();
    // LOG_INDEX_INFO("写入固件信息: {}", out.toStdString());
    info_file.write(out);
    info_file.close();
  }

  std::map<QString, QString> zip_md5_map;
  for (const auto& key : new_json_obj.keys())
  {
    zip_md5_map[key] = new_json_obj[key].toString();
  }
  return zip_md5_map;
}

bool AgingWorkModel::checkFirmware(QString /*_file_path*/)
{
  // TODO:
  return true;
}
bool AgingWorkModel::updateFirmwareApp(QString _file_path)
{
  if (_file_path.isEmpty())
  {
    _file_path = path_.firmware_app_file_path;
  }
  if (_file_path.isEmpty())
  {
    LOG_INDEX_ERROR("固件升级失败，当前固件路径为空");
    return false;
  }

  QFileInfo file_info(_file_path);
  if (!file_info.exists())
  {
    LOG_INDEX_ERROR("固件升级失败,固件文件不存在: {}", _file_path.toStdString());
    return false;
  }

  return getLidarManager()->firmwareUpdateApp(_file_path);
}
bool AgingWorkModel::updateFirmwareBot(QString _file_path)
{
  if (_file_path.isEmpty())
  {
    if (path_.firmware_bot_file_path.isEmpty())
    {
      LOG_INDEX_ERROR("固件升级失败，当前固件路径为空");
      return false;
    }
    _file_path = path_.firmware_bot_file_path;
  }

  QFileInfo file_info(_file_path);
  if (!file_info.exists())
  {
    LOG_INDEX_ERROR("固件升级失败,固件文件不存在: {}", _file_path.toStdString());
    return false;
  }

  return getLidarManager()->firmwareUpdateBot(_file_path);
}
bool AgingWorkModel::updateFirmwareTop(QString _file_path)
{
  if (_file_path.isEmpty())
  {
    _file_path = path_.firmware_top_file_path;
  }
  if (_file_path.isEmpty())
  {
    LOG_INDEX_ERROR("固件升级失败，当前固件路径为空");
    return false;
  }

  QFileInfo file_info(_file_path);
  if (!file_info.exists())
  {
    LOG_INDEX_ERROR("固件升级失败,固件文件不存在: {}", _file_path.toStdString());
    return false;
  }

  return getLidarManager()->firmwareUpdateTop(_file_path);
}
bool AgingWorkModel::updateFirmwareWriteConfig(QString _file_path)
{
  if (_file_path.isEmpty())
  {
    _file_path = path_.firmware_config_file_path;
  }
  if (_file_path.isEmpty())
  {
    LOG_INDEX_ERROR("固件升级失败，当前固件路径为空");
    return false;
  }

  QFileInfo file_info(_file_path);
  if (!file_info.exists())
  {
    LOG_INDEX_ERROR("固件升级失败,固件文件不存在: {}", _file_path.toStdString());
    return false;
  }

  return getLidarManager()->firmwareUpdateWriteConfig(_file_path);
}
bool AgingWorkModel::updateFirmwareCheck(QString _file_path)
{
  if (_file_path.isEmpty())
  {
    _file_path = path_.firmware_config_file_path;
  }

  if (_file_path.isEmpty())
  {
    LOG_INDEX_ERROR("固件升级失败，当前固件路径为空");
    return false;
  }

  if (!para_info_.is_use_zip_file)
  {
    auto top      = limit_csv_utils_ptr_->getLimitInfo("check_top_firmware_version");
    auto bot      = limit_csv_utils_ptr_->getLimitInfo("check_bot_firmware_version");
    auto app      = limit_csv_utils_ptr_->getLimitInfo("check_app_firmware_version");
    auto motor    = limit_csv_utils_ptr_->getLimitInfo("check_motor_firmware_version");
    auto config   = limit_csv_utils_ptr_->getLimitInfo("check_config_firmware_version");
    top.min_th    = mes_top_version_;
    top.max_th    = mes_top_version_;
    bot.min_th    = mes_bot_version_;
    bot.max_th    = mes_bot_version_;
    app.min_th    = mes_app_version_;
    app.max_th    = mes_app_version_;
    motor.min_th  = mes_motor_version_;
    motor.max_th  = mes_motor_version_;
    config.min_th = mes_config_version_;
    config.max_th = mes_config_version_;

    mech::ConfigPara config_para {};
    if (!getLidarManager()->getConfigParam(config_para) && !getLidarManager()->getConfigParam(config_para))
    {
      setFailMsg("获取升级前获取参数失败");
      addMeasureMessage("fsm_firmware_update_app", false);
      updateFirmwareUpdateState(FIRMWARE_UPDATE_NG);
      return false;
    }
    uint32_t config_version = 0;
    if (!getLidarManager()->readRegDataByKey("config_version", config_version))
    {
      setFailMsg("获取升级前获取参数失败");
      addMeasureMessage("fsm_firmware_update_app", false);
      updateFirmwareUpdateState(FIRMWARE_UPDATE_NG);
      return false;
    }

    bool is_pass = true;
    is_pass &= addMeasureMessage(top, config_para.getPlVersion());
    is_pass &= addMeasureMessage(bot, config_para.getPsVersion());
    is_pass &= addMeasureMessage(app, config_para.getSoftwareVersion());
    is_pass &= addMeasureMessage(motor, config_para.getMotorVersion());
    is_pass &= addMeasureMessage(config, config_version);

    if (!is_pass)
    {
      setFailMsg("固件升级检查固件版本超限");
      updateFirmwareUpdateState(FIRMWARE_UPDATE_NG);
      return false;
    }
  }

  auto read_val_file_path = path_.data_dir.absoluteFilePath("read_config.txt");
  return getLidarManager()->firmwareUpdateCheckConfig(_file_path, read_val_file_path);
}
bool AgingWorkModel::checkBeforeFirmwareUpdate()
{
  mech::ConfigPara config_para {};
  if (!getLidarManager()->getConfigParam(config_para) && !getLidarManager()->getConfigParam(config_para))
  {
    setFailMsg("获取升级前获取参数失败");
    addMeasureMessage("fsm_firmware_update_app", false);
    updateFirmwareUpdateState(FIRMWARE_UPDATE_NG);
    return false;
  }

  addMeasureMessage("top_version_before", config_para.getPlVersion(), rsfsc_lib::MEASURE_DATA_TYPE_HEX);
  addMeasureMessage("bot_version_before", config_para.getPsVersion(), rsfsc_lib::MEASURE_DATA_TYPE_HEX);
  addMeasureMessage("app_version_before", config_para.getSoftwareVersion(), rsfsc_lib::MEASURE_DATA_TYPE_HEX);
  addMeasureMessage("motor_version_before", config_para.getMotorVersion(), rsfsc_lib::MEASURE_DATA_TYPE_HEX);

  if (!addMeasureMessage("fsm_firmware_update_motor_speed", config_para.getMotorRealTimeSpeed(),
                         rsfsc_lib::MEASURE_DATA_TYPE_INT))
  {
    setFailMsg("升级前转速检查超限");
    addMeasureMessage("fsm_firmware_update_app", false);
    updateFirmwareUpdateState(FIRMWARE_UPDATE_NG);
    app()->signalShowErrorMessageBox(
      fmt::format("升级APP前，检查到转速异常，转速为{}", config_para.getMotorRealTimeSpeed()).c_str());
    return false;
  }
  return true;
}

bool AgingWorkModel::clearCalibData()
{
  constexpr uint32_t START_ADDR                      = 0xf00000;
  constexpr uint32_t END_ADDR                        = 0xffffff;
  std::array<char, (END_ADDR - START_ADDR + 1)> data = { 0 };

  QByteArray data_array(data.data(), data.size());
  if (!getLidarManager()->writeTopFlash(data_array, START_ADDR))
  {
    setFailMsg("清除标定数据失败, all zero大小: {}kB", data.size() / 1024);
    addMeasureMessage("clear_calib_data", false);
    updateAgingState(AGING_NG);
    return false;
  }
  addMeasureMessage("clear_calib_data", true);
  LOG_INDEX_INFO("清除标定数据成功, all zero大小: {}kB, addr start: {:#x}, addr end: {:#x}", data.size() / 1024,
                 START_ADDR, END_ADDR);
  return true;
}
bool AgingWorkModel::writeExpZeroAngle()
{
  if (!getLidarManager()->writeZeroAngle(exp_zero_angle_))
  {
    LOG_INDEX_ERROR("写入经验零度角失败: {}", exp_zero_angle_);
    addMeasureMessage("write_exp_zero_angle", false);
    return false;
  }
  float read_angle = 0;
  if (!getLidarManager()->readZeroAngle(read_angle))
  {
    LOG_INDEX_ERROR("读取校验零度角失败");
    addMeasureMessage("write_exp_zero_angle", false);
    return false;
  }
  if (read_angle != exp_zero_angle_)
  {
    LOG_INDEX_ERROR("写入经验零度角失败, 写入值: {}, 读取值: {}", exp_zero_angle_, read_angle);
    addMeasureMessage("write_exp_zero_angle", false);
    return false;
  }

  addMeasureMessage("write_exp_zero_angle", true);
  return true;
}
bool AgingWorkModel::checkAgingData()
{
  DifopInfo difop_info = getLidarManager()->getLastDifopInfo();
  return checkAgingData(difop_info);
}

bool AgingWorkModel::checkAgingData(const DifopInfo& _difop_info)
{
  auto limit_name_vec = limit_csv_utils_ptr_->getLimitNameVec();
  std::map<std::string, std::string> fail_msg;
  for (const auto& key : limit_name_vec)
  {
    auto limit_info = limit_csv_utils_ptr_->getLimitInfo(key);
    if (limit_info.extra_str_info.size() < 2)
    {
      continue;
    }
    if (limit_info.extra_str_info.at(1) != "difop")
    {
      continue;
    }
    QVariant value      = _difop_info.property(key.c_str());
    std::string name_zh = limit_info.extra_str_info.at(0);
    if (!checkWithLimit(limit_info, value))
    {
      fail_msg[key] =
        fmt::format("检测到异常值, 监控项: {}, value: {}, 下限: {}, 上限: {}", name_zh, dataToString(value),
                    dataToString(limit_info.min_th, value.type()), dataToString(limit_info.max_th, value.type()));
    }
  }

  if (!fail_msg.empty())
  {
    // for (const auto& [key, msg] : fail_msg)
    // {
    //   LOG_INDEX_ERROR("{}", msg);
    // }
    return false;
  }
  return true;
}

bool AgingWorkModel::checkMsopData(const std::vector<MsopPacket96>& _msop_pkt_vec)
{
  std::map<int, int> zero_count;
  auto check_if_zero = [&](const auto& _dist_refl_low, const auto& _dist_refl_high, size_t _offset) {
    for (int j = 0; j < static_cast<int>(_dist_refl_low.size()); ++j)
    {
      int channel_num = static_cast<int>(_offset) + j + 1;
      uint16_t area   = _dist_refl_low.at(j).refl + (_dist_refl_high.at(j).refl << 8U);
      uint16_t dist   = _dist_refl_low.at(j).dist;
      // uint16_t amp    = _dist_refl_high.at(j).dist;
      if (dist == 0 && area == 0)
      {
        zero_count[channel_num]++;
      }
    }
  };
  for (const auto& msop_packet : _msop_pkt_vec)
  {
    if (msop_packet.pkt_head != MSOP_PKT_HEAD)
    {
      LOG_INDEX_ERROR("pkt_head error: 0x{:x}", msop_packet.pkt_head);
      continue;
    }
    for (size_t i = 0; i < (msop_packet.data_block.size() - 1); i += 2)
    {
      const auto& block_low  = msop_packet.data_block.at(i);
      const auto& block_high = msop_packet.data_block.at(i + 1);

      check_if_zero(block_low.dist_refl1, block_high.dist_refl1, 0);
      check_if_zero(block_low.dist_refl2, block_high.dist_refl2, block_low.dist_refl1.size());
    }
  }
  bool is_data_normal = true;
  for (const auto& [channel_num, count] : zero_count)
  {
    if (count >= static_cast<int>(_msop_pkt_vec.size()))
    {
      LOG_INDEX_ERROR("通道{}存在连续{}个包的距离与面积都为零", channel_num, count);
      is_data_normal = false;
    }
  }
  return is_data_normal;
}
int AgingWorkModel::getPastDifopTime()
{
  DifopInfo difop_info = getLidarManager()->getLastDifopInfo();
  if (difop_info.getDateTime().isValid())
  {
    return -1;
  }
  return static_cast<int>(difop_info.getDateTime().secsTo(QDateTime::currentDateTime()));
}

bool AgingWorkModel::appendAgingData()
{
  DifopInfo difop_info = getLidarManager()->getLastDifopInfo();
  if (difop_info.getDateTime() == last_difop_time_)
  {
    LOG_INDEX_ERROR("未接收到新的difop包");
    return false;
  }
  last_difop_time_    = difop_info.getDateTime();
  auto limit_name_vec = limit_csv_utils_ptr_->getLimitNameVec();

  QStringList state_list;
  QStringList value_list;

  value_list.append(QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss"));
  state_list.append("");
  for (const auto& key : limit_name_vec)
  {
    auto limit_info = limit_csv_utils_ptr_->getLimitInfo(key);
    if (limit_info.extra_str_info.size() < 2)
    {
      continue;
    }
    if (limit_info.extra_str_info.at(1) != "difop")
    {
      continue;
    }
    QVariant value = difop_info.property(key.c_str());
    value_list.append(dataToString(value));
    state_list.append(checkWithLimit(limit_info, value) ? "PASS" : "FAIL");
  }

  auto up_err_rate_limit_info   = limit_csv_utils_ptr_->getLimitInfo("fsm_up_error_rate");
  auto down_err_rate_limit_info = limit_csv_utils_ptr_->getLimitInfo("fsm_down_error_rate");
  double up_error_rate          = NAN;
  double down_error_rate        = NAN;
  getLidarManager()->getOpticalErrorRate(up_error_rate, down_error_rate);
  value_list.append(QString::number(up_error_rate));
  value_list.append(QString::number(down_error_rate));
  state_list.append(checkWithLimit(up_err_rate_limit_info, up_error_rate) ? "PASS" : "FAIL");
  state_list.append(checkWithLimit(down_err_rate_limit_info, down_error_rate) ? "PASS" : "FAIL");

  QFile file(path_.data_dir.absoluteFilePath("aging_data.csv"));
  QTextStream stream(&file);

  // 检查文件是否存在
  if (!file.exists())
  {
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text))
    {
      LOG_INDEX_ERROR("打开文件失败: {}", file.fileName().toStdString());
      return false;
    }

    // 写入初始信息
    QString lidar_sn      = getLidarManager()->getLidarInfoSn();
    QString aging_version = app()->getVersionStr();
    QString bot_version   = QString("0x%1").arg(QString::number(difop_info.getBotFirmwareVersion(), 16));
    QString top_version   = QString("0x%1").arg(QString::number(difop_info.getTopFirmwareVersion(), 16));
    QString motor_version = QString("0x%1").arg(QString::number(difop_info.getMotorFirmwareVersion(), 16));

    stream << "Lidar SN," << lidar_sn << "\r\n";
    stream << "AGING_VERSION," << aging_version << "\r\n";
    stream << "BOT_VERSION," << bot_version << "\r\n";
    stream << "TOP_VERSION," << top_version << "\r\n";
    stream << "MOTOR_VERSION," << motor_version << "\r\n";

    QStringList limit_title = { "NO", "Item", "Unit", "USL", "LSL" };
    stream << limit_title.join(",") << "\r\n";

    int count = 1;
    QStringList limit_str_list;
    for (const auto& key : limit_name_vec)
    {
      auto limit_info = limit_csv_utils_ptr_->getLimitInfo(key);
      if (limit_info.extra_str_info.size() < 2)
      {
        continue;
      }
      if (limit_info.extra_str_info.at(1) != "difop")
      {
        continue;
      }
      QVariant value = difop_info.property(key.c_str());
      QString min_th = QString::number(limit_info.min_th);
      QString max_th = QString::number(limit_info.max_th);
      if (static_cast<QMetaType::Type>(value.type()) == QMetaType::UInt)
      {
        if (min_th != "nan")
        {
          min_th = QString::fromStdString(fmt::format("{:#x}", static_cast<uint32_t>(limit_info.min_th)));
        }
        if (max_th != "nan")
        {
          max_th = QString::fromStdString(fmt::format("{:#x}", static_cast<uint32_t>(limit_info.max_th)));
        }
      }
      QStringList limit_data = { QString::number(count++), QString::fromStdString(key),
                                 QString::fromStdString(limit_info.getUnit()), max_th, min_th };
      stream << limit_data.join(",") << "\r\n";
      limit_str_list.append(QString::fromStdString(key));
    }
    limit_str_list.append("up_error_rate");
    limit_str_list.append("down_error_rate");

    stream << "\r\n";
    stream << "datetime," << limit_str_list.join(",") << "\r\n";
    stream << "\r\n";
    file.close();
  }

  // 处理已有文件：读取内容，去除最后一行
  if (!file.open(QIODevice::ReadWrite | QIODevice::Text))
  {
    LOG_INDEX_ERROR("打开文件失败: {}", file.fileName().toStdString());
    return false;
  }

  QStringList file_content;
  QTextStream inp(&file);
  while (!inp.atEnd())
  {
    QString line = inp.readLine();
    file_content.append(line);
  }

  // 移除最后一行
  if (!file_content.isEmpty())
  {
    file_content.removeLast();
  }

  file.resize(0);                                 // 清空文件内容以便写入
  stream << file_content.join("\r\n") << "\r\n";  // 重新写入文件内容

  // 写入新数据
  stream << value_list.join(",") << "\r\n";
  stream << state_list.join(",") << "\r\n";

  return true;
}
bool AgingWorkModel::addDifopMonitorResult() { return addMeasureMessage(getLidarManager()->getLastDifopInfo()); }
bool AgingWorkModel::addMeasureMessage(const DifopInfo& _difop_info)
{
  auto limit_name_vec = limit_csv_utils_ptr_->getLimitNameVec();
  std::map<std::string, std::string> fail_msg;
  for (const auto& key : limit_name_vec)
  {
    auto limit_info = limit_csv_utils_ptr_->getLimitInfo(key);
    if (limit_info.extra_str_info.size() < 2)
    {
      continue;
    }
    if (limit_info.extra_str_info.at(1) != "difop")
    {
      continue;
    }
    QVariant value      = _difop_info.property(key.c_str());
    std::string name_zh = limit_info.extra_str_info.at(0);
    if (!addMeasureMessage(limit_info, value))
    {
      fail_msg[key] =
        fmt::format("检测到异常值, 监控项: {}, value: {}, 下限: {}, 上限: {}", name_zh, dataToString(value),
                    dataToString(limit_info.min_th, value.type()), dataToString(limit_info.max_th, value.type()));
    }
  }

  if (!fail_msg.empty())
  {
    for (const auto& [key, msg] : fail_msg)
    {
      LOG_INDEX_ERROR("{}", msg);
    }
    return false;
  }
  return true;
}

bool AgingWorkModel::saveEncodeCalibData(const std::vector<std::vector<uint32_t>>& _data)
{
  QFile file(path_.data_dir.absoluteFilePath(path_.scale_file_name));
  if (!file.open(QIODevice::WriteOnly | QIODevice::Text))
  {
    LOG_INDEX_ERROR("打开文件失败: {}", file.fileName().toStdString());
    return false;
  }
  QTextStream stream(&file);
  for (const auto& data : _data)
  {
    for (auto it = data.begin(); it != data.end(); ++it)
    {
      if (it != data.begin())
      {
        stream << ",";
      }
      stream << *it;
    }
    stream << "\r\n";
  }
  file.close();
  return true;
}

std::vector<std::vector<uint32_t>> AgingWorkModel::loadEncodeCalibData()
{
  std::vector<std::vector<uint32_t>> res;
  QFile file(path_.data_dir.absoluteFilePath(path_.scale_file_name));
  if (!file.open(QIODevice::ReadOnly | QIODevice::Text))
  {
    LOG_INDEX_ERROR("打开文件失败: {}", file.fileName().toStdString());
    return {};
  }
  QTextStream stream(&file);
  while (!stream.atEnd())
  {
    QString line = stream.readLine();
    if (line.isEmpty())
    {
      continue;
    }
    QStringList str_list = line.split(",");
    std::vector<uint32_t> data;
    for (const auto& str : str_list)
    {
      bool is_ok     = false;
      uint32_t value = str.toUInt(&is_ok);
      if (!is_ok)
      {
        LOG_INDEX_ERROR("数据转换失败: {}", str.toStdString());
        continue;
      }
      data.push_back(value);
    }
    res.push_back(data);
  }
  file.close();
  return res;
}

bool AgingWorkModel::processEncodeCalibData(const std::vector<std::vector<uint32_t>>& _data,
                                            EncodCalibData& _encod_calib_data)
{
  if (_data.empty())
  {
    LOG_INDEX_ERROR("数据为空，码盘标定处理失败");
    return false;
  }
  auto scale_data_size = _data.at(0).size();
  std::vector<double> scale_avg_data(scale_data_size);
  for (size_t col = 0; col < scale_data_size; ++col)
  {
    double sum = 0;
    for (const auto& each : _data)
    {
      sum += each.at(col);
    }
    scale_avg_data.at(col) = (sum / static_cast<double>(_data.size()));
  }

  std::vector<double> calibration_scale_coeff(scale_data_size);
  calibration_scale_coeff.at(0) = scale_avg_data.at(0) / scale_avg_data.at(scale_data_size - 1) * 65536.;
  for (size_t i = 1; i < scale_avg_data.size(); ++i)
  {
    calibration_scale_coeff.at(i) = scale_avg_data.at(i) / scale_avg_data.at(i - 1) * 65536.;
  }

  double avg_sum = std::accumulate(scale_avg_data.begin(), scale_avg_data.end(), 0.0);
  std::vector<double> angle_insert_step(scale_data_size);
  for (size_t i = 0; i < scale_data_size; ++i)
  {
    angle_insert_step.at(i) = (scale_avg_data.at(i) / avg_sum) * 100. * 32768.;
  }
  angle_insert_step.at(scale_data_size - 1) = angle_insert_step.back() / 2.;

  _encod_calib_data.scale_avg.clear();
  _encod_calib_data.calibration_scale_coeff.clear();
  _encod_calib_data.angle_insert_step.clear();

  for (const auto& val : scale_avg_data)
  {
    if (val < 0)
    {
      LOG_INDEX_ERROR("码盘标定数据异常，标定系数小于0或大于65535, value: {}", val);
      return false;
    }
    _encod_calib_data.scale_avg.push_back(static_cast<uint32_t>(std::lround(val)));
  }

  for (const auto& val : calibration_scale_coeff)
  {
    if (val < 0)
    {
      LOG_INDEX_ERROR("码盘标定数据异常，标定系数小于0或大于65535, value: {}", val);
      return false;
    }
    _encod_calib_data.calibration_scale_coeff.push_back(static_cast<uint32_t>(std::lround(val)));
  }

  for (const auto& val : angle_insert_step)
  {
    if (val < 0)
    {
      LOG_INDEX_ERROR("码盘标定数据异常，角度插值步长小于0或大于65535, value: {}", val);
      return false;
    }
    _encod_calib_data.angle_insert_step.push_back(static_cast<uint32_t>(std::lround(val)));
  }

  if (!saveEncodeCalibData(_encod_calib_data, "process"))
  {
    LOG_INDEX_ERROR("保存码盘标定数据失败");
    return false;
  }

  return true;
}

bool AgingWorkModel::saveEncodeCalibData(const EncodCalibData& _encod_calib_data, const QString& _file_name_suffix)
{
  QFile file(path_.data_dir.absoluteFilePath("encode_calib_data_" + _file_name_suffix + ".csv"));
  if (!file.open(QIODevice::WriteOnly | QIODevice::Text))
  {
    LOG_INDEX_ERROR("打开文件失败: {}", file.fileName().toStdString());
    return false;
  }
  QTextStream stream(&file);
  auto save_data = [&](const std::vector<uint32_t>& _res_data, const QString& _name) {
    stream << _name;
    for (const auto& val : _res_data)
    {
      stream << "," << val;
    }
    stream << "\r\n";
  };

  save_data(_encod_calib_data.scale_avg, "scale_avg");
  save_data(_encod_calib_data.calibration_scale_coeff, "calibration_scale_coeff");
  save_data(_encod_calib_data.angle_insert_step, "angle_insert_step");
  file.close();
  return true;
}

bool AgingWorkModel::checkEncodeCalibData(const EncodCalibData& _encod_calib_data)
{
  // 校验回读的寄存器数值
  double inter_angle = 36000.0 / static_cast<double>(_encod_calib_data.angle_insert_step.size() + 1);
  double sum =
    std::accumulate(_encod_calib_data.angle_insert_step.begin(), _encod_calib_data.angle_insert_step.end(), 0.0);
  sum += _encod_calib_data.angle_insert_step.back();
  double angle = inter_angle * sum / 32768;
  if (angle < 35999 || angle > 36001)
  {
    LOG_INDEX_ERROR("检验标定后角度和不对, 值为 {:.3f}. 请重新标定", angle);
    return false;
  }
  LOG_INDEX_INFO("检验标定后角度和为 {:.3f}", angle);

  if (_encod_calib_data.calibration_scale_coeff.size() < 99)
  {
    LOG_INDEX_ERROR("码盘标定系数数量小于99, 请重新标定");
    return false;
  }

  double coeff = _encod_calib_data.calibration_scale_coeff.at(0);
  for (size_t i = 1; i < _encod_calib_data.calibration_scale_coeff.size(); ++i)
  {
    double curr_coeff = _encod_calib_data.calibration_scale_coeff.at(i);
    curr_coeff /= 65536.0;
    coeff *= curr_coeff;
  }
  // double epsilon = 5;
  // if (coeff < 65536 - epsilon || coeff > 65536 + epsilon)
  // {
  //   LOG_INDEX_ERROR("检验标定后系数和不对, 值为 {:.3f}", coeff);
  //   return false;
  // }
  LOG_INDEX_INFO("检验标定后系数和为 {:.3f}", coeff);
  return true;
}

bool AgingWorkModel::requireVerAngleData()
{
  auto ask_type     = rsfsc_lib::AskData::ASK_TYPE_CALIB_ASK_SN_TO_SUB_SN_DATA;
  auto station_name = std::string("BT220D");
  std::map<rsfsc_lib::AskData::AskType, std::vector<rsfsc_lib::AskData>> ads = {
    { ask_type, { rsfsc_lib::AskData(station_name, {}) } }
  };
  for (int i = 1; i <= 96; ++i)
  {
    std::string item_name = fmt::format("ch{}_ver", i);
    ads[ask_type].at(0).items.emplace(item_name, fmt::format("item{}", i));
  }
  ads[ask_type].at(0).items.emplace("JT", "SubType");

  if (!app()->getWidgetLogSetting()->requireData(getLidarIndex(), ads) || ads.find(ask_type) == ads.end() ||
      ads[ask_type].empty() || ads[ask_type].front().items["ch1_ver"].empty())
  {
    setFailMsg("从MES获取垂直角度失败");
    addMeasureMessage("require_ver_angle", false);
    return false;
  }
  auto ask_ver_angle = ads[ask_type];

  ver_angle_vec_.clear();
  for (int chn_num = 1; chn_num <= 96; ++chn_num)
  {
    auto ver_angle_q_str = QString::fromStdString(ask_ver_angle.front().items[fmt::format("ch{}_ver", chn_num)]);
    QStringList ver_angle_q_str_list = ver_angle_q_str.split("_");
    if (ver_angle_q_str_list.size() != 4 || ver_angle_q_str_list.at(3).toUpper() != "P")
    {
      setFailMsg("垂直角度数据格式错误, raw: {}", ver_angle_q_str);
      addMeasureMessage("require_ver_angle", false);
      return false;
    }
    auto ver_angle_float = ver_angle_q_str_list.at(0).toFloat();
    ver_angle_vec_.push_back(ver_angle_float);
  }

  auto angle_error_max = limit_csv_utils_ptr_->getLimitInfo("ver_angle_error");
  if (!angle_error_max.is_ok)
  {
    setFailMsg("从CSV获取垂直角度误差最大值失败");
    addMeasureMessage("require_ver_angle", false);
    return false;
  }

  std::vector<float> ver_angle_vec_error_exceed {};
  for (size_t i = 0; i < ver_angle_vec_.size(); ++i)
  {
    auto angle_error = sim_ver_angle_vec_.at(i) - ver_angle_vec_.at(i);
    if (!checkWithLimit(angle_error_max, angle_error))
    {
      angle_error_max.setNameSuffix(fmt::format("_ch{}", i + 1));
      addMeasureMessage(angle_error_max, angle_error);
      ver_angle_vec_error_exceed.push_back(angle_error);
    }
  }
  if (!ver_angle_vec_error_exceed.empty())
  {
    LOG_INDEX_WARN("AA垂直角度超差{}: {}", angle_error_max.max_th, ver_angle_vec_error_exceed);
    LOG_INDEX_INFO("选择写入roc+1.4通道角");
    ver_angle_vec_ = exp_ver_angle_vec_;
  }

  LOG_INDEX_INFO("选择写入垂直角度: [{:.3f}]", fmt::join(ver_angle_vec_.begin(), ver_angle_vec_.end(), ","));
  return true;
}

bool AgingWorkModel::writeChnAngleData()
{
  if (!addMeasureMessage("write_chn_angle", getLidarManager()->writeChnAngle(ver_angle_vec_, hor_angle_vec_)))
  {
    setFailMsg("写入通道角度失败");
    addMeasureMessage("check_chn_angle", false);
    updateAgingState(AGING_NG);
    return false;
  }

  std::vector<float> read_ver_angle_vec;
  std::vector<float> read_hor_angle_vec;

  if (!getLidarManager()->readChnAngle(read_ver_angle_vec, read_hor_angle_vec, ver_angle_vec_.size()))
  {
    setFailMsg("读取通道角度失败");
    addMeasureMessage("check_chn_angle", false);
    updateAgingState(AGING_NG);
    return false;
  }

  QFile file(path_.chn_angle_file_path);
  if (!file.open(QIODevice::WriteOnly | QIODevice::Text))
  {
    setFailMsg("打开文件失败: {}", file.fileName());
    addMeasureMessage("check_chn_angle", false);
    updateAgingState(AGING_NG);
    return false;
  }
  QTextStream stream(&file);
  stream << "垂直角度,水平角度\n";
  for (size_t i = 0; i < ver_angle_vec_.size(); ++i)
  {
    stream << ver_angle_vec_.at(i) << "," << hor_angle_vec_.at(i) << "\n";
  }
  stream << "read chn angle\n";
  for (size_t i = 0; i < read_ver_angle_vec.size(); ++i)
  {
    stream << read_ver_angle_vec.at(i) << "," << read_hor_angle_vec.at(i) << "\n";
  }
  file.close();

  if (read_ver_angle_vec != ver_angle_vec_ || read_hor_angle_vec != hor_angle_vec_)
  {
    setFailMsg("通道角度校验失败");
    addMeasureMessage("check_chn_angle", false);
    updateAgingState(AGING_NG);
    return false;
  }
  addMeasureMessage("check_chn_angle", true);

  return true;
}

bool AgingWorkModel::requiredVbdData()
{
  std::string station_name   = "BT255D";
  std::string test_item_name = "vbd_voltage";
  auto ask_type              = rsfsc_lib::AskData::ASK_TYPE_CALIB_ASK_SN_TO_SUB_SN_DATA;
  std::map<rsfsc_lib::AskData::AskType, std::vector<rsfsc_lib::AskData>> ads = {
    { ask_type,
      {
        rsfsc_lib::AskData(station_name, { test_item_name }),
      } }
  };
  if (!app()->getWidgetLogSetting()->requireData(getLidarIndex(), ads) || ads.find(ask_type) == ads.end() ||
      ads[ask_type].empty() || ads[ask_type].front().items[test_item_name].empty())
  {
    setFailMsg("从MES获取VBD电压失败, sn: {}", getLidarManager()->getLidarInfo()->getLidarSN());
    addMeasureMessage("require_vbd_vol", false);
    return false;
  }
  auto ask_sn            = ads[ask_type];
  auto vbd_voltage_q_str = QString::fromStdString(ask_sn.front().items[test_item_name]);

  // raw: -20.735_-20.400_-21.300_P
  QStringList vbd_voltage_q_str_list = vbd_voltage_q_str.split("_");
  if (vbd_voltage_q_str_list.size() != 4 || vbd_voltage_q_str_list.at(3).toUpper() != "P")
  {
    setFailMsg("VBD电压数据格式错误, raw: {}", vbd_voltage_q_str);
    addMeasureMessage("require_vbd_vol", false);
    return false;
  }

  vbd_voltage_ = vbd_voltage_q_str_list.at(0).toFloat();
  LOG_INDEX_INFO("VBD电压: {}, raw: {}", vbd_voltage_, vbd_voltage_q_str);
  if (!addMeasureMessage("vbd_voltage", vbd_voltage_, rsfsc_lib::MEASURE_DATA_TYPE_FLOAT))
  {
    setFailMsg("VBD电压读取时检测超限, raw: {}", vbd_voltage_q_str);
    return false;
  }

  return true;
}

float bcdToFloat(const uint16_t _bcd_val)
{
  // 1. 分离高 8 位和低 8 位
  uint8_t high = (_bcd_val >> 8U) & 0xFFU;  // 高 8 位（整数部分）
  uint8_t low  = _bcd_val & 0xFFU;          // 低 8 位（小数部分）

  // 2. 将高 8 位 BCD 转换为整数
  int integer_part = ((high >> 4U) * 10) + (high & 0x0FU);

  // 3. 将低 8 位 BCD 转换为小数
  float fractional_part = ((low >> 4U) * 10.F) + (low & 0x0FU);
  fractional_part /= 100.0F;  // 小数部分需要除以 100

  // 4. 合并整数和小数部分
  return static_cast<float>(integer_part) + fractional_part;
}

bool AgingWorkModel::readVbdCurveAndCalData()
{
  uint16_t vbd_v0 = 0;
  uint16_t vbd_v1 = 0;
  uint16_t vbd_v2 = 0;
  if (!getLidarManager()->readVbdCurve(vbd_v0, vbd_v1, vbd_v2))
  {
    setFailMsg("读取VBD曲线失败");
    updateAgingState(AGING_NG);
    addMeasureMessage("vbd_read_data", false);
    return false;
  }
  double vbd_err            = vbd_voltage_ - (-20.51802);
  double vbd_err_trans      = std::clamp(vbd_err, -1.0, 1.0 - (1.0 / 32768.0F));
  int16_t vbd_err_trans_int = static_cast<int16_t>(std::round(vbd_err_trans * 32768.0));
  vbd_err_hex_              = static_cast<uint16_t>(vbd_err_trans_int);
  LOG_INDEX_INFO("VBD误差计算值vbd_err: {:.3f}, vbd_err_hex_: {:#x}", vbd_err, vbd_err_hex_);

  auto vbd_v0_float = bcdToFloat(vbd_v0);
  auto vbd_v1_float = bcdToFloat(vbd_v1);
  auto vbd_v2_float = bcdToFloat(vbd_v2);
  LOG_INDEX_INFO("VBD曲线值vbd_v0: {:.3f}, vbd_v1: {:.3f}, vbd_v2: {:.3f}", vbd_v0_float, vbd_v1_float, vbd_v2_float);

  double v3_50       = ((vbd_v1_float - vbd_v0_float) / 125 * 90) + vbd_v0_float;
  auto v_t           = -v3_50 - 0.36F - vbd_err;
  auto vbd_intercept = ((-8.12 - v_t) * 1000 / 3.3 / 5.4545) - (vbd_cal_k_ * 90);
  vbd_intercept_hex_ = static_cast<uint32_t>(std::round(vbd_intercept * 256));
  LOG_INDEX_INFO("VBD截距计算值vbd_intercept: {:.3f}, vbd_intercept_hex_: {:#x}", vbd_intercept, vbd_intercept_hex_);

  addMeasureMessage("vbd_read_data", true);
  return true;
}

bool AgingWorkModel::writeVbdData()
{
  CustomGdiFeild custom_gdi_feild {};
  auto& custom_gdi_reg = custom_gdi_feild.custom_gdi_reg;
  custom_gdi_reg.vbd_err.setData(0x8c3200, vbd_err_hex_);
  custom_gdi_reg.vbd_intercept.setData(0x8c3018, vbd_intercept_hex_);
  custom_gdi_reg.refl_calib_version_reg.setData(0x8c212d, 0x02);
  custom_gdi_feild.gdi_crc = CrcUtils::calcCrc(custom_gdi_reg.arr.data(), sizeof(CustomGdiReg));

  QFile file(path_.vbd_file_path);
  if (!file.open(QIODevice::WriteOnly))
  {
    setFailMsg("打开文件失败: {}", file.fileName());
    addMeasureMessage("vbd_write_data", false);
    updateAgingState(AGING_NG);
    return false;
  }
  file.write(custom_gdi_feild.arr.data(), sizeof(CustomGdiFeild));
  file.close();

  QByteArray data(custom_gdi_feild.arr.data(), sizeof(CustomGdiFeild));
  if (!getLidarManager()->writeTopFlash(data, CustomGdiFeild::getStartAddr()))
  {
    setFailMsg("写入TopFlash失败");
    addMeasureMessage("vbd_write_data", false);
    updateAgingState(AGING_NG);
    return false;
  }
  addMeasureMessage("vbd_write_data", true);
  return true;
}

bool AgingWorkModel::checkVbdData()
{
  uint32_t vbd_intercept_hex = 0;
  uint32_t vbd_err_hex       = 0;
  if (!getLidarManager()->readVbd(vbd_intercept_hex, vbd_err_hex))
  {
    setFailMsg("读取VBD数据失败");
    addMeasureMessage("vbd_check_data", false);
    updateAgingState(AGING_NG);
    return false;
  }
  LOG_INDEX_INFO("回读VBD截距: {:#x}, 回读VBD误差: {:#x}", vbd_intercept_hex, vbd_err_hex);
  uint32_t calib_status = 0;
  if (!getLidarManager()->readTopRegDataByKey("calib_status", calib_status, 1))
  {
    LOG_ERROR("读取calib_status失败");
    addMeasureMessage("vbd_check_data", false);
    updateAgingState(AGING_NG);
    return false;
  }

  if (vbd_intercept_hex != vbd_intercept_hex_ || vbd_err_hex != vbd_err_hex_)
  {
    setFailMsg("VBD数据校验失败，回读值与写入值不一致，写入值vbd_intercept_hex_: {:#x}, vbd_err_hex_: {:#x}",
               vbd_intercept_hex_, vbd_err_hex_);
    addMeasureMessage("vbd_check_data", false);
    updateAgingState(AGING_NG);
    return false;
  }

  // bit0 反标, bit1 动标, bit2 静标, bit3 近距离, bit4 gdi, bit5 绝标
  // LOG_INFO("calib_status: {:#x}", calib_status);
  LOG_INFO("CRC 反标: {}, 动标: {}, 静标: {}, 近距离: {}, GDI: {}, 绝标: {}", calib_status & 0x01U,
           (calib_status >> 1U) & 0x01U, (calib_status >> 2U) & 0x01U, (calib_status >> 3U) & 0x01U,
           (calib_status >> 4U) & 0x01U, (calib_status >> 5U) & 0x01U);

  if (!addMeasureMessage("vbd_calib_status", ((calib_status >> 4U) & 0x01U) != 0U))
  {
    setFailMsg("VBD标定CRC校验失败");
    addMeasureMessage("vbd_check_data", false);
    updateAgingState(AGING_NG);
    return false;
  }

  addMeasureMessage("vbd_check_data", true);
  return true;
}

bool AgingWorkModel::requireCustomerInfo()
{
  if (auto customer_sn = mech::requireCustomerSN(app()->getWidgetLogSetting(), getLidarIndex()))
  {
    getLidarManager()->setMesMacAddress(customer_sn->mac_addr);
    mes_top_version_    = customer_sn->top_version;
    mes_bot_version_    = customer_sn->bot_version;
    mes_app_version_    = customer_sn->app_version;
    mes_motor_version_  = customer_sn->motor_version;
    mes_config_version_ = customer_sn->config_version;
    firmware_md5_       = customer_sn->firmware_md5.c_str();
    return true;
  }

  setFailMsg("从MES获取客户信息失败");
  addMeasureMessage("require_customer_info", false);
  updateAgingState(AGING_NG);
  return false;
}

bool AgingWorkModel::checkWithLimit(const LimitInfo& _limit_info, const QVariant& _value)
{
  if (!_limit_info.is_ok)
  {
    LOG_INDEX_ERROR("limit信息错误 key: {}", _limit_info.getName());
    return false;
  }
  if (!_value.isValid())
  {
    LOG_INDEX_ERROR("QVariant value key 无效: {}", _limit_info.getName());
    return false;
  }

  switch (static_cast<QMetaType::Type>(_value.type()))
  {
  case QMetaType::Int:
  {
    return rsfsc_lib::WidgetLogSetting::checkWithinLimit(_limit_info, _value.toInt(),
                                                         rsfsc_lib::MeasureDataType::MEASURE_DATA_TYPE_INT);
    break;
  }
  case QMetaType::UInt:
  {
    return rsfsc_lib::WidgetLogSetting::checkWithinLimit(_limit_info, _value.toUInt(),
                                                         rsfsc_lib::MeasureDataType::MEASURE_DATA_TYPE_HEX);
    break;
  }
  case QMetaType::Double:
  case QMetaType::Float:
  {
    return rsfsc_lib::WidgetLogSetting::checkWithinLimit(_limit_info, _value.toDouble(),
                                                         rsfsc_lib::MeasureDataType::MEASURE_DATA_TYPE_FLOAT);
    break;
  }
  case QMetaType::QString:
  {
    return rsfsc_lib::WidgetLogSetting::checkWithinLimit(_limit_info, _value.toString().toStdString());
    break;
  }
  default:
  {
    LOG_INDEX_ERROR("未知的数据类型name: {}, type name: {},", _limit_info.getName(), _value.typeName());
    return false;
  }
  break;
  }
  return true;
}
bool AgingWorkModel::addMeasureMessage(const LimitInfo& _limit_info, const QVariant& _value)
{
  if (!_limit_info.is_ok)
  {
    LOG_INDEX_ERROR("limit信息错误 key: {}", _limit_info.getName());
    return false;
  }
  if (!_value.isValid())
  {
    LOG_INDEX_ERROR("QVariant value key 无效: {}", _limit_info.getName());
    return false;
  }

  std::string content;
  switch (static_cast<QMetaType::Type>(_value.type()))
  {
  case QMetaType::Int:
  {
    content = fmt::format("{}", _value.toInt());
    return app()->getWidgetLogSetting()->addMeasureMessage(getLidarIndex(), _limit_info, _value.toInt(),
                                                           rsfsc_lib::MeasureDataType::MEASURE_DATA_TYPE_INT);
    break;
  }
  case QMetaType::UInt:
  {
    content = fmt::format("{:#x}", _value.toUInt());
    return app()->getWidgetLogSetting()->addMeasureMessage(getLidarIndex(), _limit_info, _value.toUInt(),
                                                           rsfsc_lib::MeasureDataType::MEASURE_DATA_TYPE_HEX);
    break;
  }
  case QMetaType::Double:
  case QMetaType::Float:
  {
    content = fmt::format("{:.12f}", _value.toDouble());
    return app()->getWidgetLogSetting()->addMeasureMessage(getLidarIndex(), _limit_info, _value.toDouble(),
                                                           rsfsc_lib::MeasureDataType::MEASURE_DATA_TYPE_FLOAT);
    break;
  }
  case QMetaType::QString:
  {
    content = _value.toString().toStdString();
    return app()->getWidgetLogSetting()->addMeasureMessage(getLidarIndex(), _limit_info,
                                                           _value.toString().toStdString());
    break;
  }
  default:
  {
    LOG_INDEX_ERROR("未知的数据类型name: {}, type name: {},", _limit_info.getName(), _value.typeName());
    return false;
  }
  break;
  }

  LOG_INDEX_INFO("addMeasure [{}:{}]", _limit_info.getName(), content);
  return true;
}

QString AgingWorkModel::dataToString(const QVariant& _value) { return dataToString(_value, _value.type()); }

QString AgingWorkModel::dataToString(const QVariant& _value, const QVariant::Type& _type)
{
  bool is_ok = false;
  _value.toDouble(&is_ok);
  if (is_ok && std::isnan(_value.toDouble()))
  {
    return "nan";
  }
  switch (static_cast<QMetaType::Type>(_type))
  {
  case QMetaType::Int:
  {
    return _value.toString();
    break;
  }
  case QMetaType::UInt:
  {
    return QString::fromStdString(fmt::format("{:#x}", _value.toUInt()));
    break;
  }
  case QMetaType::Double:
  case QMetaType::Float:
  {
    return QString::number(_value.toDouble(), 'f', 3);
    break;
  }
  case QMetaType::QString:
  {
    return _value.toString();
    break;
  }
  default:
  {
    return _value.toString();
  }
  break;
  }

  return "nan";
}

void AgingWorkModel::updateStressCount(const int _count)
{
  // TODO: 获取到widget，调用更新压测次数函数
}
bool AgingWorkModel::startTcpdumpBothOrgAndObjIPExcludeMSOP()
{
  return getLidarManager()->startTcpdumpBothOrgAndObjIPExcludeMSOP(path_.connect_dump_file_path.toStdString());
}
bool AgingWorkModel::startTcpdumpExcludeMSOP()
{
  return getLidarManager()->startTcpdumpExcludeMSOP(path_.exclude_msop_dump_file_path.toStdString());
}

bool AgingWorkModel::startTcpdumpOnlyMSOP()
{
  return getLidarManager()->startTcpdumpOnlyMSOP(path_.only_msop_dump_file_path.toStdString());
}
void AgingWorkModel::stopTcpdumpExcludeMSOP() { getLidarManager()->stopTcpdumpExcludeMSOP(); }
void AgingWorkModel::stopTcpdumpOnlyMSOP() { getLidarManager()->stopTcpdumpOnlyMSOP(); }

}  // namespace lidar
}  // namespace robosense