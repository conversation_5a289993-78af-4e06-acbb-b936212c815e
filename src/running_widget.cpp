﻿
/******************************************************************************
 * Copyright 2020 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#include "running_widget.h"
#include "app_event.h"

#include "config.h"
#include "data_struct.h"
#include "mes_widget.h"
#include "relay_controller.h"
#include "rsfsc_log/rsfsc_log_macro.h"
#include <QtWidgets>
#include <rsfsc_msg.h>

// NOLINTNEXTLINE(modernize-concat-nested-namespaces)
namespace robosense
{
namespace lidar
{

RunningWidget::RunningWidget(int _lidar_index, ProtocolType _lidar_type, QWidget* _parent) :
  QWidget(_parent), lidar_type_(_lidar_type), lidar_index_(_lidar_index)
{
  //this->setAttribute(Qt::WA_DeleteOnClose);
  widget_lidar_info_ = new robosense::lidar::rsfsc_lib::WidgetLidarInfo(PROJECT_NAME, lidar_index_, this);
  widget_lidar_info_->setLidarSNPos(1, 1, 1, 1);
  // widget_lidar_info_->setProjectCodePos(1, 2, 1, 1, false);
  widget_lidar_info_->setIPPos(1, 3, 1, 1);
  widget_lidar_info_->setMSOPPos(1, 4, 1, 1);
  widget_lidar_info_->setDIFOPPos(1, 5, 1, 1);
  // widget_lidar_info_->setLidarPositionPos(1, 5, 1, 1);
  widget_lidar_info_->setIPandPortAccording2Index();
  MesWidget::getInst()->getWidgetLogSettingPtr()->registerWidgetLidarInfo(widget_lidar_info_);
  rsfsc_lib::WidgetLidarInfo::setNotCheckSNProject("0350;0351;0352;0360");
  widget_lidar_info_->setFixedLidarInstallPosition(rsfsc_lib::LIDAR_INSTALL_POSITION_FL);

  one_key_run_button_ = new QPushButton("开始老化", this);
  connect(widget_lidar_info_, &robosense::lidar::rsfsc_lib::WidgetLidarInfo::signalLidarNameInputFinished, this,
          &RunningWidget::slotStartAgingRunning);
  connect(one_key_run_button_, &QPushButton::clicked, this, &RunningWidget::slotStartAgingRunning);
  connect(this, &RunningWidget::signalEnterPressed, this, &RunningWidget::slotStartAgingRunning);

  test_relay_button_ = new QCheckBox("测试继电器", this);
  connect(test_relay_button_, &QCheckBox::clicked, this, &RunningWidget::slotTestRelay);

  checkbox_fsm_change_ip_        = new QCheckBox("修改IP", this);
  checkbox_fsm_firmware_update_  = new QCheckBox("固件升级", this);
  checkbox_fsm_encoding_calib_   = new QCheckBox("码盘标定", this);
  checkbox_fsm_chn_angle_write_  = new QCheckBox("角度写入", this);
  checkbox_fsm_clear_calib_data_ = new QCheckBox("清除数据", this);
  checkbox_fsm_vbd_calib_        = new QCheckBox("VBD标定", this);
  checkbox_fsm_stress_test_      = new QCheckBox("压测", this);
  checkbox_fsm_aging_            = new QCheckBox("老化", this);
  checkbox_fsm_change_ip_->setChecked(true);
  checkbox_fsm_firmware_update_->setChecked(true);
  checkbox_fsm_encoding_calib_->setChecked(true);
  checkbox_fsm_chn_angle_write_->setChecked(true);
  checkbox_fsm_clear_calib_data_->setChecked(true);
  checkbox_fsm_vbd_calib_->setChecked(true);
  checkbox_fsm_stress_test_->setChecked(true);
  checkbox_fsm_aging_->setChecked(true);

  QSizePolicy size_policy = test_relay_button_->sizePolicy();
  size_policy.setHorizontalPolicy(QSizePolicy::Fixed);
  test_relay_button_->setSizePolicy(size_policy);
  checkbox_fsm_change_ip_->setSizePolicy(size_policy);
  checkbox_fsm_firmware_update_->setSizePolicy(size_policy);
  checkbox_fsm_encoding_calib_->setSizePolicy(size_policy);
  checkbox_fsm_chn_angle_write_->setSizePolicy(size_policy);
  checkbox_fsm_clear_calib_data_->setSizePolicy(size_policy);
  checkbox_fsm_vbd_calib_->setSizePolicy(size_policy);
  checkbox_fsm_stress_test_->setSizePolicy(size_policy);
  checkbox_fsm_aging_->setSizePolicy(size_policy);

  QHBoxLayout* button_layout = new QHBoxLayout;

  auto* project_code_label = new QLabel("项目编号:", this);
  project_code_label->setFixedWidth(65);

  project_code_combo_box_ = new QComboBox(this);
  project_code_combo_box_->setFixedWidth(100);
  connect(project_code_combo_box_, QOverload<int>::of(&QComboBox::currentIndexChanged), [&](int _index) {
    selected_project_ =
      static_cast<robosense::lidar::rsfsc_lib::ProjectCode>(project_code_combo_box_->currentData().toInt());
    widget_lidar_info_->setFixedProjectCode(static_cast<rsfsc_lib::ProjectCode>(selected_project_));
  });
  switch (_lidar_type)
  {
  case ProtocolType::MECH_BPEARL:
  {
    project_code_combo_box_->addItem("0335", robosense::lidar::rsfsc_lib::ProjectCode::PROJECT_0335);
    break;
  }
  case ProtocolType::MECH_HELIOS:
  {
    project_code_combo_box_->addItem("0320", robosense::lidar::rsfsc_lib::ProjectCode::PROJECT_0320);
    project_code_combo_box_->addItem("0321", robosense::lidar::rsfsc_lib::ProjectCode::PROJECT_0321);
    break;
  }
  case ProtocolType::MECH_RUBY:
  {
    project_code_combo_box_->addItem("0303", robosense::lidar::rsfsc_lib::ProjectCode::PROJECT_0303);
    break;
  }
  case ProtocolType::MECH_AIRY:
  {
    project_code_combo_box_->addItem("0350", robosense::lidar::rsfsc_lib::ProjectCode::PROJECT_0350);
    project_code_combo_box_->addItem("0351", robosense::lidar::rsfsc_lib::ProjectCode::PROJECT_0351);
    project_code_combo_box_->addItem("0352", robosense::lidar::rsfsc_lib::ProjectCode::PROJECT_0352);
    project_code_combo_box_->addItem("0360", robosense::lidar::rsfsc_lib::ProjectCode::PROJECT_0360);
    break;
  }
  default:
    project_code_combo_box_->addItem("0320", robosense::lidar::rsfsc_lib::ProjectCode::PROJECT_0320);
    project_code_combo_box_->addItem("0321", robosense::lidar::rsfsc_lib::ProjectCode::PROJECT_0321);
    project_code_combo_box_->addItem("0303", robosense::lidar::rsfsc_lib::ProjectCode::PROJECT_0303);
    project_code_combo_box_->addItem("0335", robosense::lidar::rsfsc_lib::ProjectCode::PROJECT_0335);
    project_code_combo_box_->addItem("0350", robosense::lidar::rsfsc_lib::ProjectCode::PROJECT_0350);
  }

  button_layout->addWidget(project_code_label);
  button_layout->addWidget(project_code_combo_box_);
  button_layout->addWidget(test_relay_button_);
  button_layout->addWidget(checkbox_fsm_change_ip_);
  button_layout->addWidget(checkbox_fsm_firmware_update_);
  button_layout->addWidget(checkbox_fsm_encoding_calib_);
  button_layout->addWidget(checkbox_fsm_chn_angle_write_);
  button_layout->addWidget(checkbox_fsm_clear_calib_data_);
  button_layout->addWidget(checkbox_fsm_vbd_calib_);
  button_layout->addWidget(checkbox_fsm_stress_test_);
  button_layout->addWidget(checkbox_fsm_aging_);
  button_layout->addWidget(one_key_run_button_);

  QVBoxLayout* layout = new QVBoxLayout;
  layout->addWidget(widget_lidar_info_);
  layout->addLayout(button_layout);

  this->setLayout(layout);

  QString lidar_name = QString("lidar %1").arg(lidar_index_);
  this->setWindowTitle(lidar_name);
}

RunningWidget::~RunningWidget() {}

void RunningWidget::showWidget(bool _is_running)
{
  one_key_run_button_->setText(_is_running ? "停止老化" : "开始老化");
  if (_is_running)
  {
    QPalette palette;
    palette.setColor(QPalette::ButtonText, Qt::darkRed);  // 设置按钮文本颜色为白色
    one_key_run_button_->setPalette(palette);
  }
  else
  {
    QPalette palette;
    palette.setColor(QPalette::ButtonText, Qt::black);  // 设置按钮文本颜色为白色
    one_key_run_button_->setPalette(palette);
  }

  checkbox_fsm_aging_->setEnabled(!_is_running && is_operate_enabled_);
  checkbox_fsm_stress_test_->setEnabled(!_is_running && is_operate_enabled_);
  checkbox_fsm_change_ip_->setEnabled(!_is_running && is_operate_enabled_);
  checkbox_fsm_encoding_calib_->setEnabled(!_is_running && is_operate_enabled_);
  checkbox_fsm_firmware_update_->setEnabled(!_is_running && is_operate_enabled_);
  checkbox_fsm_chn_angle_write_->setEnabled(!_is_running && is_operate_enabled_);
  checkbox_fsm_clear_calib_data_->setEnabled(!_is_running && is_operate_enabled_);
  checkbox_fsm_vbd_calib_->setEnabled(!_is_running && is_operate_enabled_);
  this->setWindowFlags(Qt::WindowStaysOnTopHint);
  this->setAttribute(Qt::WA_ShowModal, true);
  this->show();
}

QString RunningWidget::getLidarSn() { return widget_lidar_info_->getLidarSN(); }

QString RunningWidget::getLidarProject() { return widget_lidar_info_->getProjectCodeStr(); }
void RunningWidget::setOperateEnabled(bool _enabled)
{
  checkbox_fsm_change_ip_->setEnabled(_enabled);
  checkbox_fsm_firmware_update_->setEnabled(_enabled);
  checkbox_fsm_encoding_calib_->setEnabled(_enabled);
  checkbox_fsm_chn_angle_write_->setEnabled(_enabled);
  checkbox_fsm_clear_calib_data_->setEnabled(_enabled);
  checkbox_fsm_vbd_calib_->setEnabled(_enabled);
  checkbox_fsm_stress_test_->setEnabled(_enabled);
  checkbox_fsm_aging_->setEnabled(_enabled);
  is_operate_enabled_ = _enabled;
}
robosense::lidar::rsfsc_lib::ProjectCode RunningWidget::getLidarProjectCodeIndex()
{
  // return widget_lidar_info_->getProjectCodeIndex();
  return selected_project_;
}

void RunningWidget::setProtocolType(ProtocolType _type) { lidar_type_ = _type; }

void RunningWidget::slotStartAgingRunning() { signalRunning(one_key_run_button_->text() == "开始老化"); }

void RunningWidget::slotTestRelay(bool _is_turn_on)
{
  robosense::lidar::RelayController* relay_controller = robosense::lidar::RelayController::getInstance();

  relay_controller->relayTurn(lidar_index_ - 1, _is_turn_on);
}

}  // namespace lidar
}  // namespace robosense