﻿/******************************************************************************
 * Copyright 2024 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai
 *
 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.
 *
 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONS<PERSON>QUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#include "utils/csv_utils.h"
#include "csv_parser.h"
#include "rsfsc_log/rsfsc_log_macro.h"
#include <QFile>
#include <QTextStream>
#include <vector>

// NOLINTNEXTLINE(modernize-concat-nested-namespaces)
namespace robosense
{
namespace lidar
{

CsvUtils::CsvUtils()  = default;
CsvUtils::~CsvUtils() = default;

bool CsvUtils::loadRegisterCsvInfo(const QString& _file_path)
{
  QFile file(_file_path);
  if (!file.open(QIODevice::ReadOnly | QIODevice::Text))
  {
    RSFSCLog::getInstance()->error("无法打开文件: {}", _file_path.toStdString());
    return false;
  }
  CsvParser::loadRegisterCsvInfo(_file_path.toStdString());
  file.close();
  return true;
}
bool CsvUtils::loadLimitCsvInfo(const QString& _file_path)
{
  QFile file(_file_path);
  if (!file.open(QIODevice::ReadOnly | QIODevice::Text))
  {
    RSFSCLog::getInstance()->error("无法打开文件: {}", _file_path.toStdString());
    return false;
  }
  // 遍历所有行，找到包含 参数名称，那么下一行就是参数行，第一列是参数名称，第二列是参数值，取参数名push进vec中
  QTextStream in_file(&file);
  while (!in_file.atEnd())
  {
    QString line = in_file.readLine();
    if (line.isEmpty())
    {
      continue;
    }
    if (line.at(0) == '#')
    {
      continue;
    }

    auto line_split = line.split(",");
    if (line_split.size() < 2)
    {
      continue;
    }
    auto name = line_split.at(0).toStdString();
    if (name == "参数名称")
    {
      continue;
    }
    if (name.empty())
    {
      continue;
    }
    limit_name_vec_.emplace_back(line_split.at(0).toStdString());
  }
  file.close();
  return CsvParser::loadLimitCsvInfo(_file_path.toStdString());
}
bool CsvUtils::loadNormalCsvInfo(const QString& _file_path)
{
  QFile file(_file_path);
  if (!file.open(QIODevice::ReadOnly | QIODevice::Text))
  {
    RSFSCLog::getInstance()->error("无法打开文件: {}", _file_path.toStdString());
    return false;
  }
  RSFSCLog::getInstance()->info("加载打开 {}", _file_path);
  QTextStream in_file(&file);
  normal_data_str_list_vec_.clear();
  while (!in_file.atEnd())
  {
    normal_data_str_list_vec_.emplace_back(in_file.readLine().split(","));
  }
  file.close();
  return true;
}
bool CsvUtils::loadAngleCsvInfo(const QString& _file_path)
{
  QFile file(_file_path);
  if (!file.open(QIODevice::ReadOnly | QIODevice::Text))
  {
    RSFSCLog::getInstance()->error("无法打开文件: {}", _file_path.toStdString());
    return false;
  }
  QTextStream in_file(&file);
  angle_data_str_list_vec_.clear();
  while (!in_file.atEnd())
  {
    angle_data_str_list_vec_.emplace_back(in_file.readLine().split(","));
  }
  file.close();
  return true;
}

bool CsvUtils::getAngleData(std::vector<std::vector<float>>& _angle_vec_vec)
{
  size_t count = 0;
  for (auto& str_list : normal_data_str_list_vec_)
  {
    std::vector<float> angle_vec {};
    for (const auto& str : str_list)
    {
      bool is_ok  = false;
      float angle = str.toFloat(&is_ok);
      if (!is_ok)
      {
        LOG_ERROR("{} str list error {}", count, str_list);
        return false;
      }

      angle_vec.emplace_back(angle);
    }
    _angle_vec_vec.emplace_back(angle_vec);
    ++count;
  }
  return true;
}

std::optional<std::pair<std::vector<float>, std::vector<float>>> CsvUtils::getAngleData()
{
  if (angle_data_str_list_vec_.empty())
  {
    RSFSCLog::getInstance()->error("angle_data_str_list_vec_ 为空");
    return std::nullopt;
  }
  std::vector<float> ver_angle_vec {};
  std::vector<float> hor_angle_vec {};
  for (const auto& str : angle_data_str_list_vec_)
  {
    if (str.size() < 2)
    {
      RSFSCLog::getInstance()->error("angle_data_str_list_vec_ 数据错误: {}", str);
      return std::nullopt;
    }
    ver_angle_vec.emplace_back(str.at(0).toFloat());
    hor_angle_vec.emplace_back(str.at(1).toFloat());
  }
  return std::make_pair(ver_angle_vec, hor_angle_vec);
}

}  // namespace lidar
}  // namespace robosense