/******************************************************************************
 * Copyright 2022 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#include "lidar_ctl.h"
#include "app_event.h"
#include "config.h"
#include "data_struct.h"
#include "mech_udp.h"
#include "mes_widget.h"
#include "protocol/protocol_common.h"
#include "rsfsc_log/rsfsc_log_macro.h"
#include <QDateTime>
#include <QDebug>
#include <QString>
#include <chrono>
#include <cstddef>
#include <cstdint>
#include <rsfsc_msg.h>
#include <string>
#include <vector>
#include <widget_log_setting.h>

// NOLINTNEXTLINE(modernize-concat-nested-namespaces)
namespace robosense
{
namespace lidar
{

AgingWorkHandler::~AgingWorkHandler() { abort(); }
int AgingWorkHandler::handleState()
{
  try
  {
    handleEnter();
    next_state_ = static_cast<ActionState>(handle());
    handleExit();
  }
  catch (const InterruptException& exception)
  {
    if (dynamic_cast<RestoreLidarIp*>(this) != nullptr)
    {
      LOG_INDEX_ERROR("恢复IP中触发中断，已停止恢复IP: {}", exception.what());
      getWorkModel()->abort();
      app()->getMesWidget()->getWidgetLogSettingPtr()->signalAbort(getLidarIndex());
    }
    else
    {
      LOG_INDEX_ERROR("触发中断: {}", exception.what());
      getWorkModel()->abort();
    }
  }
  catch (const std::exception& exception)
  {
    LOG_INDEX_ERROR("触发抛出异常，未知异常: {}", exception.what());
    getWorkModel()->abort();
  }
  catch (...)
  {
    LOG_INDEX_ERROR("触发抛出异常，未知异常");
    getWorkModel()->abort();
  }
  return next_state_;
}

std::string AgingWorkHandler::pingWait(const std::vector<std::string>& _ip_vec, uint32_t _msec)
{
  auto start_time = std::chrono::system_clock::now();
  auto end_time   = std::chrono::system_clock::now();
  auto duration   = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);

  while (!isAbort() && duration.count() < _msec)
  {
    for (const auto& each_ip : _ip_vec)
    {
      if (LidarManager::ping(each_ip))
      {
        return each_ip;
      }
    }
    end_time = std::chrono::system_clock::now();
    duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    sleep(2);
  }

  return "";
}

bool AgingWorkHandler::connectLidar(const QString& _ip, int _port, int _timeout)
{
  auto para_aging  = getWorkModel()->getParaInfo();
  auto ping_result = pingWait({ _ip.toStdString() }, para_aging.ping_timeout);
  if (ping_result.empty())
  {
    LOG_INDEX_ERROR("ping雷达超时{}ms，[{}:{}]", para_aging.ping_timeout, _ip, _port);
    return false;
  }
  if (!getLidarManager()->connect(_ip, _port, _timeout))
  {
    LOG_INDEX_ERROR("连接雷达失败 [{}:{}]", _ip, _port);
    return false;
  }
  return true;
}

bool AgingWorkHandler::connectLidarCurrIP()
{
  auto para_info   = getWorkModel()->getParaInfo();
  auto ip_addr     = getLidarManager()->getIP();
  auto port        = getLidarManager()->getPort();
  auto ping_result = pingWait({ ip_addr }, para_info.ping_timeout);
  if (ping_result.empty())
  {
    LOG_INDEX_ERROR("ping雷达超时{}ms，[{}:{}]", para_info.ping_timeout, ip_addr, port);
    return false;
  }

  if (!connectLidar(QString::fromStdString(ip_addr), port, 50000))
  {
    LOG_INDEX_ERROR("连接雷达失败 [{}:{}]", ip_addr, port);
    return false;
  }

  return true;
}

bool AgingWorkHandler::connectLidarInfoIP()
{
  auto ip_addr = getLidarInfo()->getIP();
  auto port    = getLidarInfo()->getMSOPPort();
  return connectLidar(ip_addr, port, 50000);
}

// NOLINTNEXTLINE(bugprone-easily-swappable-parameters)
bool AgingWorkHandler::waitForStartUpComplete(const QString& _ip, const int _port, const int _timeout)
{
  QString ip_addr;
  int port = 0;
  if (getLidarManager()->isConnected())
  {
    if (!getLidarManager()->getConnectedIpPort(ip_addr, port))
    {
      LOG_INDEX_ERROR("获取雷达连接信息失败");
      return false;
    }
  }
  else
  {
    ip_addr = _ip;
    port    = _port;
    if (!getLidarManager()->connect(ip_addr, port, _timeout))
    {
      LOG_INDEX_ERROR("等待雷达启动失败, [{}:{}] 超时时间: {}", ip_addr, port, _timeout);
      return false;
    }
  }

  if (!waitForOneMsopPacket(ip_addr, port, _timeout))
  {
    LOG_INDEX_ERROR("等待雷达启动完成失败");
    return false;
  }
  LOG_INDEX_INFO("已接收到雷达MSOP包");

  sleep(1);
  return true;
}
bool AgingWorkHandler::waitForStartUpComplete()
{
  auto& para_info = getWorkModel()->getParaInfo();
  QString ip_addr = getLidarManager()->getIP().c_str();
  int port        = getLidarManager()->getPort();
  if (pingWait({ ip_addr.toStdString() }, para_info.ping_timeout).empty())
  {
    LOG_INDEX_ERROR("ping雷达超时{}ms，[{}:{}]", para_info.ping_timeout, ip_addr, port);
    return false;
  }
  if (!waitForStartUpComplete(ip_addr, port, getWorkModel()->getParaInfo().start_up_max_time))
  {
    return false;
  }

  getLidarManager()->writeCsvInitRegData();
  return true;
}

bool AgingWorkHandler::waitForStartUpCompleteByLidarInfo()
{
  auto& para_info = getWorkModel()->getParaInfo();
  QString ip_addr = getLidarInfo()->getIP();
  int port        = getLidarInfo()->getMSOPPort();
  if (pingWait({ ip_addr.toStdString() }, para_info.ping_timeout).empty())
  {
    LOG_INDEX_ERROR("ping雷达超时{}ms，[{}:{}]", para_info.ping_timeout, ip_addr, port);
    return false;
  }
  if (!waitForStartUpComplete(ip_addr, port, getWorkModel()->getParaInfo().start_up_max_time))
  {
    return false;
  }

  getLidarManager()->writeCsvInitRegData();
  return true;
}

bool AgingWorkHandler::waitForOneMsopPacket(const QString& _ip, const int _port, int _timeout)
{
  int pkt_count = 0;
  std::mutex mtx;
  std::condition_variable cond_v;

  auto ptr_udp_client = std::make_unique<robosense::lidar::MechUdp>(1248);
  ptr_udp_client->setLogIndex(getLidarIndex());
  ptr_udp_client->regRecvCallback([&](const char* _udp_data) {
    const auto* msop_pkt = reinterpret_cast<const MsopPacket96*>(_udp_data);
    if (msop_pkt->pkt_head != MSOP_PKT_HEAD_LITTLE)
    {
      return;
    }
    if (getLidarManager()->isValidMsop(_udp_data))
    {
      std::lock_guard<std::mutex> lock(mtx);
      pkt_count++;
      cond_v.notify_one();  // 收到有效的包后通知等待线程
    }
  });

  ptr_udp_client->start(_ip.toStdString(), _port);
  auto start_time = std::chrono::steady_clock::now();

  std::unique_lock<std::mutex> lock(mtx);
  while (!isAbort() && pkt_count == 0)
  {
    // 使用条件变量等待包，设置超时时间
    if (cond_v.wait_for(lock, std::chrono::milliseconds(_timeout), [&] { return pkt_count > 0; }))
    {
      break;  // 成功收到包，退出循环
    }

    // 检查超时条件
    auto end_time = std::chrono::steady_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    if (duration.count() > _timeout)
    {
      break;  // 超时退出
    }
  }

  return pkt_count != 0;  // 返回是否收到包的结果
}

void AgingWorkHandler::abortAndMaintainEnv(const QString& _msg)
{
  getWorkModel()->updateAgingState(AGING_ABORT);
  AppEvent::getInstance()->signalShowErrorMessageBox(
    QString("[%1] %2，已保留现场，请尽快排查处理").arg(getLidarIndex()).arg(_msg));
  getWorkModel()->setFailLabel(className());
  getWorkModel()->setFailMsg(_msg.toStdString());
  LOG_INDEX_ERROR("{}，请排查现场雷达问题[ip:{}:{}]", _msg, getLidarManager()->getIP(), getLidarManager()->getPort());
  app()->getMesWidget()->finishProcess(getLidarIndex(), LogTestStatus::LOG_TEST_STATUS_ABORT, "Aging", _msg);
}

void AgingWorkHandler::setWorkModelFailMsg(const QString& _fail_label, std::string_view _fail_msg)
{
  getWorkModel()->setFailLabel(_fail_label.toStdString());
  getWorkModel()->setFailMsg(_fail_msg);
  LOG_INDEX_ERROR("{}, {}", _fail_label, _fail_msg);
}
void AgingWorkHandler::setWorkModelFailMsg(std::string_view _fail_msg) { setWorkModelFailMsg(className(), _fail_msg); }

int CheckMes::handleEnter()
{
  app()->signalLidarChangeIpIsReady(false, getLidarIndex());
  return 0;
}

int CheckMes::handleExit()
{
  app()->signalLidarChangeIpIsReady(true, getLidarIndex());
  msleep(100);
  return 0;
}

int CheckMes::handle()
{
  QString data_path;
  QString result_path;
  QString temp_path;

  if (!getWorkModel()->checkAllState())
  {
    abortAndMaintainEnv("MES检查失败");
    return STATE_FINAL;
  }

  if (app()->getWidgetLogSetting()->isUseIPPool() ||
      (!getParaInfo().is_use_zip_file && getParaInfo().fsm_firmware_update))
  {
    if (!getWorkModel()->requireCustomerInfo())
    {
      getWorkModel()->setFailLabel("requiredCustomerSN");
      getWorkModel()->updateAgingState(AGING_NG);
      app()->signalShowErrorMessageBox("MES中获取Mac地址失败");
      return STATE_FAIL;
    }
  }

  if (getParaInfo().fsm_chn_angle_write)
  {
    if (!getWorkModel()->requireVerAngleData())
    // if (!getWorkModel()->requireVerAngleDataBySubSN())
    {
      getWorkModel()->setFailLabel("requireVerAngleData");
      getWorkModel()->updateAgingState(AGING_NG);
      app()->signalShowErrorMessageBox("MES中获取垂直角度数据失败");
      return STATE_FAIL;
    }
  }

  if (getParaInfo().fsm_vbd_calib)
  {
    if (!getWorkModel()->requiredVbdData())
    {
      getWorkModel()->setFailLabel("requiredVbdData");
      getWorkModel()->updateAgingState(AGING_NG);
      app()->signalShowErrorMessageBox("MES中获取VBD数据失败");
      return STATE_FAIL;
    }
  }

  getWorkModel()->updateFirmwareUpdateState(FIRMWARE_UPDATE_WAIT);
  getWorkModel()->updateEncodeCalibState(ENCODE_CALIB_WAIT);
  getWorkModel()->updateStressState(STRESS_WAIT);
  getWorkModel()->updateAgingState(AGING_WAIT);
  getWorkModel()->updateRunState(RUN_BUSY);

  return STATE_AGING_CONNECT_LIDAR;
}

int ConnectLidar::handle()
{
  // 上电开始老化，进行第一步连接操作
  getWorkModel()->turnOnRelay();
  app()->signalLidarChangeIpIsReady(false, getLidarIndex());

  const auto& para_info = getWorkModel()->getParaInfo();

  getWorkModel()->startTcpdumpBothOrgAndObjIPExcludeMSOP();

  std::vector<std::string> ip_vec = { para_info.org_ip.toStdString(), getLidarInfo()->getIP().toStdString() };
  LOG_INDEX_INFO("开始ping雷达 [{}]", fmt::join(ip_vec, ","));

  auto start_time  = std::chrono::steady_clock::now();
  auto ping_result = pingWait(ip_vec, para_info.ping_timeout);

  bool ping_success = getWorkModel()->addMeasureMessage("fsm_ping", ping_result.empty() ? 0 : 1,
                                                        rsfsc_lib::MeasureDataType::MEASURE_DATA_TYPE_INT);
  if (!ping_success)
  {
    auto err_msg = fmt::format("ping雷达超时{}ms，[{}:{}]，[{}:{}]", para_info.ping_timeout, para_info.org_ip,
                               para_info.org_msop_port, getLidarInfo()->getIP(), getLidarInfo()->getMSOPPort());
    setWorkModelFailMsg(err_msg);
    getWorkModel()->updateAgingState(AGING_NG);
    AppEvent::getInstance()->signalShowErrorMessageBox(QString("[%1] %2").arg(getLidarIndex()).arg(err_msg.c_str()));
    return STATE_ABORT;
  }
  // LOG_INDEX_INFO("ping雷达成功，IP为[{}], sleep 10s等待连接", ping_result);
  // sleep(10);

  auto end_time = std::chrono::steady_clock::now();
  auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
  LOG_INDEX_INFO("ping {} 成功，耗时{}s", ping_result, static_cast<float>(duration.count()) / 1000);
  QString connect_ip = para_info.org_ip;
  int connect_port   = para_info.org_msop_port;
  if (ping_result == getLidarInfo()->getIP().toStdString())
  {
    connect_ip   = getLidarInfo()->getIP();
    connect_port = getLidarInfo()->getMSOPPort();
  }

  if (!getLidarManager()->connect(connect_ip, connect_port))
  {
    getWorkModel()->addMeasureMessage("fsm_connect_before", false);
    setWorkModelFailMsg(fmt::format("第一次连接雷达失败 [{}:{}]", connect_ip, connect_port));
    getWorkModel()->updateAgingState(AGING_NG);
    return STATE_ABORT;
  }
  getWorkModel()->addMeasureMessage("fsm_connect_before", true);

  if (!getLidarManager()->setPowerOn())
  {
    getWorkModel()->addMeasureMessage("fsm_power_on", false);
    setWorkModelFailMsg("setPowerOn 顶板上电失败");
    getWorkModel()->updateAgingState(AGING_NG);
    return STATE_FAIL;
  }

  // LOG_INDEX_INFO("等待雷达启动完成");
  // if (!waitForStartUpComplete(connect_ip, connect_port, para_info.start_up_max_time))
  // {
  //   setWorkModelFailMsg("等待雷达启动完成失败");
  //   getWorkModel()->updateAgingState(AGING_NG);
  //   return STATE_FAIL;
  // }
  LOG_INDEX_INFO("雷达启动成功");
  LOG_INDEX_INFO("第一次连接雷达成功 [{}:{}]", connect_ip, connect_port);
  auto lidar_real_sn = QString::fromStdString(getLidarManager()->getConfigParamLidarSN());
  auto lidar_info_sn = getLidarInfo()->getLidarSN();

  if (lidar_real_sn.toCaseFolded() != lidar_info_sn.toCaseFolded())
  {
    auto err_msg =
      fmt::format("位置{} 读取到的雷达SN[{}]与扫码的SN[{}]不匹配", getLidarIndex(), lidar_real_sn, lidar_info_sn);
    setWorkModelFailMsg(err_msg);
    app()->signalShowErrorMessageBox(err_msg.c_str());
    getWorkModel()->updateAgingState(AGING_ABORT);
    getWorkModel()->addMeasureMessage("fsm_lidar_sn_match", false);
    return STATE_ABORT;
  }
  getWorkModel()->addMeasureMessage("fsm_lidar_sn_match", true);

  //判断是否使用ip池  若使用则进行绑定操作
  if (app()->getWidgetLogSetting()->isUseIPPool())
  {
    LOG_INDEX_INFO("已经检测到使用IP池，现进行查询绑定");
    if (!app()->getWidgetLogSetting()->boundLidarIP(getLidarIndex()))
    {
      setWorkModelFailMsg(fmt::format("雷达{} 绑定雷达ip失败.", getLidarIndex()));
      app()->signalShowErrorMessageBox(fmt::format("雷达{} 绑定雷达ip失败.", getLidarIndex()).c_str());
      getWorkModel()->updateAgingState(AGING_NG);
      getWorkModel()->addMeasureMessage("fsm_bound_ip_pool", false);
      return STATE_ABORT;
    }
    getWorkModel()->addMeasureMessage("fsm_bound_ip_pool", true);
    LOG_INDEX_INFO("获取雷达绑定IP成功 [{}:{}] difop:[{}]", getLidarInfo()->getIP(), getLidarInfo()->getMSOPPort(),
                   getLidarInfo()->getDIFOPPort());
  }

  if (connect_ip == getLidarInfo()->getIP())
  {
    LOG_INDEX_INFO("已经是老化IP，不需要修改IP");
    bool is_need_restore = !app()->getWidgetLogSetting()->isUseIPPool();
    is_need_restore &= getParaInfo().fsm_change_ip;
    getWorkModel()->setNeedRestoreIp(is_need_restore);
    app()->signalLidarChangeIpIsReady(true, getLidarIndex());
    return STATE_FIRMWARE_UPDATE_UNZIP;
  }

  // 等待雷达启动完成后仍然需要等待5秒，因为顶板还在初始化
  sleep(5);

  return STATE_CHANGE_LIDAR_IP;
}

int ChangeLidarIp::handle()
{
  const auto& para_info = getWorkModel()->getParaInfo();
  if (!para_info.fsm_change_ip)
  {
    LOG_INDEX_INFO("不需要修改雷达IP");
    app()->signalLidarChangeIpIsReady(true, getLidarIndex());
    return STATE_FIRMWARE_UPDATE_UNZIP;
  }

  bool is_net_need_change = false;
  if (!getLidarManager()->isNetNeedChange(is_net_need_change, getLidarInfo()->getIP(), getLidarInfo()->getMSOPPort(),
                                          getLidarInfo()->getDIFOPPort()))
  {
    LOG_INDEX_ERROR("获取雷达网络参数失败");
    setWorkModelFailMsg("获取雷达网络参数失败");
    getWorkModel()->addMeasureMessage("fsm_change_ip", false);
    getWorkModel()->updateAgingState(AGING_NG);
    app()->signalLidarChangeIpIsReady(true, getLidarIndex());
    return STATE_FAIL;
  }

  if (!is_net_need_change)
  {
    LOG_INDEX_INFO("雷达网络不需要修改");
    app()->signalLidarChangeIpIsReady(true, getLidarIndex());
    getWorkModel()->setNeedRestoreIp(!app()->getWidgetLogSetting()->isUseIPPool());
    return STATE_FIRMWARE_UPDATE_UNZIP;
  }

  LOG_INDEX_INFO("尝试修改雷达网络为[{}:{}]", getLidarInfo()->getIP(), getLidarInfo()->getMSOPPort());
  if (!getLidarManager()->changeLidarNet(getLidarInfo()->getIP(), getLidarInfo()->getMSOPPort(),
                                         getLidarInfo()->getDIFOPPort()))
  {
    getWorkModel()->addMeasureMessage("fsm_change_ip", false);
    setWorkModelFailMsg("修改雷达IP失败");
    app()->signalLidarChangeIpIsReady(true, getLidarIndex());
    getWorkModel()->setNeedRestoreIp(false);
    return STATE_FAIL;
  }
  getLidarManager()->disconnect();

  if (app()->getWidgetLogSetting()->isUseIPPool())
  {
    msleep(5000);
    if (!app()->getWidgetLogSetting()->confirmBoundIPState(getLidarIndex(), true))
    {
      // setWorkModelFailMsg(fmt::format("雷达{}确认绑定雷达ip失败.", getLidarIndex()));
      // app()->signalShowErrorMessageBox(fmt::format("雷达{} 绑定雷达ip失败.", getLidarIndex()).c_str());
      // getWorkModel()->updateAgingState(AGING_NG);
      getWorkModel()->addMeasureMessage("fsm_bound_ip_pool", false);
      // return STATE_FAIL;
      // LOG_INDEX_ERROR("雷达{}确认绑定雷达ip失败 SN:{}, IP:", getLidarIndex(), getLidarInfo()->getLidarSN(),
      //                 getLidarInfo()->getIP());
    }
  }

  QString net_str = QString("[%1:%2]").arg(getLidarInfo()->getIP()).arg(getLidarInfo()->getMSOPPort());

  LOG_INDEX_INFO("IP修改成功，雷达IP为{}, difop:[{}]", net_str, getLidarInfo()->getDIFOPPort());
  getWorkModel()->addMeasureMessage("fsm_change_ip", 1, rsfsc_lib::MeasureDataType::MEASURE_DATA_TYPE_INT);
  getWorkModel()->setNeedRestoreIp(!app()->getWidgetLogSetting()->isUseIPPool());

  sleep(5);
  getWorkModel()->toggleRelay();
  app()->signalLidarChangeIpIsReady(true, getLidarIndex());
  // LOG_INDEX_INFO("修改配置后等待30s");
  // sleep(30);
  // if (!getWorkModel()->addMeasureMessage("fsm_connect_after", waitForStartUpCompleteByLidarInfo()))
  // {
  //   setWorkModelFailMsg("等待雷达启动完成失败");
  //   getWorkModel()->updateAgingState(AGING_NG);
  //   app()->signalLidarChangeIpIsReady(true, getLidarIndex());
  //   return STATE_FAIL;
  // }
  LOG_INDEX_INFO("尝试连接雷达{}", net_str);
  if (!getWorkModel()->addMeasureMessage("fsm_connect_after",
                                         connectLidar(getLidarInfo()->getIP(), getLidarInfo()->getMSOPPort(), 50000)))
  {
    setWorkModelFailMsg(fmt::format("第二次连接雷达失败{}", net_str));
    getWorkModel()->updateAgingState(AGING_NG);
    app()->signalLidarChangeIpIsReady(true, getLidarIndex());
    return STATE_FAIL;
  }

  getWorkModel()->addMeasureMessage("fsm_connect_after", 1, rsfsc_lib::MeasureDataType::MEASURE_DATA_TYPE_INT);
  app()->signalLidarChangeIpIsReady(true, getLidarIndex());

  return STATE_FIRMWARE_UPDATE_UNZIP;
}

int FirmwareUpdateUnzip::handle()
{
  sleep(2);
  getWorkModel()->startTcpdumpExcludeMSOP();
  if (!getParaInfo().fsm_firmware_update)
  {
    LOG_INDEX_INFO("跳过固件升级");
    getWorkModel()->updateFirmwareUpdateState(FIRMWARE_UPDATE_SKIP);
    return STATE_CLEAR_CALIB_DATA;
  }
  LOG_INDEX_INFO("开始固件升级解压文件中...");
  getWorkModel()->updateFirmwareUpdateState(FIRMWARE_UPDATE_UNZIP);

  if (!getWorkModel()->addMeasureMessage("fsm_firmware_update_unzip", getWorkModel()->unzipFirmware()))
  {
    setWorkModelFailMsg("固件升级解压文件失败");
    getWorkModel()->updateFirmwareUpdateState(FIRMWARE_UPDATE_NG);
    return STATE_ABORT;
  }
  return STATE_FIRMWARE_UPDATE_APP;
}

int FirmwareUpdateApp::handleEnter()
{
  RSFSCLog::getInstance(getLidarIndex())->setFileLogLevel(RSFSCLog::RSFSCLOG_LEVEL_INFO);
  return 0;
}

int FirmwareUpdateApp::handle()
{
  LOG_INDEX_INFO("开始固件升级APP...");
  if (!waitForStartUpComplete())
  {
    LOG_INDEX_ERROR("升级前等待点云包超时");
    setWorkModelFailMsg("升级前等待点云包超时");
    getWorkModel()->addMeasureMessage("fsm_firmware_update_app", false);
    getWorkModel()->updateFirmwareUpdateState(FIRMWARE_UPDATE_NG);
    return STATE_FAIL;
  }
  LOG_INDEX_INFO("升级前等待点云包成功");

  if (!getWorkModel()->checkBeforeFirmwareUpdate())
  {
    setWorkModelFailMsg("固件升级前检查失败");
    getWorkModel()->updateFirmwareUpdateState(FIRMWARE_UPDATE_NG);
    return STATE_FAIL;
  }

  getWorkModel()->updateFirmwareUpdateState(FIRMWARE_UPDATE_APP);

  if (!getWorkModel()->addMeasureMessage("fsm_firmware_update_app", getWorkModel()->updateFirmwareApp()))
  {
    setWorkModelFailMsg("固件升级APP失败");
    getWorkModel()->updateFirmwareUpdateState(FIRMWARE_UPDATE_NG);
    return STATE_FAIL;
  }
  return STATE_FIRMWARE_UPDATE_BOT;
}
int FirmwareUpdateBot::handle()
{
  LOG_INDEX_INFO("开始固件升级底板...");
  sleep(2);
  getWorkModel()->updateFirmwareUpdateState(FIRMWARE_UPDATE_BOT);

  if (!getWorkModel()->addMeasureMessage("fsm_firmware_update_bot", getWorkModel()->updateFirmwareBot()))
  {
    setWorkModelFailMsg("固件升级底板失败");
    getWorkModel()->updateFirmwareUpdateState(FIRMWARE_UPDATE_NG);
    return STATE_FAIL;
  }
  return STATE_FIRMWARE_UPDATE_TOP;
}
int FirmwareUpdateTop::handle()
{
  LOG_INDEX_INFO("开始固件升级顶板...");
  sleep(2);
  getWorkModel()->updateFirmwareUpdateState(FIRMWARE_UPDATE_TOP);

  if (!getWorkModel()->addMeasureMessage("fsm_firmware_update_top", getWorkModel()->updateFirmwareTop()))
  {
    setWorkModelFailMsg("固件升级顶板失败");
    getWorkModel()->updateFirmwareUpdateState(FIRMWARE_UPDATE_NG);
    return STATE_FAIL;
  }
  return STATE_FIRMWARE_UPDATE_WRITE_CONFIG;
}
int FirmwareUpdateWriteConfig::handle()
{
  LOG_INDEX_INFO("开始固件升级写入配置...");
  getWorkModel()->updateFirmwareUpdateState(FIRMWARE_UPDATE_WRITE_CONFIG);

  if (!getWorkModel()->addMeasureMessage("fsm_firmware_update_write_config",
                                         getWorkModel()->updateFirmwareWriteConfig()))
  {
    setWorkModelFailMsg("固件升级写入配置失败");
    getWorkModel()->updateFirmwareUpdateState(FIRMWARE_UPDATE_NG);
    return STATE_FAIL;
  }
  sleep(2);
  return STATE_FIRMWARE_UPDATE_REBOOT;
}

int FirmwareUpdateReboot::handle()
{
  LOG_INDEX_INFO("开始固件升级重启...");
  getWorkModel()->updateFirmwareUpdateState(FIRMWARE_UPDATE_REBOOT);

  getWorkModel()->toggleRelay();

  if (!connectLidarCurrIP())
  {
    setWorkModelFailMsg("固件升级重启失败");
    getWorkModel()->addMeasureMessage("fsm_firmware_update_reboot", false);
    getWorkModel()->updateFirmwareUpdateState(FIRMWARE_UPDATE_NG);
    return STATE_FAIL;
  }
  LOG_INDEX_INFO("固件升级重启连接成功[{}:{}]", getLidarInfo()->getIP(), getLidarInfo()->getMSOPPort());

  if (!waitForStartUpComplete())
  {
    setWorkModelFailMsg("固件升级重启, 等待雷达启动超时");
    getWorkModel()->addMeasureMessage("fsm_firmware_update_reboot", false);
    getWorkModel()->updateFirmwareUpdateState(FIRMWARE_UPDATE_NG);
    return STATE_FAIL;
  }
  LOG_INDEX_INFO("固件升级重启, 已接收到MSOP包");
  getWorkModel()->addMeasureMessage("fsm_firmware_update_reboot", true);

  return STATE_FIRMWARE_UPDATE_CHECK;
}

int FirmwareUpdateCheck::handle()
{
  LOG_INDEX_INFO("开始固件升级校验");
  getWorkModel()->updateFirmwareUpdateState(FIRMWARE_UPDATE_CHECK);

  if (!getWorkModel()->addMeasureMessage("fsm_firmware_update_check", getWorkModel()->updateFirmwareCheck()))
  {
    setWorkModelFailMsg("固件升级校验失败");
    getWorkModel()->updateFirmwareUpdateState(FIRMWARE_UPDATE_NG);
    return STATE_FAIL;
  }

  getWorkModel()->updateFirmwareUpdateState(FIRMWARE_UPDATE_PASS);
  getWorkModel()->resetEncodeCalibCount();
  return STATE_CLEAR_CALIB_DATA;
}
int FirmwareUpdateCheck::handleExit()
{
  RSFSCLog::getInstance(getLidarIndex())->setFileLogLevel(RSFSCLog::RSFSCLOG_LEVEL_DEBUG);
  return 0;
}

int ClearCalibData::handleEnter()
{
  RSFSCLog::getInstance(getLidarIndex())->setFileLogLevel(RSFSCLog::RSFSCLOG_LEVEL_INFO);
  return 0;
}

int ClearCalibData::handle()
{
  if (!getParaInfo().fsm_clear_calib_data)
  {
    LOG_INDEX_INFO("跳过清除标定数据");
    return STATE_CHN_ANGLE_WRITE;
  }
  if (!waitForStartUpComplete())
  {
    setWorkModelFailMsg("等待雷达启动超时");
    getWorkModel()->addMeasureMessage("clear_calib_data", false);
    getWorkModel()->updateAgingState(AGING_NG);
    return STATE_FAIL;
  }
  LOG_INDEX_INFO("开始清除标定数据");
  // if (!getWorkModel()->clearCalibData())
  // {
  //   return STATE_FAIL;
  // }
  // LOG_INDEX_INFO("正在重启雷达");
  // getWorkModel()->toggleRelay();
  // if (!waitForStartUpComplete())
  // {
  //   setWorkModelFailMsg("等待雷达启动超时");
  //   getWorkModel()->addMeasureMessage("clear_calib_data", false);
  //   getWorkModel()->updateAgingState(AGING_NG);
  //   return STATE_FAIL;
  // }

  if (!getWorkModel()->writeExpZeroAngle())
  {
    setWorkModelFailMsg("设置经验零度角失败");
    getWorkModel()->updateAgingState(AGING_NG);
    return STATE_FAIL;
  }
  sleep(2);

  return STATE_CHN_ANGLE_WRITE;
}

int ClearCalibData::handleExit()
{
  RSFSCLog::getInstance(getLidarIndex())->setFileLogLevel(RSFSCLog::RSFSCLOG_LEVEL_INFO);
  return 0;
}

int ChnAngleWrite::handle()
{
  if (!getParaInfo().fsm_chn_angle_write)
  {
    LOG_INDEX_INFO("跳过角度写入");
    return STATE_VBD_CALIB;
  }
  LOG_INDEX_INFO("开始角度写入");

  if (!getWorkModel()->writeChnAngleData())
  {
    return STATE_FAIL;
  }

  return STATE_VBD_CALIB;
}

int VbdCalib::handle()
{
  if (!getParaInfo().fsm_vbd_calib)
  {
    LOG_INDEX_INFO("跳过VBD标定");
    return STATE_ENCODING_CALIB_MOTOR_STABLE;
  }
  LOG_INDEX_INFO("开始VBD标定");

  waitForStartUpComplete();

  if (!getWorkModel()->readVbdCurveAndCalData())
  {
    getWorkModel()->setFailLabel("readVbdCurveAndCalData");
    return STATE_FAIL;
  }

  if (!getWorkModel()->writeVbdData())
  {
    getWorkModel()->setFailLabel("writeVbdData");
    return STATE_FAIL;
  }

  if (!getWorkModel()->rebootAndWait())
  {
    getWorkModel()->setFailLabel("rebootAndWait");
    return STATE_FAIL;
  }

  if (!getWorkModel()->checkVbdData())
  {
    getWorkModel()->setFailLabel("checkVbdData");
    return STATE_FAIL;
  }

  return STATE_ENCODING_CALIB_MOTOR_STABLE;
}

int EncodingMotorStable::handle()
{
  if (!getParaInfo().fsm_encoding_calib)
  {
    LOG_INDEX_INFO("跳过码盘标定");
    getWorkModel()->updateEncodeCalibState(ENCODE_CALIB_SKIP);
    return STATE_INIT_LIDAR;
  }
  if (getWorkModel()->getEncodeCalibCount() > 2)
  {
    LOG_INDEX_ERROR("码盘标定失败3次");
    getWorkModel()->updateEncodeCalibState(ENCODE_CALIB_NG);
    return STATE_FAIL;
  }

  LOG_INDEX_INFO("正在进行第{}次码盘标定", getWorkModel()->getEncodeCalibCount() + 1);
  if (getWorkModel()->getEncodeCalibCount() > 0)
  {
    // 重启
    getWorkModel()->toggleRelay(5000);
    sleep(10);
    if (!connectLidarCurrIP())
    {
      setWorkModelFailMsg(fmt::format("第{}次重启后连接雷达失败", getWorkModel()->getEncodeCalibCount() + 1));
      getWorkModel()->updateEncodeCalibState(ENCODE_CALIB_NG);
      getWorkModel()->addEncodeCalibCount();
      return STATE_ENCODING_CALIB_MOTOR_STABLE;
    }
  }

  LOG_INDEX_INFO("等待电机稳定...");
  getWorkModel()->updateEncodeCalibState(ENCODE_CALIB_BUSY);
  if (!getLidarManager()->startEncodCalib())
  {
    setWorkModelFailMsg("等待电机稳定失败");
    getWorkModel()->addMeasureMessage("fsm_encod_calib_motor_stable", false);
    getWorkModel()->updateEncodeCalibState(ENCODE_CALIB_NG);
    getWorkModel()->addEncodeCalibCount();
    return STATE_ENCODING_CALIB_MOTOR_STABLE;
  }
  getWorkModel()->addMeasureMessage("fsm_encod_calib_motor_stable", true);

  LOG_INDEX_INFO("电机已经稳定就绪");
  return STATE_ENCODING_CALIB_COLLECT;
}

int EncodingCollect::handleEnter()
{
  RSFSCLog::getInstance(getLidarIndex())->setFileLogLevel(RSFSCLog::RSFSCLOG_LEVEL_INFO);
  return 0;
}
int EncodingCollect::handleExit()
{
  RSFSCLog::getInstance(getLidarIndex())->setFileLogLevel(RSFSCLog::RSFSCLOG_LEVEL_DEBUG);
  return 0;
}
int EncodingCollect::handle()
{
  LOG_INDEX_INFO("开始码盘标定采集");
  getWorkModel()->updateEncodeCalibState(ENCODE_CALIB_COLLECT);
  std::vector<std::vector<uint32_t>> scale_data_vec_vec;

  for (int i = 0; i < getParaInfo().encod_calib_collect_num; ++i)
  {
    if (!getLidarManager()->writeRegData(ADDR_INFO_LOCK, 0))
    {
      setWorkModelFailMsg("写入寄存器数据锁失败ADDR_INFO_LOCK = 0x83c04300, 1");
      getWorkModel()->addMeasureMessage("fsm_encod_calib_collect", false);
      getWorkModel()->updateEncodeCalibState(ENCODE_CALIB_NG);
      getWorkModel()->addEncodeCalibCount();
      return STATE_ENCODING_CALIB_MOTOR_STABLE;
    }
    msleep(100);
    if (!getLidarManager()->writeRegData(ADDR_INFO_LOCK, 1))
    {
      setWorkModelFailMsg("写入寄存器数据锁失败ADDR_INFO_LOCK = 0x83c04300, 1");
      getWorkModel()->addMeasureMessage("fsm_encod_calib_collect", false);
      getWorkModel()->updateEncodeCalibState(ENCODE_CALIB_NG);
      getWorkModel()->addEncodeCalibCount();
      return STATE_ENCODING_CALIB_MOTOR_STABLE;
    }
    msleep(100);

    std::vector<uint32_t> scale_data_vec;
    if (!getLidarManager()->readRegData(ADDR_SCALE, ENCODE_NUM, scale_data_vec) || scale_data_vec.size() != 99)
    {
      setWorkModelFailMsg("读取电机scale数据失败");
      getWorkModel()->addMeasureMessage("fsm_encod_calib_collect", false);
      getWorkModel()->updateEncodeCalibState(ENCODE_CALIB_NG);
      getWorkModel()->addEncodeCalibCount();
      return STATE_ENCODING_CALIB_MOTOR_STABLE;
    }

    scale_data_vec_vec.push_back(scale_data_vec);
    msleep(200);
  }
  if (!getWorkModel()->saveEncodeCalibData(scale_data_vec_vec))
  {
    setWorkModelFailMsg("保存码盘标定数据失败");
    getWorkModel()->addMeasureMessage("fsm_encod_calib_collect", false);
    getWorkModel()->updateEncodeCalibState(ENCODE_CALIB_NG);
    getWorkModel()->addEncodeCalibCount();
    return STATE_ENCODING_CALIB_MOTOR_STABLE;
  }

  LOG_INDEX_INFO("码盘标定数据采集完成");
  return STATE_ENCODING_CALIB_PROCESS_WRITE;
}

int EncodingProcessAndWrite::handleEnter()
{
  RSFSCLog::getInstance(getLidarIndex())->setFileLogLevel(RSFSCLog::RSFSCLOG_LEVEL_INFO);
  return 0;
}
int EncodingProcessAndWrite::handleExit()
{
  RSFSCLog::getInstance(getLidarIndex())->setFileLogLevel(RSFSCLog::RSFSCLOG_LEVEL_DEBUG);
  return 0;
}

int EncodingProcessAndWrite::handle()
{
  LOG_INDEX_INFO("开始码盘标定处理数据");
  std::vector<std::vector<uint32_t>> scale_data = getWorkModel()->loadEncodeCalibData();
  if (scale_data.empty() || static_cast<int>(scale_data.size()) != getParaInfo().encod_calib_collect_num)
  {
    setWorkModelFailMsg(fmt::format("加载码盘标定数据失败, scale size: {}, collect num: {}", scale_data.size(),
                                    getParaInfo().encod_calib_collect_num));
    getWorkModel()->addMeasureMessage("fsm_encod_calib_process", 0, rsfsc_lib::MeasureDataType::MEASURE_DATA_TYPE_INT);
    getWorkModel()->updateEncodeCalibState(ENCODE_CALIB_NG);
    getWorkModel()->addEncodeCalibCount();
    return STATE_ENCODING_CALIB_MOTOR_STABLE;
  }

  AgingWorkModel::EncodCalibData result_data;
  if (!getWorkModel()->processEncodeCalibData(scale_data, result_data))
  {
    setWorkModelFailMsg("码盘标定处理失败");
    getWorkModel()->addMeasureMessage("fsm_encod_calib_process", false);
    getWorkModel()->addEncodeCalibCount();
    return STATE_ENCODING_CALIB_MOTOR_STABLE;
  }
  getWorkModel()->addMeasureMessage("fsm_encod_calib_process", true);

  if (!getWorkModel()->addMeasureMessage("fsm_encod_calib_process", getWorkModel()->checkEncodeCalibData(result_data)))
  {
    setWorkModelFailMsg("码盘标定数据校验失败");
    getWorkModel()->addEncodeCalibCount();
    getWorkModel()->updateEncodeCalibState(ENCODE_CALIB_NG);
    return STATE_ENCODING_CALIB_MOTOR_STABLE;
  }

  if (!getLidarManager()->writeRegData(ADDR_COEFF, result_data.calibration_scale_coeff))
  {
    setWorkModelFailMsg(
      fmt::format("写入码盘标定系数coeff失败 [{}:{}]", ADDR_COEFF, result_data.calibration_scale_coeff.size()));
    getWorkModel()->addMeasureMessage("fsm_encod_calib_write", 0, rsfsc_lib::MeasureDataType::MEASURE_DATA_TYPE_INT);
    getWorkModel()->updateEncodeCalibState(ENCODE_CALIB_NG);
    getWorkModel()->addEncodeCalibCount();
    return STATE_ENCODING_CALIB_MOTOR_STABLE;
  }

  if (!getLidarManager()->writeRegData(ADDR_STEP, result_data.angle_insert_step))
  {
    setWorkModelFailMsg(
      fmt::format("写入码盘标定角度插值步长step失败 [{}:{}]", ADDR_STEP, result_data.angle_insert_step.size()));
    getWorkModel()->addMeasureMessage("fsm_encod_calib_write", 0, rsfsc_lib::MeasureDataType::MEASURE_DATA_TYPE_INT);
    getWorkModel()->updateEncodeCalibState(ENCODE_CALIB_NG);
    getWorkModel()->addEncodeCalibCount();
    return STATE_ENCODING_CALIB_MOTOR_STABLE;
  }

  if (!getLidarManager()->sendScaleAvgToMotor(result_data.scale_avg))
  {
    setWorkModelFailMsg("发送码盘标定电机数据平均值失败");
    getWorkModel()->addMeasureMessage("fsm_encod_calib_write", 0, rsfsc_lib::MeasureDataType::MEASURE_DATA_TYPE_INT);
    getWorkModel()->updateEncodeCalibState(ENCODE_CALIB_NG);
    getWorkModel()->addEncodeCalibCount();
    return STATE_ENCODING_CALIB_MOTOR_STABLE;
  }

  if (!getLidarManager()->stopEncodCalib())
  {
    setWorkModelFailMsg("停止码盘标定失败");
    getWorkModel()->addMeasureMessage("fsm_encod_calib_save", 0, rsfsc_lib::MeasureDataType::MEASURE_DATA_TYPE_INT);
    getWorkModel()->updateEncodeCalibState(ENCODE_CALIB_NG);
    getWorkModel()->addEncodeCalibCount();
    return STATE_ENCODING_CALIB_MOTOR_STABLE;
  }

  // 回读校验
  AgingWorkModel::EncodCalibData read_data;
  if (!getLidarManager()->getEncodCalibData(read_data.calibration_scale_coeff, read_data.angle_insert_step))
  {
    setWorkModelFailMsg("回读码盘标定数据失败");
    getWorkModel()->addMeasureMessage("fsm_encod_calib_read", 0, rsfsc_lib::MeasureDataType::MEASURE_DATA_TYPE_INT);
    getWorkModel()->updateEncodeCalibState(ENCODE_CALIB_NG);
    getWorkModel()->addEncodeCalibCount();
    return STATE_ENCODING_CALIB_MOTOR_STABLE;
  }

  if (!getWorkModel()->addMeasureMessage(
        "fsm_encod_calib_read_check",
        getWorkModel()->checkEncodeCalibData(read_data) && getWorkModel()->saveEncodeCalibData(read_data, "read")))
  {
    setWorkModelFailMsg("回读码盘标定数据校验失败");
    getWorkModel()->updateEncodeCalibState(ENCODE_CALIB_NG);
    getWorkModel()->addEncodeCalibCount();
    return STATE_ENCODING_CALIB_MOTOR_STABLE;
  }

  LOG_INDEX_INFO("码盘标定成功");
  getWorkModel()->updateEncodeCalibState(ENCODE_CALIB_PASS);
  getWorkModel()->addEncodeCalibCount();
  return STATE_INIT_LIDAR;
}

int InitLidar::handle()
{
  LOG_INDEX_INFO("开始初始化雷达");
  RSFSCLog::getInstance(getLidarIndex())->setFileLogLevel(RSFSCLog::RSFSCLOG_LEVEL_DEBUG);
  if (!getWorkModel()->addMeasureMessage("fsm_init_lidar", getLidarManager()->startMonitorDifop()))
  {
    setWorkModelFailMsg("启动监控difop失败");
    getWorkModel()->updateAgingState(AGING_NG);
    return STATE_FAIL;
  }

  return STATE_STRESS_TEST;
}

int StressTest::waitAndValidateMsopPacket(uint32_t _timeout)
{
  int pkt_count       = 0;
  int pkt_error_count = 0;
  auto ptr_udp_client = std::make_unique<robosense::lidar::MechUdp>(1248);
  ptr_udp_client->setLogIndex(getLidarIndex());
  ptr_udp_client->regRecvCallback([&](const char* _udp_data) {
    const auto* msop_pkt = reinterpret_cast<const MsopPacket96*>(_udp_data);
    if (msop_pkt->pkt_head != MSOP_PKT_HEAD_LITTLE)
    {
      return;
    }

    if (getLidarManager()->isValidMsop(_udp_data))
    {
      pkt_count++;
      return;
    }
    pkt_error_count++;
  });
  ptr_udp_client->start(getLidarManager()->getIP(), getLidarManager()->getPort());
  sleep(_timeout);

  return pkt_count;
}

bool StressTest::checkData()
{

  if (!connectLidarCurrIP())
  {
    setWorkModelFailMsg("连接雷达失败");
    getWorkModel()->addMeasureMessage("fsm_connect_lidar", 0, rsfsc_lib::MeasureDataType::MEASURE_DATA_TYPE_INT);
    return false;
  }

  if (!getLidarManager()->getDifopData())
  {
    setWorkModelFailMsg("读取difop数据超时");
    getWorkModel()->addMeasureMessage("fsm_difop_timeout", getWorkModel()->getPastDifopTime(),
                                      rsfsc_lib::MeasureDataType::MEASURE_DATA_TYPE_INT);
    return false;
  }

  if (!getWorkModel()->checkAgingData())
  {
    setWorkModelFailMsg("压测数据超限");
    getWorkModel()->addDifopMonitorResult();
    return false;
  }

  return true;
}

int StressTest::handle()
{
  if (!getParaInfo().fsm_stress_test)
  {
    LOG_INDEX_INFO("跳过压测阶段");
    getWorkModel()->updateStressState(STRESS_SKIP);
    return STATE_INIT_READ_DATA;
  }

  getWorkModel()->updateStressState(STRESS_BUSY);
  LOG_INDEX_INFO("进入压测阶段");

  getWorkModel()->startTcpdumpExcludeMSOP();
  connectLidarCurrIP();
  if (getLidarManager()->isConnected())
  {
    sleep(5);
    getLidarManager()->disconnect();
  }

  int success_count = 0;

  const auto& para_aging = getWorkModel()->getParaInfo();

  getWorkModel()->startTcpdumpOnlyMSOP();
  for (size_t i = 1; i <= static_cast<size_t>(para_aging.stress_num); ++i)
  {
    msleep(100);
    if (isAbort())
    {
      break;
    }

    getWorkModel()->toggleRelay(para_aging.stress_toggle_interval);

    LOG_INDEX_INFO("等待{}s抓包", para_aging.stress_wait_start_time);
    auto pkt_count = waitAndValidateMsopPacket(para_aging.stress_wait_start_time);

    bool is_data_pass = checkData();
    if (pkt_count > 0 && is_data_pass)
    {
      LOG_INDEX_INFO("压测第{}次重启成功，接收到正常点云包{}帧", i, pkt_count);
      success_count++;
      getWorkModel()->updateStressCount(success_count);
      continue;
    }
    if (pkt_count == 0)
    {
      LOG_INDEX_INFO("压测第{}次重启失败，未接收到有效点云包", i, pkt_count);
    }
    if (!is_data_pass)
    {
      LOG_INDEX_INFO("压测第{}次重启失败，数据超限", i);
    }

    if (!para_aging.stress_allow_fail)
    {
      AppEvent::getInstance()->signalShowErrorMessageBox(
        QString("序号%1 压测阶段失败，不允许失败，将停止继续压测").arg(getLidarIndex()));
      break;
    }
  }
  // getWorkModel()->stopTcpdumpOnlyMSOP();
  // getWorkModel()->stopTcpdumpExcludeMSOP();

  getWorkModel()->updateStressCount(success_count);

  if (isAbort())
  {
    getWorkModel()->updateStressState(STRESS_ABORT);
    return STATE_ABORT;
  }

  if (!getWorkModel()->addMeasureMessage("fsm_stress_success_count", success_count,
                                         rsfsc_lib::MeasureDataType::MEASURE_DATA_TYPE_INT))
  {
    setWorkModelFailMsg(fmt::format("压测失败，成功次数{}，要求次数{}", success_count, para_aging.stress_num));
    getWorkModel()->updateStressState(STRESS_NG);
    return STATE_FAIL;
  }

  getWorkModel()->updateStressState(STRESS_PASS);

  return STATE_INIT_READ_DATA;
}

int InitReadAgingData::handle()
{
  if (!getParaInfo().fsm_aging)
  {
    LOG_INDEX_INFO("跳过老化阶段");
    getWorkModel()->updateAgingState(AGING_SKIP);
    return STATE_RESTORE_LIDAR_IP;
  }
  getLidarManager()->disconnect();

  if (!connectLidarCurrIP())
  {
    setWorkModelFailMsg("连接雷达失败");
    getWorkModel()->updateAgingState(AGING_NG);
    getWorkModel()->addMeasureMessage("fsm_init_read_data", false);
    return STATE_FAIL;
  }

  if (!getLidarManager()->startMonitorDifop())
  {
    setWorkModelFailMsg("启动监控difop失败");
    getWorkModel()->updateAgingState(AGING_NG);
    getWorkModel()->addMeasureMessage("fsm_init_read_data", false);
    return STATE_FAIL;
  }

  if (!waitForStartUpComplete())
  {
    setWorkModelFailMsg("初始化后雷达启动完成失败");
    getWorkModel()->updateAgingState(AGING_NG);
    getWorkModel()->addMeasureMessage("fsm_init_read_data", false);
    return STATE_FAIL;
  }

  getWorkModel()->startTcpdumpExcludeMSOP();

  sleep(5);
  getWorkModel()->addMeasureMessage("fsm_init_read_data", true);
  return STATE_READ_DATA;
}

int ReadAgingData::handle()
{
  // LOG_INDEX_DEBUG("进入老化读取状态");
  getWorkModel()->updateAgingState(AGING_BUSY);
  bool is_success = false;

  getWorkModel()->waitForDataCheck();

  if (getWorkModel()->getAgingState() == AGING_ABORT)
  {
    LOG_INDEX_WARN("老化被中止");
    getWorkModel()->updateAgingState(AGING_ABORT);
    setWorkModelFailMsg("手动终止");
    return STATE_ABORT;
  }

  if (!getOpticalErrorRate())
  {
    getWorkModel()->addMeasureMessage("fsm_light_test_state", false);
    setWorkModelFailMsg("光通误码测试存在失败项");
    getWorkModel()->updateAgingState(AGING_NG);
    return STATE_FAIL;
  }

  if (!getAgingData())
  {
    AppEvent::getInstance()->signalShowErrorMessageBox(QString("[%1] 老化阶段读取失败").arg(getLidarIndex()));

    setWorkModelFailMsg("读取雷达数据失败");
    getWorkModel()->updateAgingState(AGING_NG);
    return STATE_FAIL;
  }

  LOG_INDEX_INFO("获取监控数据成功");
  is_success = getWorkModel()->checkAgingData();
  getWorkModel()->appendAgingData();

  getWorkModel()->startTcpdumpOnlyMSOP();
  sleep(1);  // 为了抓到1帧difop包，才能加载到通道角度
  auto msop_vec = getLidarManager()->getMsopData(getParaInfo().check_msop_size);
  getWorkModel()->stopTcpdumpOnlyMSOP();

  bool msop_success = getWorkModel()->checkMsopData(msop_vec);
  if (!msop_success)
  {
    getWorkModel()->addMeasureMessage("fsm_msop_channel", 0, rsfsc_lib::MeasureDataType::MEASURE_DATA_TYPE_INT);
  }

  is_success &= msop_success;

  if (!is_success)
  {
    AppEvent::getInstance()->signalShowErrorMessageBox(QString("序号%1 老化阶段数据超限").arg(getLidarIndex()));
    getWorkModel()->addDifopMonitorResult();
    QString err_msg = "老化阶段数据超限";
    setWorkModelFailMsg("老化阶段数据超限");
    getWorkModel()->updateAgingState(AGING_NG);
    return STATE_FAIL;
  }
  LOG_INDEX_INFO("监控数据正常");

  if (getWorkModel()->getAgingFinish())
  {
    LOG_INDEX_INFO("老化阶段结束");
    getWorkModel()->addDifopMonitorResult();
    getWorkModel()->updateAgingState(AGING_PASS);
    return STATE_RESTORE_LIDAR_IP;
  }

  return STATE_READ_DATA;
}

bool ReadAgingData::getAgingData()  //由于经常出现获取雷达数据失败的现象,所以在失败时重新连接再读取数据,提高成功率
{
  LOG_INDEX_DEBUG("开始尝试获取数据");

  if (!getLidarManager()->getDifopData(getParaInfo().difop_timeout))
  {
    getWorkModel()->addMeasureMessage("fsm_difop_timeout", getWorkModel()->getPastDifopTime(),
                                      rsfsc_lib::MeasureDataType::MEASURE_DATA_TYPE_INT);
    return false;
  }
  return true;
}

bool ReadAgingData::getOpticalErrorRate()
{
  uint32_t up_error   = 0;
  uint32_t up_total   = 0;
  uint32_t down_error = 0;
  uint32_t down_total = 0;

  bool get_success = false;
  for (int i = 0; i < 3; ++i)
  {
    if (i > 0)
    {
      sleep(5);
      connectLidarCurrIP();
    }
    if (!getWorkModel()->getLidarManager()->stopUpOpticalErrorTest())
    {
      setWorkModelFailMsg("停止上行光通误码测试失败");
      getWorkModel()->addMeasureMessage("fsm_up_light_test_en", false);
      continue;
    }
    msleep(200);
    if (!getWorkModel()->getLidarManager()->startUpOpticalErrorTest())
    {
      setWorkModelFailMsg("启动上行光通误码测试失败");
      getWorkModel()->addMeasureMessage("fsm_up_light_test_en", false);
      continue;
    }

    LOG_INDEX_INFO("正在进行上行光通误码测试，等待{}s...", 15);
    sleep(15);
    if (!getWorkModel()->getLidarManager()->stopUpOpticalErrorTest())
    {
      setWorkModelFailMsg("停止上行光通误码测试失败");
      getWorkModel()->addMeasureMessage("fsm_up_light_test_en", false);
      continue;
    }

    msleep(1000);

    if (!getLidarManager()->getUpOpticalError(up_error, up_total))
    {
      setWorkModelFailMsg("获取上行光通误码数据失败");
      getWorkModel()->addMeasureMessage("fsm_up_light_test_en", false);
      continue;
    }
    LOG_INDEX_INFO("获取上行光通测试数据成功， 上行总数: {}, 上行误码数: {}", up_total, up_error);

    if (!getWorkModel()->getLidarManager()->stopDownOpticalErrorTest())
    {
      setWorkModelFailMsg("停止下行光通误码测试失败");
      getWorkModel()->addMeasureMessage("fsm_down_light_test_en", false);
      continue;
    }
    msleep(200);
    if (!getWorkModel()->getLidarManager()->startDownOpticalErrorTest())
    {
      setWorkModelFailMsg("启动下行光通误码测试失败");
      getWorkModel()->addMeasureMessage("fsm_down_light_test_en", false);
      continue;
    }
    LOG_INDEX_INFO("正在进行下行光通误码测试，等待{}s...", 15);
    sleep(15);
    if (!getLidarManager()->stopDownOpticalErrorTest())
    {
      setWorkModelFailMsg("停止下行光通误码测试失败");
      getWorkModel()->addMeasureMessage("fsm_down_light_test_en", false);
      continue;
    }
    msleep(1000);
    if (!getLidarManager()->getDownOpticalError(down_error, down_total))
    {
      setWorkModelFailMsg("获取下行光通误码数据失败");
      getWorkModel()->addMeasureMessage("fsm_down_light_test_en", false);
      continue;
    }
    LOG_INDEX_INFO("获取下行光通测试数据成功， 下行总数: {}, 下行误码数: {}", down_total, down_error);
    get_success = true;
    break;
  }

  if (!get_success)
  {
    LOG_INDEX_ERROR("3次获取光通误码数据失败");
    return false;
  }

  bool is_pass = true;
  if (up_total == 0)
  {
    is_pass = false;
    setWorkModelFailMsg("获取上行光通总数据量需要大于0");
  }

  double up_error_rate = static_cast<double>(up_error) / up_total;
  getWorkModel()->addMeasureMessage("fsm_up_light_test_total", up_total, rsfsc_lib::MEASURE_DATA_TYPE_HEX);
  getWorkModel()->addMeasureMessage("fsm_up_light_test_error", up_error, rsfsc_lib::MEASURE_DATA_TYPE_HEX);
  getWorkModel()->addMeasureMessage("fsm_up_light_test_error_rate", up_error_rate, rsfsc_lib::MEASURE_DATA_TYPE_FLOAT);

  if (up_error > 0)
  {
    is_pass = false;
    setWorkModelFailMsg(fmt::format("获取上行光通误码数为{}", up_error));
  }

  if (down_total == 0)
  {
    is_pass = false;
    setWorkModelFailMsg("获取下行光通总数据量需要大于0");
  }
  double down_error_rate = static_cast<double>(down_error) / down_total;

  getWorkModel()->addMeasureMessage("fsm_down_light_test_total", down_total, rsfsc_lib::MEASURE_DATA_TYPE_HEX);
  getWorkModel()->addMeasureMessage("fsm_down_light_test_error", down_error, rsfsc_lib::MEASURE_DATA_TYPE_HEX);
  getWorkModel()->addMeasureMessage("fsm_down_light_test_error_rate", down_error_rate,
                                    rsfsc_lib::MEASURE_DATA_TYPE_FLOAT);

  if (down_error > 0)
  {
    is_pass = false;
    setWorkModelFailMsg(fmt::format("获取下行光通误码数为{}", down_error));
  }

  if (!is_pass)
  {
    return false;
  }

  return true;
}

int DeInitReadAgingData::handle()
{
  if (!connectLidarCurrIP())
  {
    setWorkModelFailMsg("连接雷达失败");
    getWorkModel()->updateAgingState(AGING_NG);
    return STATE_FAIL;
  }

  if (!getLidarManager()->deInitLidarPara())
  {
    LOG_INDEX_ERROR("恢复雷达初始设置失败");
  }
  else
  {
    LOG_INDEX_INFO("已恢复雷达初始设置");
  }
  getLidarManager()->disconnect();

  LOG_INDEX_INFO("等待30s的重新启动");
  // 重启消除
  getWorkModel()->turnOffRelay();
  sleep(5);
  getWorkModel()->turnOnRelay();
  sleep(25);

  getWorkModel()->updateAgingState(AGING_PASS);
  return STATE_RESTORE_LIDAR_IP;
}

int RestoreLidarIp::handle()
{
  resetAbort();
  auto& para_info = getWorkModel()->getParaInfo();
  if (getWorkModel()->getNeedRestoreIp())
  {
    for (int i = 0; i < 5; ++i)
    {
      if (isAbort())
      {
        LOG_INDEX_WARN("恢复雷达IP被中止");
        getWorkModel()->updateAgingState(AGING_ABORT);
        setWorkModelFailMsg("手动终止");
        return STATE_ABORT;
      }
      LOG_INDEX_INFO("尝试恢复雷达IP为[{}:{}]", para_info.org_ip, para_info.org_msop_port);
      getLidarManager()->resetAbort();
      auto ip_addr = getLidarInfo()->getIP().toStdString();
      if (pingWait({ ip_addr }, 5000).empty())
      {
        LOG_INDEX_INFO("雷达未响应，正在重新启动雷达");
        msleep(100);
        getWorkModel()->turnOffRelay();
        sleep(5);
        getWorkModel()->turnOnRelay();
        pingWait({ ip_addr }, 40000);
      }

      if (connectLidarInfoIP())
      {
        if (!getLidarManager()->changeLidarNet(para_info.org_ip, para_info.org_msop_port, para_info.org_difop_port))
        {
          setWorkModelFailMsg("恢复雷达IP失败");
        }
        else
        {
          LOG_INDEX_INFO("恢复雷达IP成功");
          getWorkModel()->setNeedRestoreIp(false);
          sleep(5);
          break;
        }
      }
      else
      {
        LOG_INDEX_ERROR("恢复IP时候连接雷达失败[{}:{}]", getLidarInfo()->getIP(), getLidarInfo()->getMSOPPort());
        getWorkModel()->addMeasureMessage("fsm_restore_ip", false);
      }

      LOG_INDEX_ERROR("尝试第二次恢复雷达IP");
      getWorkModel()->turnOffRelay();
      LOG_INDEX_INFO("等待120s再次进行恢复");
      sleep(120);
      getWorkModel()->turnOnRelay();
      LOG_INDEX_INFO("等待10s");
      sleep(10);
    }
    if (!getWorkModel()->addMeasureMessage("fsm_restore_ip", !getWorkModel()->getNeedRestoreIp()))
    {
      LOG_INDEX_ERROR("经过5次尝试后，恢复IP失败");
      getWorkModel()->updateAgingState(AGING_NG);
      getWorkModel()->setNeedRestoreIp(false);
      return STATE_FAIL;
    }
    getWorkModel()->setNeedRestoreIp(false);
  }

  getWorkModel()->turnOffRelay();
  auto aging_state           = getWorkModel()->getAgingState();
  auto firmware_update_state = getWorkModel()->getFirmwareUpdateState();
  auto encode_calib_state    = getWorkModel()->getEncodeCalibState();
  auto stress_state          = getWorkModel()->getStressState();

  if (getLastMachineState() == STATE_FAIL || aging_state == AGING_NG || firmware_update_state == FIRMWARE_UPDATE_NG ||
      encode_calib_state == ENCODE_CALIB_NG || stress_state == STRESS_NG)
  {
    return STATE_FAIL;
  }
  if (getLastMachineState() == STATE_ABORT || aging_state == AGING_ABORT ||
      firmware_update_state == FIRMWARE_UPDATE_ABORT || encode_calib_state == ENCODE_CALIB_ABORT ||
      stress_state == STRESS_ABORT)
  {
    return STATE_ABORT;
  }

  if (aging_state == AGING_PASS)
  {
    return STATE_COOLING;
  }

  if (getLastMachineState() == STATE_SUCCESS)
  {
    return STATE_SUCCESS;
  }

  LOG_INDEX_INFO("last state: {}, aging state: {}", getLastMachineState(), getWorkModel()->getAgingState());
  return STATE_SUCCESS;
}

int AgingCooling::handle()
{
  getWorkModel()->updateAgingState(AGING_COOLING);
  auto colling_time_secs = getParaInfo().cooling_time_secs;
  if (getParaInfo().aging_time_secs < colling_time_secs)
  {
    colling_time_secs = 0;
  }
  LOG_INDEX_INFO("老化结束，开始降温，等待{}min", colling_time_secs);
  getWorkModel()->turnOffRelay();
  sleep(colling_time_secs * 60);
  getWorkModel()->updateAgingState(AGING_PASS);
  return STATE_SUCCESS;
}

int SuccessHandler::handleState()
{
  LOG_INDEX_INFO("运行成功");
  if (getLastMachineState() != ActionState::STATE_RESTORE_LIDAR_IP && getWorkModel()->getNeedRestoreIp())
  {
    return STATE_RESTORE_LIDAR_IP;
  }

  if (getWorkModel()->getRunState() == RUN_BUSY)
  {
    getWorkModel()->updateRunState(RUN_PASS);
  }

  app()->getMesWidget()->finishProcess(getLidarIndex(), LogTestStatus::LOG_TEST_STATUS_PASS);
  return STATE_FINAL;
}
int SuccessHandler::handle() { return STATE_FINAL; }

int FailHandler::handleState()
{
  if (getParaInfo().is_maintain_fail_env)
  {
    LOG_INDEX_ERROR("出现失败，已保留现场，请尽快排查处理");
    getWorkModel()->updateRunState(RUN_NG);
    app()->signalShowErrorMessageBox(QString("[%1] %2，已保留现场，请尽快排查处理").arg(getLidarIndex()).arg("失败"));
    app()->getMesWidget()->finishProcess(getLidarIndex(), rsfsc_lib::LOG_TEST_STATUS_FAIL,
                                         getWorkModel()->getFailLabel(), getWorkModel()->getFailMsg());
    return STATE_FINAL;
  }

  auto aging_state        = getWorkModel()->getAgingState();
  auto stress_state       = getWorkModel()->getStressState();
  auto update_state       = getWorkModel()->getFirmwareUpdateState();
  auto encode_calib_state = getWorkModel()->getEncodeCalibState();

  if (aging_state == AGING_ABORT || stress_state == STRESS_ABORT || update_state == FIRMWARE_UPDATE_ABORT ||
      encode_calib_state == ENCODE_CALIB_ABORT)
  {
    return STATE_ABORT;
  }

  if (getLastMachineState() != ActionState::STATE_RESTORE_LIDAR_IP)
  {
    getWorkModel()->setFailLabel("{}", getLastMachineState());
    if (getWorkModel()->getNeedRestoreIp())
    {
      return STATE_RESTORE_LIDAR_IP;
    }
  }
  app()->getMesWidget()->finishProcess(getLidarIndex(), LogTestStatus::LOG_TEST_STATUS_FAIL,
                                       getWorkModel()->getFailLabel(), getWorkModel()->getFailMsg());

  return STATE_FINAL;
}
int FailHandler::handle() { return STATE_FINAL; }

int AbortHandler::handleState()
{
  LOG_INDEX_WARN("老化中断");
  if (getWorkModel()->getNeedRestoreIp() && getLastMachineState() != ActionState::STATE_RESTORE_LIDAR_IP)
  {
    return STATE_RESTORE_LIDAR_IP;
  }
  getWorkModel()->updateRunState(RUN_ABORT);
  getWorkModel()->setFailMsg("老化中断");
  app()->getMesWidget()->finishProcess(getLidarIndex(), LogTestStatus::LOG_TEST_STATUS_ABORT,
                                       getWorkModel()->getFailLabel(), getWorkModel()->getFailMsg());
  auto firmware_update_state = getWorkModel()->getFirmwareUpdateState();
  auto encode_calib          = getWorkModel()->getEncodeCalibState();
  auto aging_state           = getWorkModel()->getAgingState();
  auto stress_state          = getWorkModel()->getStressState();

  if (firmware_update_state != FIRMWARE_UPDATE_NG && firmware_update_state != FIRMWARE_UPDATE_PASS &&
      firmware_update_state != FIRMWARE_UPDATE_SKIP)
  {
    getWorkModel()->updateFirmwareUpdateState(FIRMWARE_UPDATE_ABORT);
  }
  if (encode_calib != ENCODE_CALIB_NG && encode_calib != ENCODE_CALIB_SKIP && encode_calib != ENCODE_CALIB_PASS)
  {
    getWorkModel()->updateEncodeCalibState(ENCODE_CALIB_ABORT);
  }
  if ((aging_state != AGING_NG) && (aging_state != AGING_SKIP) && (aging_state != AGING_PASS))
  {
    getWorkModel()->updateAgingState(AGING_ABORT);
  }
  if ((stress_state != STRESS_NG) && (stress_state != STRESS_SKIP) && (stress_state != STRESS_PASS))
  {
    getWorkModel()->updateStressState(STRESS_ABORT);
  }
  return STATE_FINAL;
}
int AbortHandler::handle() { return STATE_FINAL; }

int FinalHandler::handleState()
{
  app()->signalLidarChangeIpIsReady(true, getLidarIndex());
  getLidarManager()->stopMonitorDifop();

  if (!getParaInfo().is_maintain_fail_env)
  {
    getLidarManager()->disconnect();
    getWorkModel()->turnOffRelay();
  }

  if (getWorkModel()->getRunState() == RUN_BUSY)
  {
    getWorkModel()->updateRunState(RUN_ABORT);
  }

  getWorkModel()->updateRunState();
  LOG_INDEX_DEBUG("状态机流程结束");
  RSFSCLog::getInstance(getLidarIndex())->resetFileSavePath();
  getWorkModel()->stopTcpdumpExcludeMSOP();
  getWorkModel()->stopTcpdumpOnlyMSOP();
  return STATE_END;
}
int FinalHandler::handle() { return STATE_END; }

}  // namespace lidar

}  // namespace robosense
