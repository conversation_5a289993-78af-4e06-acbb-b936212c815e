#include "rsfsc_comm_client/rsfsc_udp_client.h"
#include <chrono>

namespace robosense
{
namespace lidar
{

RsfscUdpClient::RsfscUdpClient(const std::string& _host, uint16_t _port) : host_(_host), port_(_port), socket_(nullptr)
{}

RsfscUdpClient::~RsfscUdpClient() { disconnect(); }

bool RsfscUdpClient::connect()
{
  try
  {
    socket_ = std::make_unique<boost::asio::ip::udp::socket>(ioContext());
    socket_->open(boost::asio::ip::udp::v4());

    remote_endpoint_ = boost::asio::ip::udp::endpoint(boost::asio::ip::address::from_string(host_), port_);

    is_connected_ = true;
    startIoContext();
    return true;
  }
  catch (const std::exception&)
  {
    socket_.reset();
    is_connected_ = false;
    return false;
  }
}

bool RsfscUdpClient::disconnect()
{
  if (socket_)
  {
    try
    {
      socket_->close();
    }
    catch (const std::exception&)
    {
      // 忽略关闭时的错误
    }
    socket_.reset();
  }
  is_connected_ = false;
  return true;
}

bool RsfscUdpClient::isConnected() const { return is_connected_ && socket_ && socket_->is_open(); }

std::size_t RsfscUdpClient::write(const std::vector<uint8_t>& _data)
{
  if (!isConnected())
  {
    return 0;
  }

  try
  {
    return socket_->send_to(boost::asio::buffer(_data), remote_endpoint_);
  }
  catch (const std::exception&)
  {
    return 0;
  }
}

std::size_t RsfscUdpClient::read(std::vector<uint8_t>& _data)
{
  if (!isConnected())
  {
    return 0;
  }

  try
  {
    boost::asio::ip::udp::endpoint sender_endpoint;
    return socket_->receive_from(boost::asio::buffer(_data), sender_endpoint);
  }
  catch (const std::exception&)
  {
    return 0;
  }
}

void RsfscUdpClient::asyncWrite(const std::vector<uint8_t>& _data, WriteHandler _handler)
{
  if (!isConnected())
  {
    _handler(boost::asio::error::not_connected, 0);
    return;
  }

  socket_->async_send_to(boost::asio::buffer(_data), remote_endpoint_, _handler);
}

void RsfscUdpClient::asyncRead(std::vector<uint8_t>& _data, ReadHandler _handler)
{
  if (!isConnected())
  {
    _handler(boost::asio::error::not_connected, 0);
    return;
  }

  boost::asio::ip::udp::endpoint sender_endpoint;
  socket_->async_receive_from(boost::asio::buffer(_data), sender_endpoint, _handler);
}

std::vector<uint8_t> RsfscUdpClient::request(const std::vector<uint8_t>& _request, uint32_t _timeout_ms)
{
  if (!write(_request))
  {
    return {};
  }

  std::vector<uint8_t> response(1024);
  auto deadline = std::chrono::system_clock::now() + std::chrono::milliseconds(_timeout_ms);

  while (std::chrono::system_clock::now() < deadline)
  {
    if (auto bytes_read = read(response))
    {
      response.resize(bytes_read);
      return response;
    }
  }

  return {};
}

}  // namespace lidar
}  // namespace robosense