#ifndef RSFSC_COMM_CLIENT_H
#define RSFSC_COMM_CLIENT_H

#include <atomic>
#include <boost/asio/executor_work_guard.hpp>
#include <boost/asio/io_context.hpp>
#include <functional>
#include <memory>
#include <thread>

// NOLINTNEXTLINE(modernize-concat-nested-namespaces)
namespace robosense
{
namespace lidar
{

struct TcpClientConfig
{
  std::string host;
  uint16_t port;
  uint32_t connect_timeout_ms;
};

struct SerialClientConfig
{
  std::string port_name;
  uint32_t baudrate;
  uint32_t connect_timeout_ms;
};

struct UdpClientConfig
{
  std::string host;
  uint16_t port;
  uint32_t connect_timeout_ms;
};

struct CommClientConfig
{
  TcpClientConfig tcp_config;
  SerialClientConfig serial_config;
  UdpClientConfig udp_config;
};

class RsfscCommClient
{
public:
  using ReadHandler    = std::function<void(const boost::system::error_code&, std::size_t)>;
  using WriteHandler   = std::function<void(const boost::system::error_code&, std::size_t)>;
  using ConnectHandler = std::function<void(const boost::system::error_code&)>;

  RsfscCommClient();
  virtual ~RsfscCommClient();

  // 禁用拷贝和移动
  RsfscCommClient(const RsfscCommClient&) = delete;
  RsfscCommClient& operator=(const RsfscCommClient&) = delete;
  RsfscCommClient(RsfscCommClient&&)                 = delete;
  RsfscCommClient& operator=(RsfscCommClient&&) = delete;

  // 纯虚函数接口
  virtual bool connect() = 0;
  bool connect(const TcpClientConfig& _tcp_config);
  bool connect(const SerialClientConfig& _serial_config);
  virtual bool disconnect()                      = 0;
  [[nodiscard]] virtual bool isConnected() const = 0;

  void setLogIndex(int _log_index) { log_index_ = _log_index; }
  [[nodiscard]] int getLogIndex() const { return log_index_; }

  virtual std::size_t write(const std::vector<uint8_t>& _data) = 0;
  virtual std::size_t read(std::vector<uint8_t>& _data)        = 0;

  virtual void asyncWrite(const std::vector<uint8_t>& _data, WriteHandler _handler) = 0;
  virtual void asyncRead(std::vector<uint8_t>& _data, ReadHandler _handler)         = 0;

  virtual std::vector<uint8_t> request(const std::vector<uint8_t>& _request, uint32_t _timeout_ms) = 0;

  virtual void abort() { is_aborted_.store(true); }
  [[nodiscard]] virtual bool isAborted() const { return is_aborted_.load(); }
  virtual void resetAbort() { is_aborted_.store(false); }

  void setConfig(const CommClientConfig& _config);
  [[nodiscard]] CommClientConfig getConfig() const;

  boost::asio::io_context& ioContext() { return *io_context_; }

private:
  int log_index_ = -1;
  std::string msg_header_;
  CommClientConfig config_;

  std::atomic<bool> io_running_;
  std::thread io_thread_;
  std::unique_ptr<boost::asio::io_context> io_context_;
  std::unique_ptr<boost::asio::executor_work_guard<boost::asio::io_context::executor_type>> work_guard_;
  std::atomic<bool> is_aborted_;

protected:
  void startIoContext();
  void stopIoContext();
};

}  // namespace lidar
}  // namespace robosense

#endif  // RSFSC_COMM_CLIENT_H
