#ifndef RSFSC_COMM_CLIENT_FACTORY_H
#define RSFSC_COMM_CLIENT_FACTORY_H

#include <memory>
#include <string>
#include <cstdint>
#include <optional>
#include <stdexcept>

// 包含必要的客户端和基类头文件
#include "rsfsc_comm_client/rsfsc_comm_client.h"
#include "rsfsc_comm_client/rsfsc_tcp_client.h"
#include "rsfsc_comm_client/rsfsc_udp_client.h"
#include "rsfsc_comm_client/rsfsc_serial_client.h"

namespace robosense
{
namespace lidar
{

class CommClientFactory
{
public:
    // --- 嵌套配置类型 ---

    // 1. 客户端类型枚举（嵌套）
    enum class CommClientType
    {
        TCP,
        UDP,
        SERIAL
    };

    // 2. 配置结构体（嵌套）
    struct CommClientConfig
    {
        CommClientType type; // 使用嵌套枚举

        // 网络参数 (TCP/UDP)
        std::optional<std::string> host;
        std::optional<uint16_t> port;

        // 串口参数
        std::optional<std::string> serial_port_name_;
        std::optional<uint32_t> baud_rate_;

        // 构造函数接收类型
        CommClientConfig(CommClientType t) : type(t) {}

        // 链式设置器
        CommClientConfig& setHost(const std::string& h) { host = h; return *this; }
        CommClientConfig& setPort(uint16_t p) { port = p; return *this; }
        CommClientConfig& setSerialPortName(const std::string& name) { serial_port_name_ = name; return *this; }
        CommClientConfig& setBaudRate(uint32_t rate) { baud_rate_ = rate; return *this; }
    };

    // --- 嵌套配置类型结束 ---

    // 删除构造函数，因为是静态类
    CommClientFactory() = delete;

    /**
     * @brief 创建通信客户端实例。
     * 客户端将创建并管理自己的内部 io_context 和线程。
     *
     * @param config 客户端配置参数（使用嵌套的 CommClientConfig）
     * @return std::unique_ptr<RsfscCommClient> 指向创建的客户端实例的指针
     * @throws std::invalid_argument 如果配置无效
     * @throws 可能抛出来自特定客户端构造函数的其他异常
     */
    static std::unique_ptr<RsfscCommClient> createClient(
        const CommClientConfig& config)
    {
        switch (config.type)
        {
        case CommClientType::TCP:
            if (!config.host || !config.port) throw std::invalid_argument("TCP 配置需要主机和端口。");
            return std::make_unique<RsfscTcpClient>(config.host.value(), config.port.value());

        case CommClientType::UDP:
            if (!config.host || !config.port) throw std::invalid_argument("UDP 配置需要主机和端口。");
            return std::make_unique<RsfscUdpClient>(config.host.value(), config.port.value());

        case CommClientType::SERIAL:
            if (!config.serial_port_name_ || !config.baud_rate_) throw std::invalid_argument("串口配置需要端口名称和波特率。");
            return std::make_unique<RsfscSerialClient>(config.serial_port_name_.value(), config.baud_rate_.value());

        default:
            throw std::invalid_argument("配置中指定了未知的客户端类型。");
        }
    }
};

} // namespace lidar
} // namespace robosense

#endif // RSFSC_COMM_CLIENT_FACTORY_H 