﻿/******************************************************************************
 * Copyright 2024 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai
 *
 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.
 *
 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONS<PERSON>QUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#ifndef CSV_UTILS_H
#define CSV_UTILS_H
#include "rsfsc_lib/include/csv_parser.h"
#include <QObject>
#include <vector>

// NOLINTNEXTLINE(modernize-concat-nested-namespaces)
namespace robosense
{
namespace lidar
{

class CsvUtils : public CsvParser
{
public:
  explicit CsvUtils();
  explicit CsvUtils(CsvUtils&&)      = delete;
  explicit CsvUtils(const CsvUtils&) = delete;
  CsvUtils& operator=(CsvUtils&&) = delete;
  CsvUtils& operator=(const CsvUtils&) = delete;
  virtual ~CsvUtils();

  bool loadRegisterCsvInfo(const QString& _file_path);
  bool loadLimitCsvInfo(const QString& _file_path);
  bool loadNormalCsvInfo(const QString& _file_path);
  template <typename T>
  std::vector<T> getNormalCsvData(const QString& _key);

  bool getAngleData(std::vector<std::vector<float>>& _angle_vec_vec);

  [[nodiscard]] std::vector<std::string> getLimitNameVec() const { return limit_name_vec_; }

private:
  // 存储csv文件的数据
  std::vector<QStringList> normal_data_str_list_vec_;

  std::vector<std::string> limit_name_vec_;
};

}  // namespace lidar
}  // namespace robosense
#endif  // CSV_UTILS_H