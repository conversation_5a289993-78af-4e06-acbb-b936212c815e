﻿#/******************************************************************************
 * Copyright 2024 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai
 *
 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.
 *
 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR <PERSON><PERSON><PERSON>QUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#include "comm_func/mes.h"
#include "rsfsc_lib/include/widget_log_setting.h"
#include "rsfsc_log/rsfsc_log_macro.h"
#include <QString>

// NOLINTNEXTLINE(modernize-concat-nested-namespaces)
namespace robosense
{
namespace lidar
{
namespace mech
{

std::optional<CustomerSN> requireCustomerSN(rsfsc_lib::WidgetLogSetting* _widget_log_setting, int _lidar_index)
{
  if (_widget_log_setting == nullptr)
  {
    RSFSCLog::getInstance(_lidar_index)
      ->error("[{}] 获取客户信息失败，_widget_log_setting为空", getFuncName(RS_PRETTY_FUNCTION));
    return {};
  }

  CustomerSN customer_sn;
  auto ask_type         = rsfsc_lib::AskData::ASK_TYPE_CALIB_ASK_CUSTOMER_SN;
  auto ask_item_key     = std::string("CustomerSn");
  auto ask_item_mac     = std::string("MAC");
  auto ask_item_md5     = std::string("ManuFirmwareRevision");
  auto ask_item_version = std::string("PLVersion");

  std::map<rsfsc_lib::AskData::AskType, std::vector<rsfsc_lib::AskData>> ads;
  ads[ask_type].emplace_back(ask_item_key,
                             std::initializer_list<std::string> { ask_item_mac, ask_item_md5, ask_item_version });
  if (!_widget_log_setting->requireData(_lidar_index, ads) || !ads[ask_type][0].is_ask_success ||
      ads.find(ask_type) == ads.end() || ads[ask_type].empty())
  {
    RSFSCLog::getInstance(_lidar_index)->error("[{}] 从MES获取CustomSN失败", getFuncName(RS_PRETTY_FUNCTION));
    return {};
  }
  auto ask_sn            = ads[ask_type];
  auto mac_address_q_str = QString::fromStdString(ask_sn.front().items[ask_item_mac]);
  mac_address_q_str.replace("-", ":");

  customer_sn.mac_addr         = mac_address_q_str.toStdString();
  customer_sn.firmware_version = ask_sn.front().items[ask_item_version];
  customer_sn.firmware_md5     = ask_sn.front().items[ask_item_md5];

  RSFSCLog::getInstance(_lidar_index)
    ->info("获取Mac地址成功: {}, 固件版本: {}, 固件MD5: {}", mac_address_q_str.toStdString(),
           customer_sn.firmware_version, customer_sn.firmware_md5);

  auto split_version = QString::fromStdString(customer_sn.firmware_version).split("_");
  if (split_version.size() != 5)
  {
    RSFSCLog::getInstance(_lidar_index)
      ->error("[{}] 固件版本格式错误: {}", getFuncName(RS_PRETTY_FUNCTION), customer_sn.firmware_version);
    return {};
  }

  customer_sn.top_version    = split_version.at(0).toUInt(nullptr, 16);
  customer_sn.bot_version    = split_version.at(1).toUInt(nullptr, 16);
  customer_sn.app_version    = split_version.at(2).toUInt(nullptr, 16);
  customer_sn.motor_version  = split_version.at(3).toUInt(nullptr, 16);
  customer_sn.config_version = split_version.at(4).toUInt(nullptr, 16);

  return customer_sn;
}

}  // namespace mech
}  // namespace lidar
}  // namespace robosense