﻿cmake_minimum_required(VERSION 3.10)

project(
  comm_func
  VERSION 1.0.0
  LANGUAGES CXX)

find_package(Qt5 COMPONENTS Core)

# 创建库
add_library(${PROJECT_NAME} STATIC)
add_library(rsfsc::${PROJECT_NAME} ALIAS ${PROJECT_NAME})

file(GLOB_RECURSE SOURCE_FILES CONFIGURE_DEPENDS src/*.cpp)

# 分别设置源文件
target_sources(${PROJECT_NAME} PRIVATE ${SOURCE_FILES})

# 设置包含目录
target_include_directories(${PROJECT_NAME} SYSTEM PUBLIC $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>
                                                         $<INSTALL_INTERFACE:include>)
target_include_directories(${PROJECT_NAME} PRIVATE ${CMAKE_SOURCE_DIR}/lib)
target_link_libraries(${PROJECT_NAME} Qt5::Core)
