﻿/******************************************************************************
 * Copyright 2024 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai
 *
 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.
 *
 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONS<PERSON>QUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#include "rsfsc_fsm/mech/mech_work_model.h"
#include "rsfsc_fsm/mech/lidar_manager.h"
#include "rsfsc_lib/include/widget_log_setting.h"
#include "rsfsc_log/rsfsc_log_macro.h"

// NOLINTNEXTLINE(modernize-concat-nested-namespaces)
namespace robosense
{
namespace lidar
{

MechWorkModel::~MechWorkModel() = default;

int MechWorkModel::getLidarIndex() { return getLidarManager()->getLidarInfo()->getLidarIndex(); }

void MechWorkModel::abort() { WorkModel::abort(); }
bool MechWorkModel::isAbort() { return WorkModel::isAbort(); }

LimitInfo MechWorkModel::getLimitInfo(const std::string& _name)
{
  auto limit_csv_utils = getLimitCsvUtils();
  if (limit_csv_utils == nullptr)
  {
    LOG_INDEX_ERROR("limit_csv_utils_ptr_ is nullptr，请查看limit文件是否被正确加载");
    return {};
  }
  return limit_csv_utils->getLimitInfo(_name);
}
bool MechWorkModel::addMeasureMessage(const std::string& _name, const bool _data)
{
  auto limit_info = getLimitInfo(_name);
  if (!limit_info.is_ok)
  {
    LOG_INDEX_ERROR("limit info 查询失败, 当前key: {}", _name);
    return false;
  }
  return getWidgetLogSetting()->addMeasureMessage(getLidarIndex(), limit_info, static_cast<int>(_data),
                                                  rsfsc_lib::MEASURE_DATA_TYPE_INT);
}
bool MechWorkModel::addMeasureMessage(const std::string& _name, const double _data)
{
  auto limit_info = getLimitInfo(_name);
  if (!limit_info.is_ok)
  {
    LOG_INDEX_ERROR("limit info 查询失败, 当前key: {}", _name);
    return false;
  }
  return getWidgetLogSetting()->addMeasureMessage(getLidarIndex(), limit_info, _data,
                                                  rsfsc_lib::MEASURE_DATA_TYPE_FLOAT);
}
bool MechWorkModel::addMeasureMessage(const std::string& _name, const float _data)
{
  auto limit_info = getLimitInfo(_name);
  if (!limit_info.is_ok)
  {
    LOG_INDEX_ERROR("limit info 查询失败, 当前key: {}", _name);
    return false;
  }
  return getWidgetLogSetting()->addMeasureMessage(getLidarIndex(), limit_info, _data,
                                                  rsfsc_lib::MEASURE_DATA_TYPE_FLOAT);
}
bool MechWorkModel::addMeasureMessage(const std::string& _name, const int _data)
{
  auto limit_info = getLimitInfo(_name);
  if (!limit_info.is_ok)
  {
    LOG_INDEX_ERROR("limit info 查询失败, 当前key: {}", _name);
    return false;
  }
  return getWidgetLogSetting()->addMeasureMessage(getLidarIndex(), limit_info, _data, rsfsc_lib::MEASURE_DATA_TYPE_INT);
}
bool MechWorkModel::addMeasureMessage(const std::string& _name, const uint32_t _data)
{
  auto limit_info = getLimitInfo(_name);
  if (!limit_info.is_ok)
  {
    LOG_INDEX_ERROR("limit info 查询失败, 当前key: {}", _name);
    return false;
  }
  return getWidgetLogSetting()->addMeasureMessage(getLidarIndex(), limit_info, _data, rsfsc_lib::MEASURE_DATA_TYPE_HEX);
}
bool MechWorkModel::addMeasureMessage(const std::string& _name, const std::string& _data)
{
  auto limit_info = getLimitInfo(_name);
  if (!limit_info.is_ok)
  {
    LOG_INDEX_ERROR("limit info 查询失败, 当前key: {}", _name);
    return false;
  }
  return getWidgetLogSetting()->addMeasureMessage(getLidarIndex(), limit_info, _data);
}

bool MechWorkModel::addMeasureMessage(const std::string& _name, const QVariant& _value)
{
  auto limit_info = getLimitInfo(_name);
  if (!limit_info.is_ok)
  {
    LOG_INDEX_ERROR("limit info 查询失败, 当前key: {}", _name);
    return false;
  }

  if (!_value.isValid())
  {
    LOG_INDEX_ERROR("QVariant value key 无效: {}", _name);
    return false;
  }

  switch (static_cast<QMetaType::Type>(_value.type()))
  {
  case QMetaType::Int:
  {
    return getWidgetLogSetting()->addMeasureMessage(getLidarIndex(), limit_info, _value.toInt(),
                                                    rsfsc_lib::MEASURE_DATA_TYPE_INT);
    break;
  }
  case QMetaType::UInt:
  {
    return getWidgetLogSetting()->addMeasureMessage(getLidarIndex(), limit_info, _value.toUInt(),
                                                    rsfsc_lib::MEASURE_DATA_TYPE_HEX);
    break;
  }
  case QMetaType::Double:
  case QMetaType::Float:
  {
    return getWidgetLogSetting()->addMeasureMessage(getLidarIndex(), limit_info, _value.toDouble(),
                                                    rsfsc_lib::MEASURE_DATA_TYPE_FLOAT);
    break;
  }
  case QMetaType::QString:
  {
    return getWidgetLogSetting()->addMeasureMessage(getLidarIndex(), limit_info, _value.toString().toStdString());
    break;
  }
  default:
  {
    LOG_INDEX_ERROR("未知的数据类型name: {}, type name: {},", limit_info.getName(), _value.typeName());
    return false;
  }
  break;
  }

  LOG_INDEX_ERROR("QVariant value key 无效: {}", _name);
  return false;
}

}  // namespace lidar
}  // namespace robosense