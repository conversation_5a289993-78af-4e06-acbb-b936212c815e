﻿/******************************************************************************
 * Copyright 2022 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#ifndef WORK_MODEL_H
#define WORK_MODEL_H

#include "rsfsc_utils/csv_utils.h"
#include "rsfsc_utils/decl_name.h"
#include <QVariant>
#include <atomic>
#include <memory>

namespace robosense  // NOLINT(modernize-concat-nested-namespaces)
{
namespace lidar
{

class LidarManager;

namespace rsfsc_lib
{
class WidgetLidarInfo;
}  // namespace rsfsc_lib

class WorkModel
{
public:
  DECL_CLASSNAME(WorkModel);
  explicit WorkModel() = default;
  explicit WorkModel(rsfsc_lib::WidgetLidarInfo* _lidar_info) :
    lidar_manager_ptr_(std::make_shared<LidarManager>(_lidar_info))
  {}
  WorkModel(const WorkModel& _other) = default;
  WorkModel& operator=(const WorkModel& _other) = default;
  WorkModel(WorkModel&& _other) noexcept        = default;
  WorkModel& operator=(WorkModel&& _other) noexcept = default;

public:
  virtual ~WorkModel() noexcept = default;

  std::shared_ptr<LidarManager> getLidarManager() { return lidar_manager_ptr_; }
  void setLidarManager(std::shared_ptr<LidarManager> _lidar_manager) { lidar_manager_ptr_ = std::move(_lidar_manager); }

  std::shared_ptr<CsvUtils> getLimitCsvUtils() { return limit_csv_utils_ptr_; }
  void setLimitCsvUtils(std::shared_ptr<CsvUtils> _limit_csv_utils)
  {
    limit_csv_utils_ptr_ = std::move(_limit_csv_utils);
  }

  [[nodiscard]] virtual int getLidarIndex() = 0;
  [[nodiscard]] int getLogIndex() { return getLidarIndex(); }

  virtual void abort() { is_abort_.store(true); }
  virtual bool isAbort() { return is_abort_.load(); };
  virtual void resetAbort() { is_abort_.store(false); }

private:
  std::atomic<bool> is_abort_ = false;
  std::shared_ptr<LidarManager> lidar_manager_ptr_;
  std::shared_ptr<CsvUtils> limit_csv_utils_ptr_;
};

}  // namespace lidar
}  // namespace robosense
#endif  // WORK_MODEL_H
