﻿/******************************************************************************
 * Copyright 2024 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai
 *
 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.
 *
 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONS<PERSON>QUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#ifndef AIRY_WORK_MODEL_H
#define AIRY_WORK_MODEL_H

#include "work_model.h"
#include <string>

// NOLINTNEXTLINE(modernize-concat-nested-namespaces)
namespace robosense
{
namespace lidar
{

namespace rsfsc_lib
{
class WidgetLidarInfo;
class WidgetLogSetting;
}  // namespace rsfsc_lib

class AiryWorkModel : public WorkModel
{
public:
  explicit AiryWorkModel(rsfsc_lib::WidgetLogSetting* _log_setting, rsfsc_lib::WidgetLidarInfo* _lidar_info) :
    WorkModel(_log_setting, _lidar_info)
  {}
  AiryWorkModel(const AiryWorkModel&)            = delete;
  AiryWorkModel(AiryWorkModel&&)                 = delete;
  AiryWorkModel& operator=(const AiryWorkModel&) = delete;
  AiryWorkModel& operator=(AiryWorkModel&&)      = delete;
  ~AiryWorkModel() override;

  void abort() override;

  bool addMeasureMessage(const std::string& _name, const bool _data);
  bool addMeasureMessage(const std::string& _name, const double _data);
  bool addMeasureMessage(const std::string& _name, const float _data);
  bool addMeasureMessage(const std::string& _name, const int _data);
  bool addMeasureMessage(const std::string& _name, const uint32_t _data);
  bool addMeasureMessage(const std::string& _name, const std::string& _data);
  bool addMeasureMessage(const std::string& _name, const QVariant& _value);

  LimitInfo getLimitInfo(const std::string& _name);

  void setFailMsg(const std::string& _fail_msg) { fail_msg_ = _fail_msg; }
  [[nodiscard]] const std::string& getFailMsg() const { return fail_msg_; }

private:
  std::string fail_msg_;
};

}  // namespace lidar
}  // namespace robosense
#endif  // AIRY_WORK_MODEL_H