﻿/******************************************************************************
 * Copyright 2023 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#ifndef AIRY_CRC32_PARSER_H
#define AIRY_CRC32_PARSER_H

#include "protocol/airy_parser.h"
#include <cstdint>

namespace robosense  //NOLINT(modernize-concat-nested-namespaces)
{
namespace lidar
{
class AiryCrc32Parser : public AiryParser
{
public:
  AiryCrc32Parser();
  AiryCrc32Parser(AiryCrc32Parser&&) noexcept = delete;
  AiryCrc32Parser(const AiryCrc32Parser&)     = delete;
  AiryCrc32Parser& operator=(AiryCrc32Parser&&) = delete;
  AiryCrc32Parser& operator=(const AiryCrc32Parser&) = delete;
  ~AiryCrc32Parser() override                        = default;

  std::string getCurrentProtocolType() override;
  std::vector<uint8_t> frameTailPack(std::vector<uint8_t>& _frame_array) override;
  bool parseClientPacket(std::vector<uint8_t>& _packet_buffer) override;
  bool parseServerPacket(std::vector<uint8_t>& _packet_buffer) override;

private:
};

}  // namespace lidar
}  // namespace robosense

#endif  //AIRY_CRC32_PARSER_H
