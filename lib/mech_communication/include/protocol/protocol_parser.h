﻿/******************************************************************************
 * Copyright 2023 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#ifndef PROTOCOL_PARSER_H
#define PROTOCOL_PARSER_H

#include <algorithm>
#include <cstdint>
#include <cstring>
#include <map>
#include <mutex>
#include <queue>
#include <string>
#include <vector>

// NOLINTNEXTLINE(modernize-concat-nested-namespaces)
namespace robosense
{
namespace lidar
{

namespace helios
{
struct ConfigPara;
}  // namespace helios

namespace mech
{
struct IntensityData;

enum class ResponseType
{
  SUCCESS                                  = 0x00,  // 成功
  UNSUPPORTED                              = 0x01,  // 不支持
  PARAMETER_ERROR                          = 0x02,  // 参数错误
  DATA_LEN_ERROR                           = 0x03,  // 数据长度错误
  FORMAT_ERROR                             = 0x04,  // 格式错误
  CHECKSUM_ERROR                           = 0x05,  // 校验和错误
  OTHER                                    = 0x06,  // 其他错误
  TIMEOUT                                  = 0x07,  // 超时
  CURRENT_STATUS                           = 0x08,  // 当前状态
  VERSION_UNSUPPORTED                      = 0x09,  // 版本不支持
  TOP_LINK_ERR_1                           = 0x0a,  // 顶板链接错误1
  TOP_LINK_ERR_2                           = 0x0b,  // 顶板链接错误2
  TOP_ERASE_ERR_1                          = 0x0c,  // 顶板擦除错误1
  TOP_ERASE_ERR_2                          = 0x0d,  // 顶板擦除错误2
  SEQUENCE_ERR                             = 0x0e,  // 包序号错误
  TOP_WRITE_ERR_1                          = 0x0f,  // 顶板写入错误1
  TOP_WRITE_ERR_2                          = 0x10,  // 顶板写入错误2
  COMM_REQ_ERR                             = 0x11,  // 通信请求错误
  COMM_FILE_SIZE_ERR                       = 0x12,  // 文件大小错误
  COMM_ERASE_FLS_ERR                       = 0x13,  // 擦除flash错误
  COMM_DATA_LEN_ERR                        = 0x14,  // 数据长度错误
  COMM_FIRMWARE_TYPE_ERR                   = 0x15,  // 固件类型错误
  COMM_OP_FLS_ERR                          = 0x16,  // flash操作错误
  COMM_COMP_CHK_ERR                        = 0x17,  // 兼容性校验错误
  COMM_OP_FLS_CMP_ERR                      = 0x18,  // flash操作回读错误
  PS2MOT_START_CODE_WHEEL_CALI_FAILED      = 0x19,  // 电机发送开始标定指令失败
  MOT2PS_MOT_CODE_WHEEL_CALI_STATUS_FAILED = 0x1a,  // 电机发送获取标定状态失败
  MOT_CODE_WHEEL_CALI_TIMEOUT              = 0x1b,  // 标定返回超时
  MOT2PS_MOT_E_ZERO_FAILED                 = 0x1c,  // 电机发送E-zero指令失败
  MOT_CODE_WHEEL_CALI_E_ZERO_TIMEOUT       = 0x1d,  // E-zero获取超时
};

}  // namespace mech

enum class ProtocolType
{
  MECH_HELIOS,
  MECH_BPEARL,
  MECH_RUBY,
  MECH_RUBY_OLD,
  MECH_AIRY,
  MECH_AIRY_CRC32,
  UNKNOWN
};
struct RegisterData
{
  uint32_t address;
  uint32_t value;
  // NOLINTNEXTLINE
  RegisterData(uint32_t _address = 0, uint32_t _value = 0) : address(_address), value(_value) {}
};

struct ExpectedResp
{
  uint32_t cmd;
  std::vector<uint8_t> data;
};

class ProtoParser
{
public:
  struct RegisterInfo
  {
    uint32_t address;
    uint32_t bit_width_mask;
    std::string formula;
    // NOLINTNEXTLINE
    explicit RegisterInfo(const uint32_t _address        = 0,
                          const uint32_t _bit_width_mask = 0xffffffff,
                          const std::string& _formula    = "") :
      address(_address), bit_width_mask(_bit_width_mask), formula(_formula)
    {}
  };

public:
  ProtoParser()                       = default;
  ProtoParser(ProtoParser&&) noexcept = delete;
  ProtoParser(const ProtoParser&)     = delete;
  ProtoParser& operator=(ProtoParser&&) = delete;
  ProtoParser& operator=(const ProtoParser&) = delete;
  virtual ~ProtoParser()                     = default;

  std::queue<std::pair<uint32_t, std::vector<uint8_t>>> payload_queue;
  std::queue<uint32_t> register_value_queue;
  std::queue<uint32_t> register_addr_queue;
  std::queue<std::vector<uint8_t>> universal_queue;
  bool is_config_register_read_finish = false;

  [[nodiscard]] int getLogIndex() const { return log_index; }
  void setLogIndex(const int _index) { log_index = _index; }

  void setRegAddr(const std::string& _reg_name, const ProtoParser::RegisterInfo& _register_info);
  void clear();

  virtual bool parseClientPacket(std::vector<uint8_t>& _packet_buffer) = 0;
  virtual bool parseServerPacket(std::vector<uint8_t>& _packet_buffer) = 0;

  // NOLINTNEXTLINE(bugprone-easily-swappable-parameters)
  virtual std::vector<uint8_t> requestFrameHeadPack(const uint32_t _cmd_type, const uint32_t _payload_size) = 0;
  virtual std::vector<uint8_t> responseFrameHeadPack(const uint32_t _cmd_type,
                                                     const mech::ResponseType _response_type,
                                                     const uint32_t _payload_size)                          = 0;
  virtual std::vector<uint8_t> frameTailPack(std::vector<uint8_t>& _frame_array)                            = 0;
  std::vector<uint8_t> packRequest(const uint32_t _cmd_type, const std::vector<uint8_t>& _payload);
  std::vector<uint8_t> packResponse(const uint32_t _cmd_type,
                                    const mech::ResponseType _response_type,
                                    const std::vector<uint8_t>& _payload);

  static std::string hex(std::vector<uint8_t> _data, uint32_t _display_max_len = 100);
  static std::string hex(uint32_t _data);

  static std::vector<uint8_t>::iterator findFrameFlag(std::vector<uint8_t>& _buffer, uint32_t _frame_flag);

  template <typename T>
  static inline typename std::enable_if<std::is_trivially_copyable<T>::value, void>::type
  copyToPayload(std::vector<uint8_t>& _buffer, const T& _source, size_t& _index)
  {
    // static_assert(std::is_trivially_copyable<T>::value, "Type must be trivially copyable");
    std::memcpy(&_buffer.at(_index), &_source, sizeof(T));
    _index += sizeof(T);
  }
  template <typename T>
  static inline typename std::enable_if<std::is_trivially_copyable<T>::value, void>::type
  copyReverseToPayload(std::vector<uint8_t>& _buffer, const T& _source, size_t& _index)
  {
    static_assert(std::is_trivially_copyable<T>::value, "type must be trivially copyable");

    auto source_ptr = static_cast<const std::array<uint8_t, sizeof(T)>*>(static_cast<const void*>(&_source));
    std::reverse_copy(source_ptr->begin(), source_ptr->end(), _buffer.begin() + static_cast<uint32_t>(_index));

    _index += sizeof(T);
  }

  template <typename T>
  static inline void copyArrayToPayload(std::vector<uint8_t>& _buffer,
                                        const T* _source,
                                        size_t _element_num,
                                        size_t& _index)
  {
    // static_assert(std::is_trivially_copyable<T>::value, "Type must be trivially copyable");
    std::memcpy(&_buffer.at(_index), _source, _element_num * sizeof(T));
    _index += _element_num * sizeof(T);
  }
  template <typename... Args>
  static inline void pushToPayload(std::vector<uint8_t>& _buffer, Args&&... _args)
  {
    size_t size                                 = 0;
    std::array<int, sizeof...(_args) + 1> dummy = { 0, (size += sizeof(_args), 0)... };
    static_cast<void>(dummy);  // Avoid unused variable warning
    size_t index = _buffer.size();
    _buffer.resize(_buffer.size() + size);
    std::array<int, sizeof...(_args) + 1> dummy2 = { 0,
                                                     (copyToPayload(_buffer, std::forward<Args>(_args), index), 0)... };
    static_cast<void>(dummy2);  // Avoid unused variable warning
  }
  template <typename... Args>
  static inline void pushReverseToPayload(std::vector<uint8_t>& _buffer, Args&&... _args)
  {
    size_t size                                 = 0;
    std::array<int, sizeof...(_args) + 1> dummy = { 0, (size += sizeof(_args), 0)... };
    static_cast<void>(dummy);  // Avoid unused variable warning
    size_t index = _buffer.size();
    _buffer.resize(_buffer.size() + size);
    std::array<int, sizeof...(_args) + 1> dummy2 = {
      0, (copyReverseToPayload(_buffer, std::forward<Args>(_args), index), 0)...
    };
    static_cast<void>(dummy2);  // Avoid unused variable warning
  }

  // copyFromPayload
  template <typename T>
  static inline typename std::enable_if<std::is_trivially_copyable<T>::value, void>::type
  copyFromPayload(const std::vector<uint8_t>& _buffer, T& _dest, size_t& _index)
  {
    // static_assert(std::is_trivially_copyable<T>::value, "Type must be trivially copyable");
    std::memcpy(&_dest, &_buffer.at(_index), sizeof(T));
    _index += sizeof(T);
  }

  template <typename T>
  static inline typename std::enable_if<std::is_trivially_copyable<T>::value, void>::type
  copyReverseFromPayload(const std::vector<uint8_t>& _buffer, T& _dest, size_t& _index)
  {
    static_assert(std::is_trivially_copyable<T>::value, "type must be trivially copyable");

    auto dest_ptr = static_cast<std::array<uint8_t, sizeof(T)>*>(static_cast<void*>(&_dest));
    std::reverse_copy(_buffer.begin() + static_cast<uint32_t>(_index),
                      _buffer.begin() + static_cast<uint32_t>(_index) + sizeof(T), dest_ptr->begin());

    _index += sizeof(T);
  }

  template <typename T>
  static inline void copyArrayFromPayload(const std::vector<uint8_t>& _buffer,
                                          T* _dest,
                                          size_t _element_num,
                                          size_t& _index)
  {
    // static_assert(std::is_trivially_copyable<T>::value, "Type must be trivially copyable");
    std::memcpy(_dest, &_buffer.at(_index), _element_num * sizeof(T));
    _index += _element_num * sizeof(T);
  }

  /**
   * @brief     根据_register_info的name，获取到对应的寄存器信息
   * 
   * @param     _register_info     返回的寄存器信息
   * @return    true               
   * @return    false              未找到该寄存器信息
  **/
  bool getRegisterInfo(const std::string& _reg_name, RegisterInfo& _register_info);

  /**
   * @brief     根据mask和公式演算实际数值
   * 
   * @param     _value             寄存器读取到的原始数值
   * @param     _register_info     该寄存器数值
   * @param     _result            返回的演算结果
   * @return    true               成功
   * @return    false              失败
  **/
  bool evalRegisterInfo(uint32_t _value, const RegisterInfo& _register_info, double& _result);

  virtual bool packReadRegister(const std::vector<uint32_t>& _reg_addr,
                                uint32_t& _expected_packet_response_code,
                                std::vector<uint8_t>& _packet)      = 0;
  virtual bool packReadConRegData(const uint32_t _start_reg_addr,
                                  const uint32_t _reg_num,
                                  uint32_t& _expected_packet_response_code,
                                  std::vector<uint8_t>& _packet)    = 0;
  virtual bool packReadConTopRegData(const uint32_t _start_reg_addr,
                                     const uint32_t _reg_num,
                                     uint32_t& _expected_packet_response_code,
                                     std::vector<uint8_t>& _packet) = 0;
  virtual bool packWriteConRegData(const uint32_t _start_reg_addr,
                                   const std::vector<uint32_t>& _reg_val,
                                   uint32_t& _expected_packet_response_code,
                                   std::vector<uint8_t>& _packet)   = 0;
  // NOLINTNEXTLINE(bugprone-easily-swappable-parameters)
  virtual bool packWriteRegister(const std::vector<uint32_t>& _reg_addr,
                                 const std::vector<uint32_t>& _reg_val,
                                 uint32_t& _expected_packet_response_code,
                                 std::vector<uint8_t>& _packet) = 0;
  virtual bool packWriteRegister(const std::vector<RegisterData>& _reg_data_vec,
                                 uint32_t& _expected_packet_response_code,
                                 std::vector<uint8_t>& _packet) = 0;

  virtual bool packWriteDigitalRegister(const RegisterData _register_data,
                                        uint32_t& _expected_packet_response_code,
                                        std::vector<uint8_t>& _packet) = 0;
  virtual bool packReadDigitalRegister(const uint32_t _reg_addr,
                                       uint32_t& _expected_packet_response_code,
                                       std::vector<uint8_t>& _packet)  = 0;

  virtual bool packGetEyesSafe(uint32_t& _expected_packet_response_code, std::vector<uint8_t>& _packet) = 0;

  virtual bool packSetEyesSafe(const uint32_t _is_open,
                               uint32_t& _expected_packet_response_code,
                               std::vector<uint8_t>& _packet) = 0;

  virtual bool packReadConfigPara(uint32_t& _expected_packet_response_code, std::vector<uint8_t>& _packet) = 0;

  virtual bool packWriteConfigPara(const helios::ConfigPara& /*_config_paramater*/,
                                   uint32_t& /*_expected_packet_response_code*/,
                                   std::vector<uint8_t>& /*_packet*/)
  {
    return false;
  };

  virtual bool packStartupCheck(uint32_t& _expected_packet_response_code, std::vector<uint8_t>& _packet) = 0;

  virtual bool packPSDefinedReady(uint32_t& /*_expected_packet_response_code*/, std::vector<uint8_t>& /*_packet*/)
  {
    error_str = "ps defined ready is not supported";
    return false;
  }

  virtual bool packReadCmd(const uint32_t _cmd_type,
                           const std::vector<uint8_t>& _data,
                           uint32_t& _expected_packet_response_code,
                           std::vector<uint8_t>& _packet) = 0;

  // NOLINTNEXTLINE(bugprone-easily-swappable-parameters)
  virtual bool packWriteCmd(const uint32_t _cmd_type,
                            const std::vector<uint8_t>& _data,
                            uint32_t& _expected_packet_response_code,
                            std::vector<uint8_t>& _packet) = 0;

  // NOLINTNEXTLINE(bugprone-easily-swappable-parameters)
  virtual bool packWriteIpPort(const uint32_t /*_ip*/,
                               uint16_t /*_msop_port*/,
                               uint16_t /*_difop_port*/,
                               uint32_t& /*_expected_packet_response_code*/,
                               std::vector<uint8_t>& /*_packet*/)
  {
    error_str = "write ip port is not supported";
    return false;
  };

  virtual bool packWriteTopRegister(const std::vector<uint32_t>& _reg_addr,
                                    const std::vector<uint32_t>& _reg_val,
                                    uint32_t& _expected_packet_response_code,
                                    std::vector<uint8_t>& _packet)   = 0;
  virtual bool packWriteTopRegister(const std::vector<RegisterData>& _reg_addr,
                                    uint32_t& _expected_packet_response_code,
                                    std::vector<uint8_t>& _packet)   = 0;
  virtual bool packWriteConTopRegData(const uint32_t _start_reg_addr,
                                      const std::vector<uint32_t>& _reg_val,
                                      uint32_t& _expected_packet_response_code,
                                      std::vector<uint8_t>& _packet) = 0;

  virtual bool packReadTopRegister(const std::vector<uint32_t>& _reg_data,
                                   uint32_t& _expected_packet_response_code,
                                   std::vector<uint8_t>& _packet) = 0;

  virtual std::string getCurrentProtocolType() = 0;

  virtual bool isLidarStartUp(const uint32_t _value) = 0;
  virtual bool isPSDefinedReady(const std::vector<uint8_t>& /*_value_vec*/)
  {
    error_str = "ps defined ready is not supported";
    return false;
  }

  virtual bool packReadIntensity(const mech::IntensityData& _intensity_data,
                                 uint32_t& _expected_packet_response_code,
                                 std::vector<uint8_t>& _packet) = 0;

  virtual bool getTxChannelRegAddr(const int _channel_num, uint32_t& _tx_channel_reg_addr)                   = 0;
  virtual bool packCtrlTxChannelExclusively(const int _channel_num,
                                            const bool _is_open,
                                            uint32_t& _expected_packet_response_code,
                                            std::vector<uint8_t>& _packet)                                   = 0;
  virtual bool packReadTxChannelAll(uint32_t& _expected_packet_response_code, std::vector<uint8_t>& _packet) = 0;
  virtual bool packCtrlTxChannelAll(const bool _open,
                                    uint32_t& _expected_packet_response_code,
                                    std::vector<uint8_t>& _packet)                                           = 0;
  virtual bool packCtrlTxChannel(const int _channel_num,
                                 const bool _open,
                                 const uint32_t _curr_value,
                                 uint32_t& _expected_packet_response_code,
                                 std::vector<uint8_t>& _packet)                                              = 0;

  virtual bool packStartWriteTopFlash(const uint32_t _start_addr,
                                      const uint32_t _len,
                                      ExpectedResp& _expected_resp,
                                      std::vector<uint8_t>& _packet)                                = 0;
  virtual bool packWriteTopFlash(const uint32_t _pkt_count,
                                 const std::vector<uint8_t>& _data,
                                 ExpectedResp& _expected_resp,
                                 std::vector<uint8_t>& _packet)                                     = 0;
  virtual bool packFinishWriteTopFlash(ExpectedResp& _expected_resp, std::vector<uint8_t>& _packet) = 0;

  virtual bool extractData(const uint32_t _response_code, const std::vector<uint8_t>& _payload) = 0;
  virtual bool extractRegister(const std::vector<uint8_t>& _payload)                            = 0;

  virtual bool isValidMsop(const char* _packet) = 0;

  std::string error_str = "unkown error";
  std::map<std::string, RegisterInfo> register_info_map;
  int log_index = -1;
  std::mutex queue_mutex;

private:
};

}  // namespace lidar
}  // namespace robosense

#endif  // PROTOCOL_PARSER_H
