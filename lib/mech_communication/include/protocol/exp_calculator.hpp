﻿/******************************************************************************
 * Copyright 2023 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#pragma once

#include <string>

class ExpCalculator
{
public:
  ExpCalculator()                              = default;
  ~ExpCalculator()                             = default;
  explicit ExpCalculator(ExpCalculator&&)      = delete;
  explicit ExpCalculator(const ExpCalculator&) = delete;
  ExpCalculator& operator=(ExpCalculator&&) = delete;
  ExpCalculator& operator=(const ExpCalculator&) = delete;

public:
  [[nodiscard]] double getResult() const { return result_; }
  bool evaluate(const std::string& _exp = "")
  {
    if (_exp.empty())
    {
      is_success_ = false;
      return is_success_;
    }

    index_      = 0;
    expression_ = _exp;
    is_success_ = true;

    try
    {
      result_ = parse();
    }
    catch (...)
    {
      is_success_ = false;
    }
    return is_success_;
  }

private:
  std::string expression_;
  bool is_success_ = true;
  double result_   = 0.0;
  size_t index_    = 0;

  char peek()
  {
    if (index_ < expression_.size())
    {
      return expression_.at(index_);
    }
    return '\0';
  }

  char get()
  {
    if (index_ < expression_.size())
    {
      return expression_.at(index_++);
    }
    return '\0';
  }

  double number()
  {
    std::string number;
    number += get();
    while ((peek() >= '0' && peek() <= '9') || peek() == '.')
    {
      number += get();
    }
    return std::stod(number);
  }

  double factor()
  {
    if (peek() >= '0' && peek() <= '9')
    {
      return number();
    }
    if (peek() == '(')
    {
      get();  // '('
      double res = parse();
      get();  // ')'
      return res;
    }
    if (peek() == '-')
    {
      get();
      return -factor();
    }
    is_success_ = false;
    return 0;  // error
  }

  double term()
  {
    double res = factor();
    while (peek() == '*' || peek() == '/')
    {
      if (get() == '*')
      {
        res *= factor();
      }
      else
      {
        auto temp = factor();
        if (temp == 0)
        {
          is_success_ = false;
        }
        else
        {
          res /= temp;
        }
      }
    }

    return res;
  }

  double parse()
  {
    double res = term();
    while (peek() == '+' || peek() == '-')
    {
      if (get() == '+')
      {
        res += term();
      }
      else
      {
        res -= term();
      }
    }
    return res;
  }
};
