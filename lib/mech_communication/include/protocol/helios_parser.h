﻿/******************************************************************************
 * Copyright 2023 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#ifndef HELIOS_PARSER_H
#define HELIOS_PARSER_H

#include "protocol_parser.h"
#include <cstdint>
#include <vector>

namespace robosense  //NOLINT(modernize-concat-nested-namespaces)
{
namespace lidar
{

class HeliosParser : public ProtoParser
{
public:
  explicit HeliosParser()                    = default;
  explicit HeliosParser(HeliosParser&&)      = delete;
  explicit HeliosParser(const HeliosParser&) = delete;
  HeliosParser& operator=(HeliosParser&&) = delete;
  HeliosParser& operator=(const HeliosParser&) = delete;
  ~HeliosParser() override                     = default;

  std::string getCurrentProtocolType() override { return "Helios"; };

  bool parseClientPacket(std::vector<uint8_t>& _packet_buffer) override;
  bool parseServerPacket(std::vector<uint8_t>& _packet_buffer) override;

  bool extractRegister(const std::vector<uint8_t>& _payload) override;
  bool extractConfigPara(const std::vector<uint8_t>& _payload);
  bool extractEyesSafe(const std::vector<uint8_t>& _payload);

  bool extractData(const uint32_t _response_code, const std::vector<uint8_t>& _payload) override;

  std::vector<uint8_t> requestFrameHeadPack(const uint32_t _cmd_type, const uint32_t _payload_size) override;
  std::vector<uint8_t> responseFrameHeadPack(const uint32_t _cmd_type,
                                             const mech::ResponseType _response_type,
                                             const uint32_t _payload_size) override;
  std::vector<uint8_t> frameTailPack(std::vector<uint8_t>& _frame_array) override;

  bool packReadRegister(const std::vector<uint32_t>& _reg_addr,
                        uint32_t& _expected_packet_response_code,
                        std::vector<uint8_t>& _packet) override;

  bool packWriteRegister(const std::vector<uint32_t>& _reg_addr,
                         const std::vector<uint32_t>& _reg_val,
                         uint32_t& _expected_packet_response_code,
                         std::vector<uint8_t>& _packet) override;
  bool packWriteRegister(const std::vector<RegisterData>& _reg_data_vec,
                         uint32_t& _expected_packet_response_code,
                         std::vector<uint8_t>& _packet) override;

  bool packWriteTopRegister(const std::vector<uint32_t>& _reg_addr,
                            const std::vector<uint32_t>& _reg_val,
                            uint32_t& _expected_packet_response_code,
                            std::vector<uint8_t>& _packet) override;
  bool packWriteTopRegister(const std::vector<RegisterData>& _reg_data,
                            uint32_t& _expected_packet_response_code,
                            std::vector<uint8_t>& _packet) override;

  bool packReadTopRegister(const std::vector<uint32_t>& _reg_addr,
                           uint32_t& _expected_packet_response_code,
                           std::vector<uint8_t>& _packet) override;

  bool packReadConRegData(const uint32_t _start_reg_addr,
                          const uint32_t _reg_num,
                          uint32_t& _expected_packet_response_code,
                          std::vector<uint8_t>& _packet) override;
  bool packReadConTopRegData(const uint32_t _start_reg_addr,
                             const uint32_t _reg_num,
                             uint32_t& _expected_packet_response_code,
                             std::vector<uint8_t>& _packet) override;

  bool packWriteConRegData(const uint32_t _start_reg_addr,
                           const std::vector<uint32_t>& _reg_val,
                           uint32_t& _expected_packet_response_code,
                           std::vector<uint8_t>& _packet) override;
  bool packWriteConTopRegData(const uint32_t _start_reg_addr,
                              const std::vector<uint32_t>& _reg_val,
                              uint32_t& _expected_packet_response_code,
                              std::vector<uint8_t>& _packet) override;

  bool packWriteDigitalRegister(const RegisterData _register_data,
                                uint32_t& _expected_packet_response_code,
                                std::vector<uint8_t>& _packet) override;
  bool packReadDigitalRegister(const uint32_t _reg_addr,
                               uint32_t& _expected_packet_response_code,
                               std::vector<uint8_t>& _packet) override;

  bool packGetEyesSafe(uint32_t& _expected_packet_response_code, std::vector<uint8_t>& _packet) override;

  bool packSetEyesSafe(const uint32_t _is_open,
                       uint32_t& _expected_packet_response_code,
                       std::vector<uint8_t>& _packet) override;

  bool packReadConfigPara(uint32_t& _expected_packet_response_code, std::vector<uint8_t>& _packet) override;
  bool packWriteConfigPara(const helios::ConfigPara& _config_paramater,
                           uint32_t& _expected_packet_response_code,
                           std::vector<uint8_t>& _packet) override;

  bool packReadCmd(const uint32_t _cmd_type,
                   const std::vector<uint8_t>& _data,
                   uint32_t& _expected_packet_response_code,
                   std::vector<uint8_t>& _packet) override;

  bool packWriteCmd(const uint32_t _cmd_type,
                    const std::vector<uint8_t>& _data,
                    uint32_t& _expected_packet_response_code,
                    std::vector<uint8_t>& _packet) override;

  bool packStartupCheck(uint32_t& _expected_packet_response_code, std::vector<uint8_t>& _packet) override;

  bool isLidarStartUp(const uint32_t _value) override;

  bool packReadIntensity(const mech::IntensityData& _intensity_data,
                         uint32_t& _expected_packet_response_code,
                         std::vector<uint8_t>& _packet) override;

  bool packCtrlTxChannelExclusively(const int _channel_num,
                                    const bool _is_open,
                                    uint32_t& _expected_packet_response_code,
                                    std::vector<uint8_t>& _packet) override;
  bool packReadTxChannelAll(uint32_t& _expected_packet_response_code, std::vector<uint8_t>& _packet) override;
  bool packCtrlTxChannel(const int _channel_num,
                         const bool _open,
                         const uint32_t _curr_value,
                         uint32_t& _expected_packet_response_code,
                         std::vector<uint8_t>& _packet) override;
  bool packCtrlTxChannelAll(const bool _open,
                            uint32_t& _expected_packet_response_code,
                            std::vector<uint8_t>& _packet) override;
  bool getTxChannelRegAddr(const int _channel_num, uint32_t& _tx_channel_reg_addr) override;

  bool packStartWriteTopFlash(const uint32_t _start_addr,
                              const uint32_t _len,
                              ExpectedResp& _expected_resp,
                              std::vector<uint8_t>& _packet) override;
  bool packWriteTopFlash(const uint32_t _pkt_count,
                         const std::vector<uint8_t>& _data,
                         ExpectedResp& _expected_resp,
                         std::vector<uint8_t>& _packet) override;
  bool packFinishWriteTopFlash(ExpectedResp& _expected_resp, std::vector<uint8_t>& _packet) override;

  bool isValidMsop(const char* _packet) override;

private:
};

}  // namespace lidar
}  // namespace robosense

#endif  // HELIOS_PARSER_H
