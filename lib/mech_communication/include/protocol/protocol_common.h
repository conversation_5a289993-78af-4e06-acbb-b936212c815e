﻿/******************************************************************************
 * Copyright 2023 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#ifndef PROTOCOL_COMMON_H
#define PROTOCOL_COMMON_H

#include "../data_struct/mech_data_struct.h"
#include <array>
#include <cstdint>
#include <string>

namespace robosense  //NOLINT(modernize-concat-nested-namespaces)
{
namespace lidar
{

namespace helios
{
constexpr uint32_t FRAME_FLAG             = 0x55AA2552;
constexpr uint32_t TRANS_TOP_REG          = 0x83c20030;
constexpr uint32_t TRANS_TOP_REG_VAL_BACK = 0x83c20034;
constexpr uint32_t MOTOR_SPEED_ADDR       = 0x83c00014;
constexpr uint32_t LINK_STATUS_ADDR       = 0x83c01050;

constexpr uint32_t NET_CMD_BEGIN               = 0x000;
constexpr uint32_t NET_CMD_READ_REGISTER       = 0x001;
constexpr uint32_t NET_CMD_WRITE_REGISTER      = 0x002;
constexpr uint32_t NET_CMD_TOP_BIN_UPDATE      = 0x003;
constexpr uint32_t NET_CMD_TOP_BACKUP_UPDATE   = 0x004;
constexpr uint32_t NET_CMD_BOT_BIN_UPDATE      = 0x005;
constexpr uint32_t NET_CMD_BOT_BACKUP_UPDATE   = 0x006;
constexpr uint32_t NET_CMD_LINUX_APP_UPDATE    = 0x007;
constexpr uint32_t NET_CMD_LAPP_BACKUP_UPDATE  = 0x008;
constexpr uint32_t NET_CMD_READ_CONFIG         = 0x009;
constexpr uint32_t NET_CMD_WRITE_CONFIG        = 0x00A;
constexpr uint32_t NET_CMD_TOP_PARA_UPDATE     = 0x00B;
constexpr uint32_t NET_CMD_BOT_PARA_UPDATE     = 0x00C;
constexpr uint32_t NET_CMD_TOP_FLASH_READ      = 0x00D;
constexpr uint32_t NET_CMD_TOP_FLASH_WRITE     = 0x00E;
constexpr uint32_t NET_CMD_TOP_READ_REGISTER   = 0x00F;
constexpr uint32_t NET_CMD_TOP_WRITE_REGISTER  = 0x010;
constexpr uint32_t NET_CMD_TOP_GET_INTENSITY   = 0x011;
constexpr uint32_t NET_CMD_ZERO_ANGLE_READ     = 0x012;
constexpr uint32_t NET_CMD_ZERO_ANGLE_WRITE    = 0x013;
constexpr uint32_t NET_CMD_CHANNEL_ANGLE_READ  = 0x014;
constexpr uint32_t NET_CMD_CHANNEL_ANGLE_WRITE = 0x015;
constexpr uint32_t NET_CMD_MOTOR_UPDATE        = 0x016;
constexpr uint32_t NET_CMD_MOTOR_SEND          = 0x017;
constexpr uint32_t NET_CMD_MOTOR_RECV          = 0x018;
constexpr uint32_t NET_CMD_CODE_WHEEL_CALI     = 0x019;
#ifdef _M_RUBY_4_0_
#  define NET_CMD_TOP_HIGH_LOW_INTENSITY 0x019
#endif
constexpr uint32_t NET_CMD_INSPECTION              = 0x020;
constexpr uint32_t NET_CMD_CLEAR_BOT_CONFIG        = 0x021;
constexpr uint32_t NET_CMD_EYES_SAFE               = 0x022;
constexpr uint32_t NET_CMD_ENCODER_CURING          = 0x023;
constexpr uint32_t NET_CMD_WAVE_SHOW               = 0x024;
constexpr uint32_t NET_CMD_CODE_WHEEL_SAVE         = 0x025;
constexpr uint32_t NET_CMD_GET_MONITOR_PARAM       = 0x026;
constexpr uint32_t NET_CMD_END                     = 0x050;
constexpr uint32_t NET_CMD_ACK_BEGIN               = 0x100;
constexpr uint32_t NET_CMD_ACK_READ_REGISTER       = 0x101;
constexpr uint32_t NET_CMD_ACK_WRITE_REGISTER      = 0x102;
constexpr uint32_t NET_CMD_ACK_TOP_BIN_UPDATE      = 0x103;
constexpr uint32_t NET_CMD_ACK_TOP_BACKUP_UPDATE   = 0x104;
constexpr uint32_t NET_CMD_ACK_BOT_BIN_UPDATE      = 0x105;
constexpr uint32_t NET_CMD_ACK_BOT_BACKUP_UPDATE   = 0x106;
constexpr uint32_t NET_CMD_ACK_LINUX_APP_UPDATE    = 0x107;
constexpr uint32_t NET_CMD_ACK_LAPP_BACKUP_UPDATE  = 0x108;
constexpr uint32_t NET_CMD_ACK_READ_CONFIG         = 0x109;
constexpr uint32_t NET_CMD_ACK_WRITE_CONFIG        = 0x10A;
constexpr uint32_t NET_CMD_ACK_TOP_PARA_UPDATE     = 0x10B;
constexpr uint32_t NET_CMD_ACK_BOT_PARA_UPDATE     = 0x10C;
constexpr uint32_t NET_CMD_ACK_TOP_FLASH_READ      = 0x10D;
constexpr uint32_t NET_CMD_ACK_TOP_FLASH_WRITE     = 0x10E;
constexpr uint32_t NET_CMD_ACK_TOP_READ_REGISTER   = 0x10F;
constexpr uint32_t NET_CMD_ACK_TOP_WRITE_REGISTER  = 0x110;
constexpr uint32_t NET_CMD_ACK_TOP_GET_INTENSITY   = 0x111;
constexpr uint32_t NET_CMD_ACK_ZERO_ANGLE_READ     = 0x112;
constexpr uint32_t NET_CMD_ACK_ZERO_ANGLE_WRITE    = 0x113;
constexpr uint32_t NET_CMD_ACK_CHANNEL_ANGLE_READ  = 0x114;
constexpr uint32_t NET_CMD_ACK_CHANNEL_ANGLE_WRITE = 0x115;
constexpr uint32_t NET_CMD_ACK_MOTOR_UPDATE        = 0x116;
constexpr uint32_t NET_CMD_ACK_MOTOR_SEND          = 0x117;
constexpr uint32_t NET_CMD_ACK_MOTOR_RECV          = 0x118;
constexpr uint32_t NET_CMD_ACK_WHEEL_CALI          = 0x119;
#ifdef _M_RUBY_4_0_
#  define NET_CMD_TOP_HIGH_LOW_INTENSITY 0x019
#endif
constexpr uint32_t NET_CMD_ACK_INSPECTION        = 0x120;
constexpr uint32_t NET_CMD_ACK_CLEAR_BOT_CONFIG  = 0x121;
constexpr uint32_t NET_CMD_ACK_EYES_SAFE         = 0x122;
constexpr uint32_t NET_CMD_ACK_ENCODER_CURING    = 0x123;
constexpr uint32_t NET_CMD_ACK_WAVE_SHOW         = 0x124;
constexpr uint32_t NET_CMD_ACK_CODE_WHEEL_SAVE   = 0x125;
constexpr uint32_t NET_CMD_ACK_GET_MONITOR_PARAM = 0x126;
constexpr uint32_t NET_CMD_ACK_END               = 0x150;

constexpr uint32_t GS_FRAME_FLAG = 0x55AA2552;

constexpr std::array<uint8_t, 4> MSOP_FRAME_FLAG = { 0x55, 0xaa, 0x05, 0x5a };

constexpr uint32_t CH_SEL = 0x2600;

constexpr uint32_t TX_EN_A1_A8  = 0x1103;
constexpr uint32_t TX_EN_A9_A16 = 0x1104;
constexpr uint32_t TX_EN_B1_B8  = 0x1105;
constexpr uint32_t TX_EN_B9_B16 = 0x1106;

constexpr uint32_t RX_EN_A1_A8  = 0x1113;
constexpr uint32_t RX_EN_A9_A16 = 0x1114;
constexpr uint32_t RX_EN_B1_B8  = 0x1115;
constexpr uint32_t RX_EN_B9_B16 = 0x1116;

constexpr uint32_t DATA_LOCK  = 0x2601;
constexpr uint32_t AREA_GDI_H = 0x2612;
constexpr uint32_t AREA_GDI_L = 0x2613;
constexpr uint32_t DIST_GDI_H = 0x2614;
constexpr uint32_t DIST_GDI_L = 0x2615;
constexpr uint32_t TMP_H      = 0x301f;
constexpr uint32_t TMP_L      = 0x3020;

constexpr uint32_t CHARGE_FIX_EN    = 0x1208;
constexpr uint32_t CHARGE_FIX_VALUE = 0x1209;

constexpr uint32_t PW_MID_H = 0x1219;
constexpr uint32_t PW_MID_L = 0x121a;

constexpr uint32_t VGA_FIX_ADDR_EN = 0x120a;
constexpr uint32_t VGA_FIX_ADDR    = 0x120b;

constexpr uint32_t GAIN_ADDR   = 0x1220;
constexpr uint32_t GAIN_ADDR_L = 0x1221;
constexpr uint32_t GAIN_ADDR_H = 0x1222;

#pragma pack(push, 1)
struct MsopPacket
{
  std::array<uint8_t, 4> frame_flag;
  uint16_t protocol_version;
  uint16_t reserve0;
  uint32_t top_download_count;
  uint32_t bot_upload_count;
  uint8_t reserve1;

  uint8_t accuracy;

  uint16_t angle_pulse;

  std::array<uint8_t, 10> timestamp;  // 20
  uint8_t reserve2;
  uint8_t lidar_type;
  uint8_t lidar_model;

  std::array<uint8_t, 9> reserve3;

  std::array<uint8_t, 1200> data;

  uint32_t reserve4;
  uint16_t frame_tail;
};
#pragma pack(pop)

using FrameHead = struct FrameHead
{
  uint32_t frame_flag;
  uint32_t length;
  uint32_t cmd;
  uint32_t check_sum;
};

inline uint16_t checkSum(FrameHead _frame_head)
{
  uint32_t sum = 0;

  sum += _frame_head.frame_flag & 0xFFFFU;
  sum += (_frame_head.frame_flag >> 16U) & 0xFFFFU;

  sum += _frame_head.length & 0xFFFFU;
  sum += (_frame_head.length >> 16U) & 0xFFFFU;

  sum += _frame_head.cmd & 0xFFFFU;
  sum += (_frame_head.cmd >> 16U) & 0xFFFFU;

  sum = (sum >> 16U) + (sum & 0xFFFFU);

  return static_cast<uint16_t>(~sum);
}

struct RegisterData
{
  uint32_t address;
  uint32_t value;
};

using LidarPara = struct LidarPara
{
  // sizeof(struct lidarPara) = 256 Bytes
  uint16_t motor_speed;       // res[0]
  uint16_t motor_phase_lock;  // res[1]
  uint16_t time_sync_mode;    // res[2]
  uint16_t start_fov;         // res[3]
  uint16_t end_fov;           // res[4]
  uint16_t wave_mode;         // res[5]

  uint32_t top_version;       // res[6, 7]
  uint32_t bot_version;       // res[8, 9]
  uint32_t app_version;       // res[10,11]
  uint32_t motor_version;     // res[12,13]
  uint32_t top_version_time;  // res[14,15]

  uint16_t temperature;                 // res[16]
  uint16_t motor_real_time_speed;       // res[17]
  uint16_t motor_real_time_phase_lock;  // res[18]
  uint16_t phase_lock_status;           // res[19]
  uint16_t disk_calib_status;           // res[20]
  uint16_t angle_pulse;                 // res[21]

  uint32_t web_version;  // res[22, 23]
};

using UnionParam = union UnionParam
{
  LidarPara lidar_para;
  std::array<uint16_t, 128> res;
};

using ConfigPara = struct ConfigPara
{
  std::array<uint8_t, 6> sn;
  std::array<uint8_t, 6> mac;

  std::array<uint8_t, 4> ip_local;
  std::array<uint8_t, 4> ip_remote;

  uint16_t msop_port;
  uint16_t difop_port;

  UnionParam param;

  std::array<uint8_t, 4> netmask_local;
  std::array<uint8_t, 4> gateway_local;
};

}  // namespace helios

namespace mech
{

constexpr uint32_t MAX_REG_ADDR     = 0x83D00000;
constexpr uint32_t MIN_REG_ADDR     = 0x83C00000;
constexpr uint32_t MIN_TOP_REG_ADDR = 0x1000;
constexpr uint32_t MAX_TOP_REG_ADDR = 0x4000;

// 参考mems通信库，声明状态码，命令类型
constexpr uint16_t FRAME_FLAG        = 0xffff;
constexpr uint8_t FRAME_TAIL_FLAG    = 0xfe;
constexpr uint8_t RUBY_NET_CMD_BEGIN = 0x00;
constexpr uint8_t FRAME_TYPE_REQUEST = 0x00;
constexpr uint8_t FRAME_TYPE_ACK     = 0x01;

constexpr uint16_t NET_CMD_BEGIN = 0x1000;

// 参数设置
constexpr uint16_t NET_CMD_CONFIG_SET_SN               = 0x1001;
constexpr uint16_t NET_CMD_CONFIG_SET_NETWORK          = 0x1002;
constexpr uint16_t NET_CMD_CONFIG_SET_TIME_SYNC_MODE   = 0x1003;
constexpr uint16_t NET_CMD_CONFIG_SET_ECHO_WAVE_MODE   = 0x1004;
constexpr uint16_t NET_CMD_CONFIG_SET_FOV_ANGLE        = 0x1005;
constexpr uint16_t NET_CMD_CONFIG_SET_PHASE_LOCK       = 0x1006;
constexpr uint16_t NET_CMD_CONFIG_SET_MOTOR_SPEED      = 0x1007;
constexpr uint16_t NET_CMD_CONFIG_SET_ANGLE_TRIGGER    = 0x1008;
constexpr uint16_t NET_CMD_CONFIG_SET_LIDAR_MODE       = 0x1009;
constexpr uint16_t NET_CMD_CONFIG_SET_RAIN_FOG_REMOVAL = 0x100a;
constexpr uint16_t NET_CMD_CONFIG_SET_ZERO_ANGLE       = 0x100b;
constexpr uint16_t NET_CMD_CONFIG_SET_MULTI_PARAM      = 0x10ff;

// 参数获取
constexpr uint16_t NET_CMD_CONFIG_READ_ALL = 0x2001;
constexpr uint16_t NET_CMD_PS_DEFINED      = 0x2002;

// 升级
constexpr uint16_t NET_CMD_FIRMWARE_UPDATE_ACK = 0x3001;
// 固件升级查询
constexpr uint16_t NET_CMD_FIRMWARE_UPDATE_CHECK = 0x3002;
// 补丁包升级
constexpr uint16_t NET_CMD_FIRMWARE_UPDATE_PATCH = 0x3003;
// 补充升级config
constexpr uint16_t NET_CMD_FIRMWARE_UPDATE_CONFIG = 0x3004;
// app升级
constexpr uint16_t NET_CMD_FIRMWARE_UPDATE_APP = 0x3005;
// sbl升级
constexpr uint16_t NET_CMD_FIRMWARE_UPDATE_SBL = 0x3006;
// bot升级
constexpr uint16_t NET_CMD_FIRMWARE_UPDATE_BOT = 0x3007;
// top升级
constexpr uint16_t NET_CMD_FIRMWARE_UPDATE_TOP = 0x3008;

// 电机控制
constexpr uint16_t NET_CMD_MOTOR_SEND_CMD    = 0x4001;
constexpr uint16_t NET_CMD_MOTOR_CALIBRATION = 0x4002;

// 顶板
constexpr uint16_t NET_CMD_TOP_BOARD_CON_READ_REGISTER    = 0x5001;
constexpr uint16_t NET_CMD_TOP_BOARD_CON_WRITE_REGISTER   = 0x5002;
constexpr uint16_t NET_CMD_TOP_BOARD_WRITE_FLASH          = 0x5003;
constexpr uint16_t NET_CMD_TOP_BOARD_READ_FLASH           = 0x5004;
constexpr uint16_t NET_CMD_TOP_BOARD_PARAM_UPDATE         = 0x5005;
constexpr uint16_t NET_CMD_TOP_BOARD_EYES_SAFE            = 0x5006;
constexpr uint16_t NET_CMD_TOP_BOARD_MULTI_WRITE_REGISTER = 0x5007;
constexpr uint16_t NET_CMD_TOP_BOARD_MULTI_READ_REGISTER  = 0x5008;
constexpr uint16_t NET_CMD_TOP_BOARD_GET_INTENSITY        = 0x5009;

// 底板
constexpr uint16_t NET_CMD_BOTTOM_BOARD_CON_READ_REGISTER    = 0x6001;  // 0x6001 读底板寄存器
constexpr uint16_t NET_CMD_BOTTOM_BOARD_CON_WRITE_REGISTER   = 0x6002;
constexpr uint16_t NET_CMD_BOTTOM_BOARD_PARAM_UPDATE         = 0x6003;
constexpr uint16_t NET_CMD_BOTTOM_BOARD_MULTI_WRITE_REGISTER = 0x6004;
constexpr uint16_t NET_CMD_BOTTOM_BOARD_MULTI_READ_REGISTER  = 0x6005;
constexpr uint16_t NET_CMD_BOTTOM_BOARD_WRITE_ZERO_ANGLE     = 0x6006;  // 写入零度角
constexpr uint16_t NET_CMD_BOTTOM_BOARD_READ_ZERO_ANGLE      = 0x6007;  // 读取零度角

// 其他
constexpr uint16_t NET_CMD_OTHER_CHANNEL_ANGLE_WRITE = 0xe001;
constexpr uint16_t NET_CMD_OTHER_CHANNEL_ANGLE_READ  = 0xe002;
constexpr uint16_t NET_CMD_OTHER_MONITOR_DATA        = 0xe003;
constexpr uint16_t NET_CMD_OTHER_FIRM_459_WRITE      = 0xe013;
constexpr uint16_t NET_CMD_OTHER_FIRM_459_READ       = 0xe014;
constexpr uint16_t NET_CMD_OTHER_FIRM_TOP_WRITE      = 0xe015;
constexpr uint16_t NET_CMD_OTHER_FIRM_BOT_WRITE      = 0xe016;
constexpr uint16_t NET_CMD_OTHER_ENCOD_CALIB_READ    = 0xe018;
constexpr uint16_t NET_CMD_OTHER_FIRM_TOP_READ       = 0xe019;
constexpr uint16_t NET_CMD_OTHER_FIRM_BOT_READ       = 0xe01a;

constexpr uint16_t NET_CMD_END = 0xf000;

constexpr uint8_t LIDAR_TYPE_RUBY   = 0x05;
constexpr uint8_t LIDAR_TYPE_BPEARL = 0x03;

constexpr uint8_t FLASH_CMD_REQ_WRITE    = 0xC1;
constexpr uint8_t FLASH_CMD_WRITE        = 0xC2;
constexpr uint8_t FLASH_CMD_WRITE_FINISH = 0xC3;

#pragma pack(push, 1)
struct FrameHead
{
  uint16_t frame_header;
  uint8_t frame_type;
  uint16_t length;
  uint16_t cmd_type;
  uint8_t response_type;
};

namespace server
{
struct FrameHead
{
  uint16_t frame_header;
  uint8_t frame_type;
  uint16_t length;
  uint16_t cmd_type;
};
}  // namespace server
struct NetPara
{
  std::array<uint8_t, 6> mac;

  std::array<uint8_t, 4> ip_local;
  std::array<uint8_t, 4> netmask_local;
  std::array<uint8_t, 4> gateway_local;

  uint16_t msop_port;
  uint16_t difop_port;

  std::array<uint8_t, 4> ip_remote;

  [[nodiscard]] std::string getIpLocal() const;
  [[nodiscard]] uint16_t getMsopPort() const;
  [[nodiscard]] uint16_t getDifopPort() const;
  [[nodiscard]] std::string getIpRemote() const;
  [[nodiscard]] std::string getMacAddr() const;
  [[nodiscard]] std::string getNetmaskLocal() const;
  [[nodiscard]] std::string getGatewayLocal() const;

  void setIpLocal(const std::string& _ip);
  void setMsopPort(const uint16_t _port);
  void setDifopPort(const uint16_t _port);
  void setIpRemote(const std::string& _ip);
  void setMacAddr(std::string _mac);
  void setNetmaskLocal(const std::string& _netmask);
  void setGatewayLocal(const std::string& _gateway);
};

struct ConfigPara
{
  std::array<uint8_t, 6> sn;
  NetPara net_info;

  TimeSyncMode time_sync_mode;
  uint8_t time_sync_status;
  EchoMode echo_wave_mode;

  std::array<uint8_t, 4> pl_version;
  std::array<uint8_t, 4> ps_version;
  std::array<uint8_t, 4> software_version;
  std::array<uint8_t, 4> web_version;
  std::array<uint8_t, 4> motor_version;

  float angle0;

  uint16_t start_fov;
  uint16_t end_fov;

  uint8_t motor_speed;
  uint16_t motor_phase;

  uint8_t status_of_code_wheel_cali;
  uint16_t status_of_phase_lock;

  uint16_t motor_real_time_speed;
  uint16_t motor_real_time_phase;

  [[nodiscard]] std::string getSn() const;
  [[nodiscard]] uint32_t getPsVersion() const;
  [[nodiscard]] uint32_t getPlVersion() const;
  [[nodiscard]] uint32_t getSoftwareVersion() const;
  [[nodiscard]] uint32_t getWebVersion() const;
  [[nodiscard]] uint32_t getMotorVersion() const;
  [[nodiscard]] std::string getIpLocal() const;
  [[nodiscard]] uint16_t getMsopPort() const;
  [[nodiscard]] uint16_t getDifopPort() const;
  [[nodiscard]] std::string getIpRemote() const;
  [[nodiscard]] std::string getMacAddr() const;
  [[nodiscard]] std::string getNetmaskLocal() const;
  [[nodiscard]] std::string getGatewayLocal() const;
  [[nodiscard]] uint16_t getMotorRealTimeSpeed() const;
};

struct MsopPacket
{
  std::array<uint8_t, 8> frame_flag;
  uint32_t top_dispatch_count;
  uint32_t bot_upload_count;
  uint32_t reserve0;
  std::array<uint8_t, 10> timestamp;
  uint8_t reserve1;
  uint8_t lidar_type;
  std::array<uint8_t, 5> reserve2;
  uint16_t temp;
  uint16_t top_temp;

  std::array<uint8_t, 1200> data;

  uint32_t reserve3;
  uint16_t frame_tail;
};
#pragma pack(pop)

#pragma pack(1)
struct GainData
{
  uint32_t addr;
  uint32_t value;

  uint32_t addr_l;
  uint32_t value_l;

  uint32_t addr_h;
  uint32_t value_h;
};

struct LockData
{
  uint32_t addr_0;
  uint32_t value_0;
  uint32_t addr_1;
  uint32_t value_1;
};

struct AreaDistData
{
  LockData lock_data;
  uint32_t area_addr_h;
  uint32_t area_value_h;
  uint32_t area_addr_l;
  uint32_t area_value_l;

  uint32_t dist_addr_h;
  uint32_t dist_value_h;
  uint32_t dist_addr_l;
  uint32_t dist_value_l;
};

struct GainIntensityData
{
  GainData gain_data;
  std::array<AreaDistData, 2> area_dist_data;
};

struct IntensityData
{
  std::array<GainIntensityData, 16> gain_intensity_data;
};
struct GainDistAreaData
{
  uint16_t gain;
  uint16_t distance;
  uint16_t area;
};
#pragma pack()

inline uint16_t checkSum(const uint8_t* _data, uint16_t _length)
{
  uint32_t check_sum           = 0;
  const uint8_t* unsigned_data = static_cast<const uint8_t*>(static_cast<const void*>(_data));
  while (_length != 0)
  {
    check_sum += unsigned_data[--_length];  // NOLINT
  }

  check_sum = check_sum & 0xffffU;

  return static_cast<uint16_t>(check_sum);
}

}  // namespace mech

namespace airy
{
constexpr std::array<uint8_t, 4> MSOP_FRAME_FLAG = { 0x55, 0xaa, 0x05, 0x5a };
}  // namespace airy

namespace bpearl
{
constexpr uint32_t LINK_STATUS_ADDR              = 0x83c01050;
constexpr std::array<uint8_t, 8> MSOP_FRAME_FLAG = { 0x55, 0xaa, 0x05, 0x0a, 0x5a, 0xa5, 0x50, 0xa0 };
using ConfigPara                                 = mech::ConfigPara;
}  // namespace bpearl

namespace ruby
{
constexpr uint32_t LINK_STATUS_ADDR              = 0x83c00414;
constexpr std::array<uint8_t, 4> MSOP_FRAME_FLAG = { 0x55, 0xaa, 0x05, 0x5a };
using ConfigPara                                 = mech::ConfigPara;
struct MonitorData
{
  uint32_t top_2v5;
  uint32_t top_vbus;
  uint32_t top_tx5;
  uint32_t top_a5;
  uint32_t top_hv;
  uint32_t top_n5v;
  uint32_t machine_vbus;
  uint32_t bot_5v;
  uint32_t bot_28v;
  uint32_t top_5v5_shunt_v;
  uint32_t top_5v5_bus_v;
  uint32_t top_5v5_power;
  uint32_t bot_24v_shunt_v;
  uint32_t bot_24v_bus_v;
  uint32_t bot_24v_power;
  uint32_t top_5v5_current;
  uint32_t machine_current;
  uint32_t bot_current;
  uint32_t bot_28v_current;
  float apd_temp;
  float top_under_temp;
  float top_above_temp;
  float bot_motor_temp;
  float bot_magnetic_rings_temp;
  float top_fpga_rx_temp;
  float chip_on_temp;
};
}  // namespace ruby

}  // namespace lidar
}  // namespace robosense

#endif  // PROTOCOL_COMMON_H