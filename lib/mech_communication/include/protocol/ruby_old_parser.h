﻿/******************************************************************************
 * Copyright 2023 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#ifndef RUBY_PARSER_H
#define RUBY_PARSER_H

#include "protocol/mech_base_parser.h"

namespace robosense  //NOLINT(modernize-concat-nested-namespaces)
{
namespace lidar
{
class RubyOldParser : public MechBaseParser
{
public:
  explicit RubyOldParser()                     = default;
  explicit RubyOldParser(RubyOldParser&&)      = delete;
  explicit RubyOldParser(const RubyOldParser&) = delete;
  RubyOldParser& operator=(RubyOldParser&&) = delete;
  RubyOldParser& operator=(const RubyOldParser&) = delete;
  ~RubyOldParser() override                      = default;

  std::string getCurrentProtocolType() override;

  bool packStartupCheck(uint32_t& _expected_packet_response_code, std::vector<uint8_t>& _packet) override;

  bool isLidarStartUp(const uint32_t _value) override;

  bool isValidMsop(const char* _packet) override;

  bool extractRegister(const std::vector<uint8_t>& _payload) override;

  bool extractMultiRegister(const std::vector<uint8_t>& _payload) override;
  bool extractConRegister(const std::vector<uint8_t>& _payload) override;

private:
};

}  // namespace lidar
}  // namespace robosense

#endif  //RUBY_PARSER_H
