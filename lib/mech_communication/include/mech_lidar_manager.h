﻿/******************************************************************************
 * Copyright 2023 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#ifndef MECH_LIDAR_MANAGER_H
#define MECH_LIDAR_MANAGER_H

#include "mech_tcp.h"
#include "protocol/protocol_parser.h"
#include <cstdint>
#include <string>
#include <vector>

// NOLINTNEXTLINE(modernize-concat-nested-namespaces)
namespace robosense
{
namespace lidar
{
class MechLidarManager : public MechTcp
{
public:
  explicit MechLidarManager(ProtocolType _protocol_type = ProtocolType::MECH_AIRY, const int _index = -1);
  MechLidarManager(const MechLidarManager&) = delete;
  MechLidarManager(MechLidarManager&&)      = delete;
  MechLidarManager& operator=(const MechLidarManager&) = delete;
  MechLidarManager& operator=(MechLidarManager&&) = delete;
  ~MechLidarManager() override;

  bool writeTopPairRegDataByName(const std::string& _reg_name, const uint16_t _value);
  bool readTopPairRegDataByName(const std::string& _reg_name, uint16_t& _value);

  // bool writeRegData(const uint32_t _reg_addr, const uint32_t _reg_val, const uint32_t _msec = DEFAULT_TIMEOUT);
  // bool writeRegData(const std::string& _reg_name, const uint32_t _value);
  // bool writeRegData(const std::string& _reg_name, const std::vector<uint32_t>& _values);

  // bool readRegData(const uint32_t _reg_addr, uint32_t& _reg_val, const uint32_t _msec = DEFAULT_TIMEOUT);
  // bool readRegData(const std::string& _reg_name, uint32_t& _value);

  /**
   * @brief     读取寄存器数据并通过公式计算出结果
   * 
   * @param     _reg_name          寄存器名称
   * @param     _result            输出的结果
   * @return    true               
   * @return    false              
  **/
  bool readRegDataEval(const std::string& _reg_name, double& _result);

  void setRegAddr(const std::string& _reg_name, const ProtoParser::RegisterInfo& _register_info);

  /**
   * @brief     打开/关闭一个发射通道，只会影响一个通道，不会影响其他通道
   * 
   * @param     _channel_num       通道号
   * @return    true               成功
   * @return    false              失败
  **/
  bool txChannelCtrl(const int _channel_num, const bool _open);

  /**
   * @brief     打开一个发射通道，关闭其他所有通道
   * 
   * @param     _channel_num       通道号
   * @return    true               成功
   * @return    false              失败
  **/
  bool txChannelOpenExclusively(const int _channel_num);

  /**
   * @brief     打开所有发射通道
   * 
   * @return    true          成功
   * @return    false         失败
  **/
  bool txChannelCtrlAll(const bool _open);

  /**
   * @brief     设置发射充能时间
   * 
   * @param     _channel_num       对应通道号
   * @param     _charge            充能时间
   * @return    true               成功
   * @return    false              失败
  **/
  bool setTxChargeTime(const uint32_t _charge);

  /**
   * @brief     获取apd负高压
   * 
   * @param     _value             
   * @return    true               
   * @return    false              
  **/
  bool getApdNHVol(float& _value);

  /**
   * @brief     设置负高压
   * 
   * @param     _value             
   * @return    true               
   * @return    false              
  **/
  bool setPwmNhvValue(uint32_t _value);

private:
  bool txChannelControl(const std::vector<uint8_t>& _data);
};

}  // namespace lidar
}  // namespace robosense

#endif  // MECH_LIDAR_MANAGER_H
