﻿/******************************************************************************
 * Copyright 2023 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#ifndef HELIOS_DATA_STRUCT_H
#define HELIOS_DATA_STRUCT_H

#include <array>
#include <cstdint>

// NOLINTNEXTLINE(modernize-concat-nested-namespaces)
namespace robosense
{
namespace lidar
{

namespace helios
{
#pragma pack(push, 1)
using Tail = struct Tail
{
  std::array<uint8_t, 4> reserved_data1;
  std::array<uint8_t, 2> line_end;
};

struct BigEndianU16
{
  uint8_t high;
  uint8_t low;
  [[nodiscard]] uint16_t toHost() const { return (high << 8U) + low; }
};

struct ADCPoint
{
  std::array<uint8_t, 3> point_array;
};

constexpr std::array<uint8_t, 2> BLOCK_HEADER_FLAG = { 0xff, 0xee };
constexpr std::array<uint8_t, 2> LINE_END_FLAG     = { 0x00, 0xff };
constexpr std::array<uint8_t, 8> MSOP_HEADER_FLAG  = { 0x55, 0xaa, 0x05, 0x5a, 0x00, 0x01, 0x00, 0x00 };

using DataBlock = struct DataBlock
{
  std::array<uint8_t, 2> block_header1;
  BigEndianU16 azimuth1;
  BigEndianU16 fixed_data1;
  uint8_t fixed_data2;
  BigEndianU16 area;
  uint8_t max_amp;
  uint8_t sample_gap;
  BigEndianU16 fixed_data3;
  std::array<ADCPoint, 29> adc_point_array1;
  std::array<uint8_t, 2> block_header2;
  BigEndianU16 azimuth2;
  BigEndianU16 distance;
  uint8_t channel;
  BigEndianU16 gain;
  uint8_t reflect;
  std::array<ADCPoint, 30> adc_point_array2;
};

using DataBlock2 = struct DataBlock2
{
  std::array<uint8_t, 2> block_header2;
  BigEndianU16 azimuth2;
  BigEndianU16 distance;
  uint8_t channel;
  BigEndianU16 gain;
  uint8_t reflect;
  std::array<ADCPoint, 30> adc_point_array2;
  std::array<uint8_t, 2> block_header1;
  BigEndianU16 azimuth1;
  BigEndianU16 fixed_data1;
  uint8_t fixed_data2;
  BigEndianU16 area;
  uint8_t max_amp;
  uint8_t sample_gap;
  BigEndianU16 fixed_data3;
  std::array<ADCPoint, 29> adc_point_array1;
};

using DataPacket = struct DataPacket
{
  std::array<DataBlock, 6> data_blocks;
};

using DataPacket2 = struct DataPacket2
{
  std::array<DataBlock2, 6> data_blocks;
};

using Header = struct Header
{
  std::array<uint8_t, 8> base_header;
  std::array<uint8_t, 4> top_down_package;
  uint32_t bot_upload_package;
  std::array<uint8_t, 4> reserved_data1;
  std::array<uint8_t, 6> timestamp_s;
  uint32_t timestamp_ns;
  std::array<uint8_t, 12> reserved_data2;
};

using MsopDataPacket = struct MsopDataPacket
{
  Header pkt_head;
  DataPacket data_packet;
  Tail tail;
};

using MsopDataPacket2 = struct MsopDataPacket2
{
  Header pkt_head;
  DataPacket2 data_packet;
  Tail tail;
};
#pragma pack(pop)

}  // namespace helios

}  // namespace lidar
}  // namespace robosense
#endif  // HELIOS_DATA_STRUCT_H