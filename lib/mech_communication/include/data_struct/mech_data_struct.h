﻿/******************************************************************************
 * Copyright 2024 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai
 *
 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.
 *
 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONS<PERSON>QUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#ifndef MECH_DATA_STRUCT_H
#define MECH_DATA_STRUCT_H
#include <array>
#include <cstdint>

// NOLINTNEXTLINE(modernize-concat-nested-namespaces)
namespace robosense
{
namespace lidar
{

namespace mech
{

#ifndef BSWAP16
#  if defined(_MSC_VER)  // MSVC
#    include <intrin.h>
#    define BSWAP16 _byteswap_ushort
#    define BSWAP32 _byteswap_ulong
#    define BSWAP64 _byteswap_uint64
#  elif defined(__GNUC__) || defined(__clang__)  // GCC or Clang
#    define BSWAP16 __builtin_bswap16
#    define BSWAP32 __builtin_bswap32
#    define BSWAP64 __builtin_bswap64
#  else
#    error "Unsupported compiler"
#  endif
#endif

#pragma pack(push, 1)
enum class GpsBaudRate : uint8_t
{
  GPS_BAUD_1200    = 0x00,
  GPS_BAUD_2400    = 0x01,
  GPS_BAUD_4800    = 0x02,
  GPS_BAUD_9600    = 0x03,
  GPS_BAUD_14400   = 0x04,
  GPS_BAUD_19200   = 0x05,
  GPS_BAUD_38400   = 0x06,
  GPS_BAUD_43000   = 0x07,
  GPS_BAUD_57600   = 0x08,
  GPS_BAUD_76800   = 0x09,
  GPS_BAUD_115200  = 0x0A,
  GPS_BAUD_128000  = 0x0B,
  GPS_BAUD_230400  = 0x0C,
  GPS_BAUD_256000  = 0x0D,
  GPS_BAUD_460800  = 0x0E,
  GPS_BAUD_921600  = 0x0F,
  GPS_BAUD_1382400 = 0x10
};
enum class EchoMode : uint8_t
{
  STRONGEST = 0x01,  // 最强回波
  FIRST     = 0x03,  // 最近回波
  LAST      = 0x02,  // 最后回波
  DUAL_ECHO = 0x00,  // 双回波
};
enum class TimeSyncMode : uint8_t
{
  GPS    = 0x00,
  E2E    = 0x01,
  P2P    = 0x02,
  GPTP   = 0x03,
  E2E_L2 = 0x04
};
enum class MountType : uint8_t
{
  FRONT,    // 正装
  SIDE,     // 侧装
  MAPPING,  // 测绘
  MOW,      // 割草
  UNKNOWN
};
struct GpsStatus
{
  uint8_t pps_lock : 1;
  uint8_t gprmc_lock : 1;
  uint8_t utc_lock : 1;
  uint8_t gprmc_input : 1;
  uint8_t pps_input : 1;
  uint8_t reserve_gps : 3;
};

constexpr uint64_t DIFOP_PKT_HEAD = 0xA5FF005A11115555;
constexpr uint16_t DIFOP_PKT_TAIL = 0x0ff0;
struct DifopPacket
{
  uint64_t pkt_head;                // 0x55aa055a
  uint16_t motor_set_speed;         // 电机设置转速 300/600/1200
  uint32_t ip_src;                  // 以太网IP源地址
  uint32_t ip_dst;                  // 以太网IP目标地址
  std::array<uint8_t, 6> mac_addr;  // 雷达MAC地址
  uint16_t msop_port;               // MSOP端口
  uint16_t reserve;
  uint16_t difop_port;  // DIFOP端口
  uint16_t reserve1;
  uint16_t fov_start;  // FOV起始角度, 0-359, 精度0.01°
  uint16_t fov_end;    // FOV结束角度, 0-359, 精度0.01°
  uint16_t reserve2;
  uint16_t lock_phase;                // 锁相相位, 0-360, 精度1°
  uint8_t top_firmware_reserve;       // 主板固件版本
  uint32_t top_firmware_version;      // 主板固件版本
  uint8_t bot_firmware_reserve;       // 底板固件版本
  uint32_t bot_firmware_version;      // 底板固件版本
  uint8_t app_firmware_reserve;       // APP固件版本
  uint32_t app_firmware_version;      // APP固件版本
  uint8_t motor_firmware_reserve;     // 电机固件版本
  uint32_t motor_firmware_version;    // 电机固件版本
  uint8_t cgi_firmware_reserve;       // cgi固件版本
  uint32_t cgi_firmware_version;      // cgi固件版本
  std::array<uint8_t, 223> reserve3;  // 预留
  GpsBaudRate gps_baud_rate;          // GPS同步模式下的GPRMC波特率
  MountType mount_type;               // 安装方式, 0:正装, 1:侧装, 2:测绘
  std::array<uint8_t, 2> reserve4;
  std::array<uint8_t, 6> sn;    // 产品序列号
  uint16_t zero_angle;          // 零度角标定值, 0-359.99, 单位0.01度
  EchoMode echo_mode;           // 回波模式
  TimeSyncMode time_sync_mode;  // 时间同步方式设置
  uint8_t time_sync_status;     // 时间同步状态, 0x00:未同步, 0x01:同步成功
  // std::array<uint8_t, 10> time;  // 时间, UTC时间格式, 前6个byte为秒时间戳, 后4个byte为微秒时间戳
  std::array<uint8_t, 6> time_sec;  // 时间, UTC时间格式, 前6个byte为秒时间戳, 后4个byte为微秒时间戳
  uint32_t time_nano;  // 时间, UTC时间格式, 前6个byte为秒时间戳, 后4个byte为微秒时间戳
  uint8_t reserve5;
  std::array<uint8_t, 19> reserve6;
  std::array<uint8_t, 4> reserve7;
  uint8_t motor_dir;        // 电机正转反转标志, 0x00:正转, 0x01:反转
  uint32_t total_run_time;  // 设备运行总时间, 单位为分钟, 溢出后重新统计
  std::array<uint8_t, 9> reserve8;
  uint16_t reboot_count;  // 设备启动次数, 1-65535循环计数
  std::array<uint8_t, 4> reserve9;
  GpsStatus gps_status;  // GPS状态
  std::array<uint8_t, 8> reserve10;
  std::array<uint8_t, 5> reserve11;
  uint16_t realtime_phase;  // 实时相位, 单位度
  uint16_t realtime_speed;  // 电机实时转速, 单位RPM
  uint32_t start_time;      // 电机启动时间, 单位ms
  std::array<uint8_t, 3> reserve12;
  std::array<uint8_t, 86> gprmc;              // GPRMC
  std::array<uint8_t, 288> vertical_calib;    // 垂直角校准
  std::array<uint8_t, 288> horizontal_calib;  // 水平角校准
  uint16_t top_input_vol;                     // 主板总输入电压, DIFOP: Data/100, 阈值范围: 9~32V
  uint16_t top_3v8;                           // 主板3.8v电压, DIFOP: Data/100, 阈值范围: 3.5~4.1V
  uint16_t top_3v3;                           // 主板3.3v电压, DIFOP: Data/100, 阈值范围: 3~3.6V
  uint16_t reserve13;
  uint16_t reserve14;
  uint16_t top_1v1;  // 主板接收1.1V电压, DIFOP: Data/100, 阈值范围: 1.0~1.2V
  uint16_t reserve15;
  int16_t top_neg_vol;      // 主板接收负压, DIFOP: Data/100, 阈值范围: -25V~-11V
  uint16_t top_3v3_rx;      // 主板接收3.3V电压, DIFOP: Data/100, 阈值范围: 3.2V~3.8V
  uint16_t top_charge_vol;  // 主板发射充能电压, DIFOP: Data/100, 阈值范围: 15V~35V
  uint16_t reserve16;
  uint16_t total_input_vol;  // 整机输入电压, DIFOP: Data/100, 阈值范围: 9V~32V
  uint16_t bot_12v;          // 底板12V电压, DIFOP: Data/100, 阈值范围: 11V~13V
  uint16_t bot_mcu_0v85;     // 底板MCU0.85V电压, DIFOP: Data/100, 阈值范围: 0.8V~0.9V
  uint16_t bot_fpga_1v;      // 底板FPGA内核1V, DIFOP: Data/100, 阈值范围: 0.9V~1.1V
  uint16_t total_input_cur;  // 整机输入电流, DIFOP: Data/100, 整机输入电压*总输入电流
  int16_t top_fpga_temp;     // 主板fpga内核温度, DIFOP: Data/100, 阈值范围: -40度~120度
  uint16_t reserve17;
  int16_t top_tx_temp;        // 主板发射温度, DIFOP: Data/100, 阈值范围: -40度~120度
  int16_t top_rx_459_temp_n;  // 主板RX-459温度N端, DIFOP: Data/100, 阈值范围: -40度~120度
  int16_t top_rx_459_temp_p;  // 主板RX-459温度P端, DIFOP: Data/100, 阈值范围: -40度~120度
  int16_t bot_imu_temp;       // 底板IMU温度, DIFOP: Data/100, 阈值范围: -40度~120度
  int16_t bot_fpga_temp;      // 底板fpga内核温度, DIFOP: Data/100, 阈值范围: -40度~120度
  uint16_t total_power;       // 整机功率, DIFOP: Data/100, 整机输入电压*总输入电流
  float q_x;                  // imu标定数据
  float q_y;                  // imu标定数据
  float q_z;                  // imu标定数据
  float q_w;                  // imu标定数据
  float x;                    // imu标定数据
  float y;                    // imu标定数据
  float z;                    // imu标定数据
  std::array<uint8_t, 126> reserve18;
  uint16_t tail;  // 帧尾, 0x0F 0xF0

  void toBigEndian()
  {
    pkt_head               = BSWAP64(pkt_head);
    motor_set_speed        = BSWAP16(motor_set_speed);
    ip_src                 = BSWAP32(ip_src);
    ip_dst                 = BSWAP32(ip_dst);
    msop_port              = BSWAP16(msop_port);
    difop_port             = BSWAP16(difop_port);
    fov_start              = BSWAP16(fov_start);
    fov_end                = BSWAP16(fov_end);
    lock_phase             = BSWAP16(lock_phase);
    top_firmware_version   = BSWAP32(top_firmware_version);
    bot_firmware_version   = BSWAP32(bot_firmware_version);
    app_firmware_version   = BSWAP32(app_firmware_version);
    motor_firmware_version = BSWAP32(motor_firmware_version);
    cgi_firmware_version   = BSWAP32(cgi_firmware_version);
    zero_angle             = BSWAP16(zero_angle);
    for (size_t i = 0; i < time_sec.size() / 2; ++i)
    {
      uint8_t tmp                          = time_sec.at(i);
      time_sec.at(i)                       = time_sec.at(time_sec.size() - 1 - i);
      time_sec.at(time_sec.size() - 1 - i) = tmp;
    }
    time_nano         = BSWAP32(time_nano);
    total_run_time    = BSWAP32(total_run_time);
    reboot_count      = BSWAP16(reboot_count);
    realtime_phase    = BSWAP16(realtime_phase);
    realtime_speed    = BSWAP16(realtime_speed);
    top_input_vol     = BSWAP16(top_input_vol);
    top_3v8           = BSWAP16(top_3v8);
    top_3v3           = BSWAP16(top_3v3);
    top_1v1           = BSWAP16(top_1v1);
    top_neg_vol       = static_cast<int16_t>(BSWAP16(static_cast<uint16_t>(top_neg_vol)));
    top_3v3_rx        = BSWAP16(top_3v3_rx);
    top_charge_vol    = BSWAP16(top_charge_vol);
    total_input_vol   = BSWAP16(total_input_vol);
    bot_12v           = BSWAP16(bot_12v);
    bot_mcu_0v85      = BSWAP16(bot_mcu_0v85);
    bot_fpga_1v       = BSWAP16(bot_fpga_1v);
    total_input_cur   = BSWAP16(total_input_cur);
    top_fpga_temp     = static_cast<int16_t>(BSWAP16(static_cast<uint16_t>(top_fpga_temp)));
    top_tx_temp       = static_cast<int16_t>(BSWAP16(static_cast<uint16_t>(top_tx_temp)));
    top_rx_459_temp_n = static_cast<int16_t>(BSWAP16(static_cast<uint16_t>(top_rx_459_temp_n)));
    top_rx_459_temp_p = static_cast<int16_t>(BSWAP16(static_cast<uint16_t>(top_rx_459_temp_p)));
    bot_imu_temp      = static_cast<int16_t>(BSWAP16(static_cast<uint16_t>(bot_imu_temp)));
    bot_fpga_temp     = static_cast<int16_t>(BSWAP16(static_cast<uint16_t>(bot_fpga_temp)));
    total_power       = BSWAP16(total_power);
    tail              = BSWAP16(tail);
  }

  [[nodiscard]] bool isValid()
  {
    if (pkt_head == DIFOP_PKT_HEAD && tail == DIFOP_PKT_TAIL)
    {
      return true;
    }
    if (BSWAP64(pkt_head) == DIFOP_PKT_HEAD && BSWAP16(tail) == DIFOP_PKT_TAIL)
    {
      toBigEndian();
      return true;
    }
    return false;
  }

  // NOLINTNEXTLINE(cppcoreguidelines-pro-type-reinterpret-cast)
  [[nodiscard]] const char* data() const { return reinterpret_cast<const char*>(this); }
};

#pragma pack(pop)
}  // namespace mech
}  // namespace lidar
}  // namespace robosense
#endif  // MECH_DATA_STRUCT_H