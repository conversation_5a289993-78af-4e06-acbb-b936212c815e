﻿/******************************************************************************
 * Copyright 2023 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
/**
 * Definitions of Register.
 *
 * @defgroup MOD_register
 * 
 * Copyright ((C) 2014 - 2020 RoboSense, Co., Ltd.  All rights reserved.
 *
 * <AUTHOR> Liu
 *
 * @attention 20210806 Create register definitions file.
 *
 */

#ifndef HELIOS_REGISTER_ADDRESS_H
#define HELIOS_REGISTER_ADDRESS_H

#include <cstdint>

namespace robosense  //NOLINT(modernize-concat-nested-namespaces)
{
namespace lidar
{
namespace helios
{
constexpr uint32_t TOP_REG_TX_EN_1_8         = 0x1103;
constexpr uint32_t TOP_REG_TX_EN_9_16        = 0x1104;
constexpr uint32_t TOP_REG_TX_EN_17_24       = 0x1105;
constexpr uint32_t TOP_REG_TX_EN_25_32       = 0x1106;
constexpr uint32_t TOP_REG_RX_EN_1_8         = 0x1113;
constexpr uint32_t TOP_REG_RX_EN_9_16        = 0x1114;
constexpr uint32_t TOP_REG_RX_EN_17_24       = 0x1115;
constexpr uint32_t TOP_REG_RX_EN_25_32       = 0x1116;
constexpr uint32_t TOP_REG_TX_DUAL_EN        = 0x1100;
constexpr uint32_t TOP_REG_CHARGE_ADDR_H     = 0x1220;
constexpr uint32_t TOP_REG_CHARGE_ADDR_MIN_L = 0x1221;
constexpr uint32_t TOP_REG_CHARGE_ADDR_MAX_L = 0x1222;
constexpr uint32_t TOP_REG_TX_CODE_FIX_EN    = 0x102a;
constexpr uint32_t TOP_REG_TX_CODE_VALUE     = 0x102b;
constexpr uint32_t TOP_REG_DATA_COLLECT_SWH  = 0X2602;
constexpr uint32_t TOP_REG_DATA_LOCK         = 0X2601;
constexpr uint32_t TOP_REG_CH_SEL            = 0X2600;
constexpr uint32_t TOP_REG_ECHO_AMP          = 0X2610;
constexpr uint32_t TOP_REG_SATU_NUM          = 0X2611;
constexpr uint32_t TOP_REG_AREA_GDI_H        = 0X2612;
constexpr uint32_t TOP_REG_AREA_GDI_L        = 0X2613;
constexpr uint32_t TOP_REG_DIST_GDI_H        = 0X2614;
constexpr uint32_t TOP_REG_DIST_GDI_L        = 0X2615;
constexpr uint32_t TOP_REG_CHARGE_FIX_EN     = 0x1208;
constexpr uint32_t TOP_REG_CHARGE_FIX_VALUE  = 0x1209;
constexpr uint32_t TOP_REG_GAIN_ADDR_MIN     = 0X1221;
constexpr uint32_t TOP_REG_GAIN_ADDR_MAX     = 0X1222;
constexpr uint32_t TOP_REG_AMP_MIN_H         = 0X1223;
constexpr uint32_t TOP_REG_AMP_MIN_L         = 0X1224;
constexpr uint32_t TOP_REG_AMP_MAX_H         = 0X1225;
constexpr uint32_t TOP_REG_AMP_MAX_L         = 0X1226;
constexpr uint32_t TOP_REG_SAT_NUM_THRE      = 0X1227;
constexpr uint32_t TOP_REG_REF_AMP_CFG       = 0X1228;
constexpr uint32_t TOP_REG_GAIN_ERR_CFG      = 0X1229;
constexpr uint32_t TOP_REG_AD_BASE_VALUE     = 0X122a;
//#define  TOP_REG_VBR_ADJ_EN             0X3007
constexpr uint32_t TOP_REG_VBR_ADJ_EN         = 0X3000;
constexpr uint32_t TOP_REG_TMPDIF             = 0X3001;
constexpr uint32_t TOP_REG_VBR_C              = 0X3002;
constexpr uint32_t TOP_REG_TMPC               = 0X3003;
constexpr uint32_t TOP_REG_TMPCOE             = 0X3004;
constexpr uint32_t TOP_REG_WORKCOE            = 0X3005;
constexpr uint32_t TOP_REG_VBR_LIMIT_EN       = 0X3006;
constexpr uint32_t TOP_REG_VBR_H              = 0X3007;
constexpr uint32_t TOP_REG_VBR_L              = 0X3008;
constexpr uint32_t TOP_REG_DAC1_CFG_DATA_H    = 0X3090;
constexpr uint32_t TOP_REG_DAC1_CFG_DATA_L    = 0X3091;
constexpr uint32_t TOP_REG_DAC2_CFG_DATA_H    = 0X3092;
constexpr uint32_t TOP_REG_DAC2_CFG_DATA_L    = 0X3093;
constexpr uint32_t TOP_REG_DAC3_CFG_DATA_H    = 0X3094;
constexpr uint32_t TOP_REG_DAC3_CFG_DATA_L    = 0X3095;
constexpr uint32_t TOP_REG_DAC4_CFG_DATA_H    = 0X3096;
constexpr uint32_t TOP_REG_DAC4_CFG_DATA_L    = 0X3097;
constexpr uint32_t TOP_REG_DAC_CFG_EN         = 0X3098;
constexpr uint32_t TOP_REG_CH_SWH             = 0X2603;
constexpr uint32_t TOP_REG_MODE_SWH           = 0X2604;
constexpr uint32_t TOP_REG_ADC_START_POINT_H  = 0X2605;
constexpr uint32_t TOP_REG_ADC_START_POINT_L  = 0X2606;
constexpr uint32_t TOP_REG_CYCLE_CNT          = 0X2607;
constexpr uint32_t TOP_REG_PWR_DEBUG_SEL      = 0X260A;
constexpr uint32_t TOP_REG_VIO_LD             = 0X3200;
constexpr uint32_t TOP_REG_VIO_INC            = 0X3201;
constexpr uint32_t TOP_REG_VIO_DEC            = 0X3202;
constexpr uint32_t TOP_REG_VIO_REGRST         = 0X3203;
constexpr uint32_t TOP_REG_SATU_CMP_EN        = 0X2231;
constexpr uint32_t TOP_REG_SATU_RAM_WR_EN     = 0X2232;
constexpr uint32_t TOP_REG_SATU_RAM_WR_DATA   = 0X2233;
constexpr uint32_t TOP_REG_SATU_RAM_WR_ADDR_H = 0X2234;
constexpr uint32_t TOP_REG_SATU_RAM_WR_ADDR_L = 0X2235;
constexpr uint32_t TOP_REG_SATU_RAM_RD_DATA   = 0X2236;
constexpr uint32_t TOP_REG_PATH_CHOOSE        = 0x200d;
constexpr uint32_t TOP_REG_AREA_THRE          = 0x201a;
constexpr uint32_t TOP_REG_DIS_CMP_SAT_EN     = 0X2018;
constexpr uint32_t TOP_REG_REF_DAS_MUX        = 0X2100;
constexpr uint32_t TOP_REG_STA_CMP_SWH        = 0x2200;

constexpr uint32_t BOT_REG_BOARD_ID                   = 0x83c00004;
constexpr uint32_t BOT_REG_COEFF1                     = 0x83c05000;
constexpr uint32_t BOT_REG_STEP1                      = 0x83c050EC;
constexpr uint32_t BOT_REG_SCALE1                     = 0x83c051D8;
constexpr uint32_t BOT_REG_LOCK                       = 0x83c052C4;
constexpr uint32_t BOT_REG_SET_MOTOR_SPEED            = 0x83c01000;
constexpr uint32_t BOT_REG_WAVE_MODE                  = 0x83c1000c;
constexpr uint32_t BOT_REG_ANGLE_TEST                 = 0x83c01028;
constexpr uint32_t BOT_REG_TOP_POWER                  = 0x83c20004;
constexpr uint32_t BOT_REG_OPTICAL_TEST               = 0x83c03200;
constexpr uint32_t BOT_REG_OPTICAL_UPLOAD_NUM         = 0x83c03204;
constexpr uint32_t BOT_REG_OPTICAL_UPLOAD_ERROR_NUM   = 0x83c03208;
constexpr uint32_t BOT_REG_OPTICAL_DOWNLOAD_NUM       = 0x83c03214;
constexpr uint32_t BOT_REG_OPTICAL_DOWNLOAD_ERROR_NUM = 0x83c03218;
constexpr uint32_t BOT_REG_OPTICAL_TEST_UNKOWN        = 0x83c03210;

}  // namespace helios

}  // namespace lidar
}  // namespace robosense

#endif  //HELIOS_REGISTER_ADDRESS_H
