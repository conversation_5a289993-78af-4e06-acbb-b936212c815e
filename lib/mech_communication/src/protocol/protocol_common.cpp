﻿/******************************************************************************
 * Copyright 2024 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai
 *
 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.
 *
 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONS<PERSON>QUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#include "protocol/protocol_common.h"
#include "rsfsc_log/rsfsc_log.h"
#include <boost/asio/ip/address.hpp>
#include <sstream>
#include <vector>

// NOLINTNEXTLINE(modernize-concat-nested-namespaces)
namespace robosense
{
namespace lidar
{

namespace mech
{
std::string ConfigPara::getSn() const { return fmt::format("{:02X}", fmt::join(sn, "")); }

uint32_t ConfigPara::getPsVersion() const
{
  uint32_t temp = 0;
  std::memcpy(&temp, ps_version.data(), 4);
  return temp;
}
uint32_t ConfigPara::getPlVersion() const
{
  uint32_t temp = 0;
  std::memcpy(&temp, pl_version.data(), 4);
  return temp;
}
uint32_t ConfigPara::getSoftwareVersion() const
{
  uint32_t temp = 0;
  std::memcpy(&temp, software_version.data(), 4);
  return temp;
}
uint32_t ConfigPara::getWebVersion() const
{
  uint32_t temp = 0;
  std::memcpy(&temp, web_version.data(), 4);
  return temp;
}
uint32_t ConfigPara::getMotorVersion() const
{
  uint32_t temp = 0;
  std::memcpy(&temp, motor_version.data(), 4);
  return temp;
}
std::string ConfigPara::getIpLocal() const { return net_info.getIpLocal(); }
uint16_t ConfigPara::getMsopPort() const { return net_info.getMsopPort(); }
uint16_t ConfigPara::getDifopPort() const { return net_info.getDifopPort(); }
std::string ConfigPara::getIpRemote() const { return net_info.getIpRemote(); }
std::string ConfigPara::getMacAddr() const { return net_info.getMacAddr(); }
std::string ConfigPara::getNetmaskLocal() const { return net_info.getNetmaskLocal(); }
std::string ConfigPara::getGatewayLocal() const { return net_info.getGatewayLocal(); }
uint16_t ConfigPara::getMotorRealTimeSpeed() const { return motor_real_time_speed; }
std::string NetPara::getIpLocal() const
{
  return fmt::format("{}.{}.{}.{}", ip_local[0], ip_local[1], ip_local[2], ip_local[3]);
}
uint16_t NetPara::getMsopPort() const { return msop_port; }
uint16_t NetPara::getDifopPort() const { return difop_port; }
std::string NetPara::getIpRemote() const
{
  return fmt::format("{}.{}.{}.{}", ip_remote[0], ip_remote[1], ip_remote[2], ip_remote[3]);
}
std::string NetPara::getMacAddr() const
{
  return fmt::format("{:02x}:{:02x}:{:02x}:{:02x}:{:02x}:{:02x}", mac[0], mac[1], mac[2], mac[3], mac[4], mac[5]);
}
std::string NetPara::getNetmaskLocal() const
{
  return fmt::format("{}.{}.{}.{}", netmask_local[0], netmask_local[1], netmask_local[2], netmask_local[3]);
}
std::string NetPara::getGatewayLocal() const
{
  return fmt::format("{}.{}.{}.{}", gateway_local[0], gateway_local[1], gateway_local[2], gateway_local[3]);
}

void NetPara::setIpLocal(const std::string& _ip)
{
  auto addr = boost::asio::ip::address::from_string(_ip).to_v4().to_bytes();
  std::copy(addr.begin(), addr.end(), ip_local.begin());
}
void NetPara::setMsopPort(const uint16_t _port) { msop_port = _port; }
void NetPara::setDifopPort(const uint16_t _port) { difop_port = _port; }
void NetPara::setIpRemote(const std::string& _ip)
{
  auto addr = boost::asio::ip::address::from_string(_ip).to_v4().to_bytes();
  std::copy(addr.begin(), addr.end(), ip_remote.begin());
}
void NetPara::setMacAddr(std::string _mac)
{
  std::vector<uint8_t> mac_addr;
  std::replace(_mac.begin(), _mac.end(), ':', ' ');
  std::istringstream iss(_mac);
  std::string token;
  while (std::getline(iss, token, ' '))
  {
    mac_addr.push_back(static_cast<uint8_t>(std::stoul(token, nullptr, 16)));
  }
  std::copy(mac_addr.begin(), mac_addr.end(), mac.begin());
}
void NetPara::setNetmaskLocal(const std::string& _netmask)
{
  auto addr = boost::asio::ip::address::from_string(_netmask).to_v4().to_bytes();
  std::copy(addr.begin(), addr.end(), netmask_local.begin());
}
void NetPara::setGatewayLocal(const std::string& _gateway)
{
  auto addr = boost::asio::ip::address::from_string(_gateway).to_v4().to_bytes();
  std::copy(addr.begin(), addr.end(), gateway_local.begin());
}

}  // namespace mech

}  // namespace lidar
}  // namespace robosense