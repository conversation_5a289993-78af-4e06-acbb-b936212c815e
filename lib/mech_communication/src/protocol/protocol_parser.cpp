﻿/******************************************************************************
 * Copyright 2023 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#include "protocol/protocol_parser.h"
#include "protocol/exp_calculator.hpp"
#include <iomanip>
#include <sstream>

namespace robosense  //NOLINT(modernize-concat-nested-namespaces)
{
namespace lidar
{
std::vector<uint8_t> ProtoParser::packRequest(const uint32_t _cmd_type, const std::vector<uint8_t>& _payload)
{
  // pack frame head
  std::vector<uint8_t> frame_array(requestFrameHeadPack(_cmd_type, static_cast<uint32_t>(_payload.size())));

  // pack frame payload data
  frame_array.insert(frame_array.end(), _payload.begin(), _payload.end());

  // pack frame tail
  frameTailPack(frame_array);

  // vector move semantic
  return frame_array;
}
std::vector<uint8_t> ProtoParser::packResponse(const uint32_t _cmd_type,
                                               const mech::ResponseType _response_type,
                                               const std::vector<uint8_t>& _payload)
{
  // pack frame head
  std::vector<uint8_t> frame_array(
    responseFrameHeadPack(_cmd_type, _response_type, static_cast<uint32_t>(_payload.size())));

  // pack frame payload data
  frame_array.insert(frame_array.end(), _payload.begin(), _payload.end());

  // pack frame tail
  frameTailPack(frame_array);

  // vector move semantic
  return frame_array;
}
void ProtoParser::setRegAddr(const std::string& _reg_name, const ProtoParser::RegisterInfo& _register_info)
{
  register_info_map.emplace(_reg_name, _register_info);
}

void ProtoParser::clear()
{
  std::lock_guard<std::mutex> lock(queue_mutex);
  std::queue<std::pair<uint32_t, std::vector<uint8_t>>>().swap(payload_queue);
  std::queue<uint32_t>().swap(register_value_queue);
  std::queue<uint32_t>().swap(register_addr_queue);
  std::queue<std::vector<uint8_t>>().swap(universal_queue);
}

std::string ProtoParser::hex(std::vector<uint8_t> _data, uint32_t _display_max_len)
{
  std::stringstream string_stream;
  string_stream << std::hex;

  auto size = _data.size() < _display_max_len ? _data.size() : _display_max_len;

  for (std::size_t i = 0; i < size; ++i)
  {
    string_stream << std::setw(2) << std::setfill('0') << static_cast<uint32_t>(_data.at(i)) << " ";
  }
  if (_data.size() > _display_max_len)
  {
    string_stream << "......";
  }
  return string_stream.str();
}

std::string ProtoParser::hex(uint32_t _data)
{
  std::stringstream string_stream;
  string_stream << std::hex << std::setfill('0') << _data;
  return string_stream.str();
}

std::vector<uint8_t>::iterator ProtoParser::findFrameFlag(std::vector<uint8_t>& _buffer, uint32_t _frame_flag)
{
  for (auto iter = _buffer.begin(); iter <= _buffer.end() - sizeof(_frame_flag); ++iter)
  {
    // NOLINTNEXTLINE(cppcoreguidelines-pro-type-reinterpret-cast)
    if (*reinterpret_cast<const uint32_t*>(&(*iter)) == _frame_flag)
    {
      return iter;
    }
  }
  return _buffer.end();
}

bool ProtoParser::getRegisterInfo(const std::string& _reg_name, RegisterInfo& _register_info)
{
  if (register_info_map.find(_reg_name) == register_info_map.end())
  {
    error_str = "Register name " + _reg_name + " not found.";
    return false;
  }
  _register_info = register_info_map.at(_reg_name);
  return true;
}

bool ProtoParser::evalRegisterInfo(uint32_t _value, const RegisterInfo& _register_info, double& _result)
{
  _value &= _register_info.bit_width_mask;
  if (_register_info.formula.empty())
  {
    _result = static_cast<float>(_value);
    return true;
  }
  auto exp   = _register_info.formula;
  size_t pos = exp.find("value");
  if (pos != std::string::npos)
  {
    // 将"value"替换为_value的真实数值
    exp.replace(pos, 5, std::to_string(_value));
  }

  ExpCalculator exp_calculator;
  if (exp_calculator.evaluate(exp))
  {
    _result = exp_calculator.getResult();
    return true;
  }

  error_str = "Failed to evaluate expression: " + exp;
  return false;
}
}  // namespace lidar
}  // namespace robosense
