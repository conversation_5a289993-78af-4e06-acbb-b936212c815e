﻿/******************************************************************************
 * Copyright 2023 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#include "protocol/mech_base_parser.h"
#include "protocol/protocol_common.h"
#include "protocol/protocol_parser.h"
#include "rsfsc_log/rsfsc_log_macro.h"
#include <cstdint>
#include <cstring>

#include "rsfsc_log/rsfsc_log.h"

uint16_t hostToNetShort(uint16_t _host_short)
{
  return static_cast<uint16_t>(_host_short >> 8U) | static_cast<uint16_t>(_host_short << 0x8U);
}
uint16_t netToHostShort(uint16_t _net_short) { return hostToNetShort(_net_short); }
uint32_t hostToNetLong(uint32_t _host_long)
{
  return ((_host_long >> 24U) & 0xffU) | ((_host_long << 8U) & 0xff0000U) | ((_host_long >> 8U) & 0xff00U) |
         ((_host_long << 24U) & 0xff000000U);
}
uint32_t netToHostLong(uint32_t _net_long) { return hostToNetLong(_net_long); }

namespace robosense  //NOLINT(modernize-concat-nested-namespaces)
{
namespace lidar
{
namespace mech
{
using CheckSumType                 = uint16_t;
using CheckSumType32               = uint32_t;
constexpr uint32_t FRAME_WRAP_SIZE = sizeof(FrameHead) - sizeof(FrameHead::cmd_type) -
                                     sizeof(FrameHead::response_type) + sizeof(FRAME_TAIL_FLAG) + sizeof(CheckSumType);
namespace server
{
constexpr uint32_t FRAME_WRAP_SIZE =
  sizeof(FrameHead) - sizeof(FrameHead::cmd_type) + sizeof(FRAME_TAIL_FLAG) + sizeof(CheckSumType);
}  // namespace server
}  // namespace mech

std::string MechBaseParser::getCurrentProtocolType() { return "Mech Base"; }

bool MechBaseParser::packReadConfigPara(uint32_t& _expected_packet_response_code, std::vector<uint8_t>& _packet)
{
  std::vector<uint8_t> data;
  return packReadCmd(mech::NET_CMD_CONFIG_READ_ALL, data, _expected_packet_response_code, _packet);
}

bool MechBaseParser::packWriteConRegData(const uint32_t _start_reg_addr,
                                         const std::vector<uint32_t>& _reg_val,
                                         uint32_t& _expected_packet_response_code,
                                         std::vector<uint8_t>& _packet)
{
  _expected_packet_response_code = mech::NET_CMD_BOTTOM_BOARD_CON_WRITE_REGISTER;
  const uint16_t REGISTER_COUNT  = static_cast<uint16_t>(_reg_val.size());

  std::vector<uint8_t> payload(sizeof(_start_reg_addr) + sizeof(REGISTER_COUNT) + REGISTER_COUNT * sizeof(uint32_t));
  size_t index = 0;
  // 数据长度 = addr(4) + reg_num(2) + val * reg_num(4 * n)
  copyToPayload(payload, _start_reg_addr, index);
  copyToPayload(payload, REGISTER_COUNT, index);
  copyArrayToPayload(payload, _reg_val.data(), REGISTER_COUNT, index);

  _packet = packRequest(_expected_packet_response_code, payload);
  return true;
}
bool MechBaseParser::packWriteConTopRegData(const uint32_t _start_reg_addr,
                                            const std::vector<uint32_t>& _reg_val,
                                            uint32_t& _expected_packet_response_code,
                                            std::vector<uint8_t>& _packet)
{
  _expected_packet_response_code = mech::NET_CMD_TOP_BOARD_CON_WRITE_REGISTER;
  const uint16_t REGISTER_COUNT  = static_cast<uint16_t>(_reg_val.size());

  std::vector<uint8_t> payload(sizeof(_start_reg_addr) + sizeof(REGISTER_COUNT) + REGISTER_COUNT * sizeof(uint32_t));
  size_t index = 0;
  // 数据长度 = addr(4) + reg_num(2) + val * reg_num(4 * n)
  copyToPayload(payload, _start_reg_addr, index);
  copyToPayload(payload, REGISTER_COUNT, index);
  copyArrayToPayload(payload, _reg_val.data(), REGISTER_COUNT, index);

  _packet = packRequest(_expected_packet_response_code, payload);
  return true;
}

bool MechBaseParser::packReadConRegData(const uint32_t _start_reg_addr,
                                        const uint32_t _reg_num,
                                        uint32_t& _expected_packet_response_code,
                                        std::vector<uint8_t>& _packet)
{
  _expected_packet_response_code = mech::NET_CMD_BOTTOM_BOARD_CON_READ_REGISTER;
  uint16_t count                 = static_cast<uint16_t>(_reg_num);
  std::vector<uint8_t> payload(sizeof(_start_reg_addr) + sizeof(count));

  // 数据长度 = addr(4byte) + reg_num(2byte)
  size_t index = 0;
  copyToPayload(payload, _start_reg_addr, index);
  copyToPayload(payload, count, index);

  _packet = packRequest(_expected_packet_response_code, payload);
  return true;
}
bool MechBaseParser::packReadConTopRegData(const uint32_t _start_reg_addr,
                                           const uint32_t _reg_num,
                                           uint32_t& _expected_packet_response_code,
                                           std::vector<uint8_t>& _packet)
{
  _expected_packet_response_code = mech::NET_CMD_TOP_BOARD_CON_READ_REGISTER;
  uint16_t count                 = static_cast<uint16_t>(_reg_num);
  std::vector<uint8_t> payload(sizeof(_start_reg_addr) + sizeof(count));

  // 数据长度 = addr(4byte) + reg_num(2byte)
  size_t index = 0;
  copyToPayload(payload, _start_reg_addr, index);
  copyToPayload(payload, count, index);

  _packet = packRequest(_expected_packet_response_code, payload);
  return true;
}

bool MechBaseParser::packWriteDigitalRegister(const RegisterData /*_register_data*/,
                                              uint32_t& /*_expected_packet_response_code*/,
                                              std::vector<uint8_t>& /*_packet*/)
{
  error_str = "unimplemented function: MechBaseParser::packTxChannelCtrlExclusively";
  return false;
}
bool MechBaseParser::packReadDigitalRegister(const uint32_t /*_reg_addr*/,
                                             uint32_t& /*_expected_packet_response_code*/,
                                             std::vector<uint8_t>& /*_packet*/)
{
  error_str = "unimplemented function: MechBaseParser::packTxChannelCtrlExclusively";
  return false;
}

bool MechBaseParser::packReadRegister(const std::vector<uint32_t>& _reg_addr,
                                      uint32_t& _expected_packet_response_code,
                                      std::vector<uint8_t>& _packet)
{
  if (_reg_addr.empty())
  {
    LOG_INDEX_ERROR("addr or val number error, addr size: {}, val size: ", _reg_addr.size());

    return false;
  }
  _expected_packet_response_code = mech::NET_CMD_BOTTOM_BOARD_MULTI_READ_REGISTER;
  const uint16_t REGISTER_COUNT  = static_cast<uint16_t>(_reg_addr.size());

  std::vector<uint8_t> payload(sizeof(REGISTER_COUNT) + REGISTER_COUNT * sizeof(uint32_t));
  size_t index = 0;
  copyToPayload(payload, REGISTER_COUNT, index);
  copyArrayToPayload(payload, _reg_addr.data(), _reg_addr.size(), index);

  _packet = packRequest(_expected_packet_response_code, payload);
  return true;
}

bool MechBaseParser::packWriteRegister(const std::vector<uint32_t>& _reg_addr,
                                       const std::vector<uint32_t>& _reg_val,
                                       uint32_t& _expected_packet_response_code,
                                       std::vector<uint8_t>& _packet)
{
  if ((_reg_addr.size() != _reg_val.size()) || _reg_addr.empty())
  {
    LOG_INDEX_ERROR("addr or val number error, addr size: {}, val size: " + std::to_string(_reg_val.size()),
                    _reg_addr.size());
    return false;
  }
  if (_reg_addr.size() == 1)
  {
    return packWriteConRegData(_reg_addr.at(0), _reg_val, _expected_packet_response_code, _packet);
  }

  _expected_packet_response_code = mech::NET_CMD_BOTTOM_BOARD_MULTI_WRITE_REGISTER;

  const uint16_t REGISTER_COUNT = static_cast<uint16_t>(_reg_addr.size());
  std::vector<uint8_t> payload(sizeof(REGISTER_COUNT) + REGISTER_COUNT * sizeof(RegisterData));

  size_t index = 0;
  copyToPayload(payload, REGISTER_COUNT, index);
  for (std::vector<uint32_t>::size_type i = 0; i < _reg_addr.size(); ++i)
  {
    copyToPayload(payload, _reg_addr.at(i), index);
    copyToPayload(payload, _reg_val.at(i), index);
  }

  _packet = packRequest(_expected_packet_response_code, payload);
  return true;
}

bool MechBaseParser::packWriteRegister(const std::vector<RegisterData>& _reg_data,
                                       uint32_t& _expected_packet_response_code,
                                       std::vector<uint8_t>& _packet)
{
  if (_reg_data.size() == 1)
  {
    std::vector<uint32_t> reg_val;
    reg_val.emplace_back(_reg_data.at(0).value);
    return packWriteConRegData(_reg_data.at(0).address, reg_val, _expected_packet_response_code, _packet);
  }

  _expected_packet_response_code = mech::NET_CMD_BOTTOM_BOARD_MULTI_WRITE_REGISTER;
  const uint16_t REGISTER_COUNT  = static_cast<uint16_t>(_reg_data.size());

  std::vector<uint8_t> payload(sizeof(REGISTER_COUNT) + REGISTER_COUNT * sizeof(RegisterData));
  size_t index = 0;
  copyToPayload(payload, REGISTER_COUNT, index);
  copyArrayToPayload(payload, _reg_data.data(), REGISTER_COUNT, index);

  _packet = packRequest(_expected_packet_response_code, payload);
  return true;
}

bool MechBaseParser::packWriteTopRegister(const std::vector<uint32_t>& _reg_addr,
                                          const std::vector<uint32_t>& _reg_val,
                                          uint32_t& _expected_packet_response_code,
                                          std::vector<uint8_t>& _packet)
{
  if ((_reg_addr.size() != _reg_val.size()) || _reg_addr.empty())
  {
    LOG_INDEX_ERROR("addr or val number error, addr size: {}, val size: " + std::to_string(_reg_val.size()),
                    _reg_addr.size());
    return false;
  }

  if (_reg_addr.size() == 1)
  {
    return packWriteConTopRegData(_reg_addr.at(0), _reg_val, _expected_packet_response_code, _packet);
  }

  _expected_packet_response_code = mech::NET_CMD_TOP_BOARD_MULTI_WRITE_REGISTER;
  const uint16_t REGISTER_COUNT  = static_cast<uint16_t>(_reg_addr.size());

  std::vector<uint8_t> payload(sizeof(REGISTER_COUNT) + REGISTER_COUNT * sizeof(RegisterData));
  size_t index = 0;
  copyToPayload(payload, REGISTER_COUNT, index);
  for (std::vector<uint32_t>::size_type i = 0; i < _reg_addr.size(); ++i)
  {
    copyToPayload(payload, _reg_addr.at(i), index);
    copyToPayload(payload, _reg_val.at(i), index);
  }
  _packet = packRequest(_expected_packet_response_code, payload);
  return true;
}

bool MechBaseParser::packWriteTopRegister(const std::vector<RegisterData>& _reg_data,
                                          uint32_t& _expected_packet_response_code,
                                          std::vector<uint8_t>& _packet)
{
  _expected_packet_response_code = mech::NET_CMD_TOP_BOARD_MULTI_WRITE_REGISTER;
  const uint16_t REGISTER_COUNT  = static_cast<uint16_t>(_reg_data.size());

  std::vector<uint8_t> payload(sizeof(REGISTER_COUNT) + REGISTER_COUNT * sizeof(RegisterData));
  size_t index = 0;
  copyToPayload(payload, REGISTER_COUNT, index);
  copyArrayToPayload(payload, _reg_data.data(), REGISTER_COUNT, index);
  _packet = packRequest(_expected_packet_response_code, payload);
  return true;
}

bool MechBaseParser::packReadTopRegister(const std::vector<uint32_t>& _reg_addr,
                                         uint32_t& _expected_packet_response_code,
                                         std::vector<uint8_t>& _packet)
{
  if (_reg_addr.empty())
  {
    LOG_INDEX_ERROR("addr or val number error, addr size: {}, val size: ", _reg_addr.size());

    return false;
  }
  _expected_packet_response_code = mech::NET_CMD_TOP_BOARD_MULTI_READ_REGISTER;
  const uint16_t REGISTER_COUNT  = static_cast<uint16_t>(_reg_addr.size());

  std::vector<uint8_t> payload(sizeof(REGISTER_COUNT) + REGISTER_COUNT * sizeof(uint32_t));
  size_t index = 0;
  copyToPayload(payload, REGISTER_COUNT, index);
  copyArrayToPayload(payload, _reg_addr.data(), _reg_addr.size(), index);

  _packet = packRequest(_expected_packet_response_code, payload);
  return true;
}

bool MechBaseParser::packGetEyesSafe(uint32_t& /*_expected_packet_response_code*/, std::vector<uint8_t>& /*_packet*/)
{
  error_str = "this protocol unsupported get eyes safe";
  return false;
}

bool MechBaseParser::packSetEyesSafe(const uint32_t _is_open,
                                     uint32_t& _expected_packet_response_code,
                                     std::vector<uint8_t>& _packet)
{
  std::vector<uint8_t> payload(sizeof(uint16_t));
  uint16_t is_open = static_cast<uint16_t>(_is_open);
  std::memcpy(payload.data(), &is_open, sizeof(is_open));
  return packWriteCmd(mech::NET_CMD_TOP_BOARD_EYES_SAFE, payload, _expected_packet_response_code, _packet);
}

bool MechBaseParser::packStartupCheck(uint32_t& _expected_packet_response_code, std::vector<uint8_t>& _packet)
{
  return packReadRegister(std::vector<uint32_t>({ bpearl::LINK_STATUS_ADDR }), _expected_packet_response_code, _packet);
}

bool MechBaseParser::packPSDefinedReady(uint32_t& _expected_packet_response_code, std::vector<uint8_t>& _packet)
{
  // uint16 copy to std::vector<uint8_t>
  uint16_t ps_defined_ready = 2;
  std::vector<uint8_t> ps_defined_ready_vec(sizeof(ps_defined_ready));
  std::memcpy(ps_defined_ready_vec.data(), &ps_defined_ready, sizeof(ps_defined_ready));
  return packWriteCmd(mech::NET_CMD_PS_DEFINED, ps_defined_ready_vec, _expected_packet_response_code, _packet);
}

bool MechBaseParser::packReadCmd(const uint32_t _cmd_type,
                                 const std::vector<uint8_t>& _data,
                                 uint32_t& _expected_packet_response_code,
                                 std::vector<uint8_t>& _packet)
{
  if (_cmd_type > mech::NET_CMD_END || _cmd_type < mech::NET_CMD_BEGIN)
  {
    error_str = "cmd type error out of range, cmd type: " + std::to_string(_cmd_type);
    return false;
  }
  _expected_packet_response_code = _cmd_type;

  _packet = packRequest(_expected_packet_response_code, _data);
  return true;
}

// NOLINTNEXTLINE(bugprone-easily-swappable-parameters)
bool MechBaseParser::packWriteCmd(const uint32_t _cmd_type,
                                  const std::vector<uint8_t>& _data,
                                  uint32_t& _expected_packet_response_code,
                                  std::vector<uint8_t>& _packet)
{
  if (_cmd_type > mech::NET_CMD_END || _cmd_type < mech::NET_CMD_BEGIN)
  {
    return false;
  }
  _expected_packet_response_code = _cmd_type;

  _packet = packRequest(_expected_packet_response_code, _data);
  return true;
}

bool MechBaseParser::packWriteIpPort(const uint32_t _ip,
                                     // NOLINTNEXTLINE(bugprone-easily-swappable-parameters)
                                     uint16_t _msop_port,
                                     uint16_t _difop_port,
                                     uint32_t& _expected_packet_response_code,
                                     std::vector<uint8_t>& _packet)
{
  if (_packet.size() != sizeof(mech::ConfigPara))
  {
    return false;
  }
  _expected_packet_response_code = mech::NET_CMD_CONFIG_SET_NETWORK;

  mech::ConfigPara config_para {};
  std::memcpy(&config_para, _packet.data(), sizeof(mech::ConfigPara));

  if (_msop_port == 0)
  {
    _msop_port = config_para.net_info.msop_port;
  }

  if (_difop_port == 0)
  {
    _difop_port = config_para.net_info.difop_port;
  }

  mech::NetPara net_para {};
  net_para = config_para.net_info;

  std::memcpy(&net_para.ip_local, &_ip, sizeof(_ip));
  net_para.msop_port  = _msop_port;
  net_para.difop_port = _difop_port;

  std::vector<uint8_t> payload(sizeof(net_para));

  auto index = 0;
  // pack net para
  std::memcpy(&payload.at(index), &net_para, sizeof(net_para));

  _packet = packRequest(_expected_packet_response_code, payload);
  return true;
}

bool MechBaseParser::isLidarStartUp(const uint32_t _value) { return (_value & 0x1UL) == 1; }

bool MechBaseParser::isPSDefinedReady(const std::vector<uint8_t>& _value_vec)
{
  if (_value_vec.size() < 5)
  {
    error_str = "ps defined ready data length error, expect: 5, actual: " + std::to_string(_value_vec.size());
    return false;
  }

  return (_value_vec[0] == 2 && _value_vec[1] == 0 && _value_vec.back() == 0x31);
}

bool MechBaseParser::packReadIntensity(const mech::IntensityData& _intensity_data,
                                       uint32_t& _expected_packet_response_code,
                                       std::vector<uint8_t>& _packet)
{
  _expected_packet_response_code = mech::NET_CMD_TOP_BOARD_GET_INTENSITY;
  std::vector<uint8_t> payload(sizeof(mech::IntensityData));
  size_t index = 0;
  std::memcpy(&payload.at(index), &_intensity_data, sizeof(mech::IntensityData));

  _packet = packRequest(_expected_packet_response_code, payload);
  return true;
}

bool MechBaseParser::packCtrlTxChannelExclusively(const int /*_channel_num*/,
                                                  const bool /*_is_open*/,
                                                  uint32_t& /*_expected_packet_response_code*/,
                                                  std::vector<uint8_t>& /*_packet*/)
{
  LOG_INDEX_ERROR("MechBaseParser:: unimplemented function: packTxChannelCtrlExclusively");
  return false;
}
bool MechBaseParser::packReadTxChannelAll(uint32_t& /*_expected_packet_response_code*/,
                                          std::vector<uint8_t>& /*_packet*/)
{
  LOG_INDEX_ERROR("MechBaseParser:: unimplemented function: packReadTxChannelAll");
  return false;
}
bool MechBaseParser::packCtrlTxChannelAll(const bool _open,
                                          uint32_t& _expected_packet_response_code,
                                          std::vector<uint8_t>& _packet)
{
  LOG_INDEX_ERROR("MechBaseParser:: unimplemented function: packCtrlTxChannelAll");
  return false;
}
bool MechBaseParser::packCtrlTxChannel(const int /*_channel_num*/,
                                       const bool /*_open*/,
                                       const uint32_t /*_curr_value*/,
                                       uint32_t& /*_expected_packet_response_code*/,
                                       std::vector<uint8_t>& /*_packet*/)
{
  LOG_INDEX_ERROR("MechBaseParser:: unimplemented function: packCtrlTxChannel");
  return false;
}
bool MechBaseParser::getTxChannelRegAddr(const int /*_channel_num*/, uint32_t& /*_tx_channel_reg_addr*/)
{
  LOG_INDEX_ERROR("unsupported function: getTxChannelRegAddr");
  return false;
}

// NOLINTNEXTLINE(bugprone-easily-swappable-parameters)
bool MechBaseParser::packStartWriteTopFlash(const uint32_t _start_addr,
                                            const uint32_t _len,
                                            ExpectedResp& _expected_resp,
                                            std::vector<uint8_t>& _packet)
{
  _expected_resp.cmd = mech::NET_CMD_TOP_BOARD_WRITE_FLASH;
  std::vector<uint8_t> payload(sizeof(mech::FLASH_CMD_REQ_WRITE) + sizeof(_start_addr) + sizeof(_len));
  size_t index = 0;
  copyToPayload(payload, mech::FLASH_CMD_REQ_WRITE, index);
  copyToPayload(payload, _start_addr, index);
  copyToPayload(payload, _len, index);

  _packet = packRequest(_expected_resp.cmd, payload);

  // expected response
  index = 0;
  _expected_resp.data.resize(sizeof(mech::FLASH_CMD_REQ_WRITE));
  copyToPayload(_expected_resp.data, mech::FLASH_CMD_REQ_WRITE, index);

  return true;
}
bool MechBaseParser::packWriteTopFlash(const uint32_t _pkt_count,
                                       const std::vector<uint8_t>& _data,
                                       ExpectedResp& _expected_resp,
                                       std::vector<uint8_t>& _packet)
{
  if (_data.empty() || _data.size() > 1024)
  {
    LOG_INDEX_ERROR("MechBaseParser::packWriteTopFlash data size out of range 1~1024, current data size: {}",
                    _data.size());
    return false;
  }

  _expected_resp.cmd = mech::NET_CMD_TOP_BOARD_WRITE_FLASH;
  std::vector<uint8_t> payload(sizeof(mech::FLASH_CMD_WRITE) + sizeof(_pkt_count) + _data.size());
  size_t index = 0;
  copyToPayload(payload, mech::FLASH_CMD_WRITE, index);
  copyToPayload(payload, _pkt_count, index);
  copyArrayToPayload(payload, _data.data(), _data.size(), index);

  _packet = packRequest(_expected_resp.cmd, payload);

  // expected response
  index = 0;
  _expected_resp.data.resize(sizeof(mech::FLASH_CMD_WRITE));
  copyToPayload(_expected_resp.data, mech::FLASH_CMD_WRITE, index);
  // copyToPayload(_expected_resp.data, _pkt_count, index);

  return true;
}
bool MechBaseParser::packFinishWriteTopFlash(ExpectedResp& _expected_resp, std::vector<uint8_t>& _packet)
{
  _expected_resp.cmd = mech::NET_CMD_TOP_BOARD_WRITE_FLASH;
  std::vector<uint8_t> payload(sizeof(mech::FLASH_CMD_WRITE_FINISH));
  size_t index = 0;
  copyToPayload(payload, mech::FLASH_CMD_WRITE_FINISH, index);

  _packet = packRequest(_expected_resp.cmd, payload);

  // expected response
  index = 0;
  _expected_resp.data.resize(sizeof(mech::FLASH_CMD_WRITE_FINISH));
  copyToPayload(_expected_resp.data, mech::FLASH_CMD_WRITE_FINISH, index);

  return true;
}

bool MechBaseParser::isValidMsop(const char* _packet)
{
  const mech::MsopPacket* pkt_data = static_cast<const mech::MsopPacket*>(static_cast<const void*>(_packet));

  return pkt_data->frame_flag == bpearl::MSOP_FRAME_FLAG;
}

// NOLINTNEXTLINE(bugprone-easily-swappable-parameters)
std::vector<uint8_t> MechBaseParser::requestFrameHeadPack(const uint32_t _cmd_type, const uint32_t _payload_size)
{
  mech::FrameHead frame_head {};
  auto correct_payload_size = _payload_size + sizeof(mech::FrameHead::cmd_type);

  // pack frame head，length should add cmd size
  frame_head.frame_header = mech::FRAME_FLAG;
  frame_head.frame_type   = mech::FRAME_TYPE_REQUEST;
  frame_head.length       = hostToNetShort(static_cast<uint16_t>(correct_payload_size));
  frame_head.cmd_type     = hostToNetShort(static_cast<uint16_t>(_cmd_type));

  auto header_size = sizeof(mech::FrameHead) - sizeof(mech::FrameHead::response_type);
  std::vector<uint8_t> frame_array(header_size);

  // frame head size + payload size + frame tail size + check sum size
  frame_array.reserve(header_size + correct_payload_size + sizeof(mech::FRAME_TAIL_FLAG) + sizeof(mech::CheckSumType));

  std::memcpy(frame_array.data(), &frame_head, header_size);

  return frame_array;
}

std::vector<uint8_t> MechBaseParser::responseFrameHeadPack(const uint32_t _cmd_type,
                                                           const mech::ResponseType _response_type,
                                                           const uint32_t _payload_size)
{
  mech::FrameHead frame_head {};
  auto correct_payload_size =
    _payload_size + sizeof(mech::FrameHead::cmd_type) + sizeof(mech::FrameHead::response_type);

  // pack frame head，length should add cmd size
  frame_head.frame_header  = mech::FRAME_FLAG;
  frame_head.frame_type    = mech::FRAME_TYPE_ACK;
  frame_head.length        = hostToNetShort(static_cast<uint16_t>(correct_payload_size));
  frame_head.cmd_type      = hostToNetShort(static_cast<uint16_t>(_cmd_type));
  frame_head.response_type = static_cast<uint8_t>(_response_type);

  auto header_size = sizeof(mech::FrameHead);
  std::vector<uint8_t> frame_array(header_size);

  // frame head size + payload size + frame tail size + check sum size
  frame_array.reserve(header_size + correct_payload_size + sizeof(mech::FRAME_TAIL_FLAG) + sizeof(mech::CheckSumType));

  std::memcpy(frame_array.data(), &frame_head, header_size);

  return frame_array;
}

std::vector<uint8_t> MechBaseParser::frameTailPack(std::vector<uint8_t>& _frame_array)
{
  // notice that it is necessary to include the tail byte and frame type of
  // the data frame for calculating checksum
  _frame_array.emplace_back(mech::FRAME_TAIL_FLAG);

  // cppcoreguidelines-pro-bounds-pointer-arithmetic
  const uint16_t CHECK_SUM = mech::checkSum(&_frame_array.at(sizeof(mech::FRAME_FLAG)),
                                            static_cast<uint16_t>(_frame_array.size() - sizeof(mech::FRAME_FLAG)));

  _frame_array.emplace_back((CHECK_SUM >> 8U) & 0xFFU);  // NOLINT
  _frame_array.emplace_back(CHECK_SUM & 0xFFU);

  return _frame_array;
}

bool MechBaseParser::parseClientPacket(std::vector<uint8_t>& _packet_buffer)
{
  if (_packet_buffer.size() < sizeof(mech::FrameHead))
  {
    error_str = "payload size error, receive payload size:" + std::to_string(_packet_buffer.size());
    return false;
  }

  auto* frame_head = static_cast<mech::FrameHead*>(static_cast<void*>(_packet_buffer.data()));

  while (frame_head->frame_header == mech::FRAME_FLAG)
  {
    auto payload_size = netToHostShort(frame_head->length);

    auto expected_data_size = payload_size + mech::FRAME_WRAP_SIZE;
    if (_packet_buffer.size() < expected_data_size)
    {
      error_str = "data length error, expect not less than: " + std::to_string(expected_data_size) +
                  ", actual: " + std::to_string(_packet_buffer.size());
      return false;
    }

    auto check_sum_data_size = expected_data_size - sizeof(mech::FRAME_FLAG) - sizeof(mech::CheckSumType);
    const mech::CheckSumType CAL_CHECK_SUM =
      mech::checkSum(&_packet_buffer.at(sizeof(mech::FRAME_FLAG)), static_cast<uint16_t>(check_sum_data_size));

    mech::CheckSumType check_sum = 0;
    std::memcpy(&check_sum, &_packet_buffer.at(expected_data_size - sizeof(mech::CheckSumType)), sizeof(check_sum));
    check_sum = netToHostShort(check_sum);

    if (CAL_CHECK_SUM != check_sum)
    {
      error_str = "check sum error, expect: " + hex(CAL_CHECK_SUM) + ", actual: " + hex(check_sum);
      _packet_buffer.clear();
      return false;
    }

    mech::ResponseType response_type = static_cast<mech::ResponseType>(frame_head->response_type);
    switch (response_type)
    {
    case mech::ResponseType::SUCCESS:
    {
      error_str = "";
      break;
    }
    case mech::ResponseType::UNSUPPORTED:
    {
      error_str =
        "response type error: 0x" + std::to_string(static_cast<uint32_t>(response_type)) + "，error type: 不支持该指令";
      break;
    }
    case mech::ResponseType::PARAMETER_ERROR:
    {
      error_str =
        "response type error: 0x" + std::to_string(static_cast<uint32_t>(response_type)) + "，error type: 参数错误";
      break;
    }
    case mech::ResponseType::DATA_LEN_ERROR:
    {
      error_str =
        "response type error: 0x" + std::to_string(static_cast<uint32_t>(response_type)) + "，error type: 数据长度错误";
      break;
    }
    case mech::ResponseType::FORMAT_ERROR:
    {
      error_str =
        "response type error: 0x" + std::to_string(static_cast<uint32_t>(response_type)) + "，error type: 格式错误";
      break;
    }
    case mech::ResponseType::CHECKSUM_ERROR:
    {
      error_str =
        "response type error: 0x" + std::to_string(static_cast<uint32_t>(response_type)) + "，error type: 校验和错误";
      break;
    }
    case mech::ResponseType::OTHER:
    {
      error_str =
        "response type error: 0x" + std::to_string(static_cast<uint32_t>(response_type)) + "，error type: 其他错误";
      break;
    }
    case mech::ResponseType::TIMEOUT:
    {
      error_str =
        "response type error: 0x" + std::to_string(static_cast<uint32_t>(response_type)) + "，error type: 超时错误";
      break;
    }
    case mech::ResponseType::CURRENT_STATUS:
    {
      error_str =
        "response type error: 0x" + std::to_string(static_cast<uint32_t>(response_type)) + "，error type: 当前状态错误";
      break;
    }
    case mech::ResponseType::VERSION_UNSUPPORTED:
    {
      error_str =
        "response type error: 0x" + std::to_string(static_cast<uint32_t>(response_type)) + "，error type: 版本不支持";
      break;
    }
    default:
    {
      error_str = fmt::format("错误码: {}", static_cast<uint32_t>(response_type));
      break;
    }
    }
    const std::vector<uint8_t> PAYLOAD(
      _packet_buffer.begin() + sizeof(mech::FrameHead),
      _packet_buffer.begin() + expected_data_size - sizeof(mech::FRAME_TAIL_FLAG) - sizeof(mech::CheckSumType));
    _packet_buffer.erase(_packet_buffer.begin(), _packet_buffer.begin() + expected_data_size);

    if (response_type != mech::ResponseType::SUCCESS)
    {
      return false;
    }

    {
      std::lock_guard<std::mutex> lock(queue_mutex);
      payload_queue.emplace(netToHostShort(frame_head->cmd_type), PAYLOAD);
    }

    if (_packet_buffer.size() < sizeof(mech::FrameHead))
    {
      break;
    }
    frame_head = static_cast<mech::FrameHead*>(static_cast<void*>(_packet_buffer.data()));
  }

  return true;
}

bool MechBaseParser::parseServerPacket(std::vector<uint8_t>& _packet_buffer)
{
  if (_packet_buffer.size() < sizeof(mech::server::FrameHead))
  {
    error_str = "payload size error, receive payload size:" + std::to_string(_packet_buffer.size());
    return false;
  }

  // NOLINTNEXTLINE(cppcoreguidelines-pro-type-reinterpret-cast)
  auto* frame_head = reinterpret_cast<mech::server::FrameHead*>(_packet_buffer.data());
  while (frame_head->frame_header == mech::FRAME_FLAG)
  {
    auto payload_size = netToHostShort(frame_head->length);

    auto expected_data_size = payload_size + mech::server::FRAME_WRAP_SIZE;
    if (_packet_buffer.size() < expected_data_size)
    {
      error_str = "data length error, expect not less than: " + std::to_string(expected_data_size) +
                  ", actual: " + std::to_string(_packet_buffer.size());
      return false;
    }

    auto check_sum_data_size = expected_data_size - sizeof(mech::FRAME_FLAG) - sizeof(mech::CheckSumType);
    const mech::CheckSumType CAL_CHECK_SUM =
      mech::checkSum(&_packet_buffer.at(sizeof(mech::FRAME_FLAG)), static_cast<uint16_t>(check_sum_data_size));

    mech::CheckSumType check_sum = 0;
    std::memcpy(&check_sum, &_packet_buffer.at(expected_data_size - sizeof(mech::CheckSumType)), sizeof(check_sum));
    check_sum = netToHostShort(check_sum);

    if (CAL_CHECK_SUM != check_sum)
    {
      error_str = "check sum error, expect: " + hex(CAL_CHECK_SUM) + ", actual: " + hex(check_sum);
      _packet_buffer.clear();
      return false;
    }

    const std::vector<uint8_t> PAYLOAD(
      _packet_buffer.begin() + sizeof(mech::server::FrameHead),
      _packet_buffer.begin() + expected_data_size - sizeof(mech::FRAME_TAIL_FLAG) - sizeof(mech::CheckSumType));
    _packet_buffer.erase(_packet_buffer.begin(), _packet_buffer.begin() + expected_data_size);

    {
      std::lock_guard<std::mutex> lock(queue_mutex);
      payload_queue.emplace(netToHostShort(frame_head->cmd_type), PAYLOAD);
    }

    if (_packet_buffer.size() < sizeof(mech::server::FrameHead))
    {
      break;
    }
    // NOLINTNEXTLINE(cppcoreguidelines-pro-type-reinterpret-cast)
    frame_head = reinterpret_cast<mech::server::FrameHead*>(_packet_buffer.data());
  }

  return true;
}

bool MechBaseParser::extractRegister(const std::vector<uint8_t>& _payload)
{

  if ((_payload.size()) % sizeof(RegisterData) != 0)
  {
    error_str = "register data length error, payload size must divisible by 4, payload actual size:" +
                std::to_string(_payload.size());
    return false;
  }

  std::queue<uint32_t>().swap(register_value_queue);
  std::queue<uint32_t>().swap(register_addr_queue);

  RegisterData register_data;
  for (size_t i = 0; i < _payload.size(); i += sizeof(register_data))
  {
    memcpy(&register_data, &_payload.at(i), sizeof(register_data));
    register_addr_queue.emplace(register_data.address);
    register_value_queue.emplace(register_data.value);
  }

  return true;
}

bool MechBaseParser::extractNormalPacket(const std::vector<uint8_t>& _packet)
{
  if (!_packet.empty())
  {
    universal_queue.emplace(_packet.begin(), _packet.end());
  }
  return true;
}

bool MechBaseParser::extractMultiRegister(const std::vector<uint8_t>& _payload)
{

  if ((_payload.size()) % sizeof(uint32_t) != 0)
  {
    error_str = "register data length error, payload size must divisible by 4, payload actual size:" +
                std::to_string(_payload.size());
    return false;
  }

  if (!register_value_queue.empty())
  {
    std::queue<uint32_t>().swap(register_value_queue);
  }

  uint32_t register_data = 0;
  for (size_t i = 0; i < _payload.size(); i += sizeof(register_data))
  {
    memcpy(&register_data, &_payload.at(i), sizeof(register_data));
    register_value_queue.emplace(register_data);
  }

  return true;
}
bool MechBaseParser::extractConRegister(const std::vector<uint8_t>& _payload)
{
  if ((_payload.size()) % sizeof(uint32_t) != 0)
  {
    error_str = "register data length error, payload size must divisible by 4, payload actual size:" +
                std::to_string(_payload.size());
    return false;
  }
  if (!register_value_queue.empty())
  {
    std::queue<uint32_t>().swap(register_value_queue);
  }
  if (_payload.size() < sizeof(uint32_t) * 2)
  {
    error_str = "register data length error, payload size must be greater than 8, payload actual size:" +
                std::to_string(_payload.size());
    return false;
  }

  uint32_t register_data = 0;
  for (size_t i = sizeof(register_data); i < _payload.size(); i += sizeof(register_data))
  {
    memcpy(&register_data, &_payload.at(i), sizeof(register_data));
    register_value_queue.emplace(register_data);
  }
  return true;
}

bool MechBaseParser::extractConfigRegister(const std::vector<uint8_t>& _payload)
{
  if (_payload.size() < 2)
  {
    is_config_register_read_finish = true;
    return true;
  }

  if (_payload.size() < sizeof(uint16_t) * 3)
  {
    error_str = "config register data length error, payload size must be greater than 4, payload actual size:" +
                std::to_string(_payload.size());
    return false;
  }

  uint16_t reg_num            = 0;
  uint16_t curr_frame_num     = 0;
  uint16_t curr_frame_reg_num = 0;

  size_t index = 0;
  ProtoParser::copyFromPayload(_payload, reg_num, index);
  ProtoParser::copyFromPayload(_payload, curr_frame_num, index);
  ProtoParser::copyFromPayload(_payload, curr_frame_reg_num, index);

  if (_payload.size() != static_cast<size_t>(curr_frame_reg_num) * 8 + sizeof(uint16_t) * 3)
  {
    error_str = "config register data length error, payload size must be equal to " + std::to_string(reg_num * 8 + 4) +
                ", payload actual size:" + std::to_string(_payload.size());
    return false;
  }

  for (size_t i = 0; i < curr_frame_reg_num; ++i)
  {
    uint32_t reg_addr = 0;
    uint32_t reg_val  = 0;
    ProtoParser::copyFromPayload(_payload, reg_addr, index);
    ProtoParser::copyFromPayload(_payload, reg_val, index);
    register_addr_queue.emplace(reg_addr);
    register_value_queue.emplace(reg_val);
  }

  if (register_addr_queue.size() == reg_num)
  {
    is_config_register_read_finish = true;
  }

  return true;
}

bool MechBaseParser::extractConfigPara(const std::vector<uint8_t>& _payload)
{
  if (_payload.size() != sizeof(mech::ConfigPara))
  {
    return false;
  }
  mech::ConfigPara config_para {};
  std::memcpy(&config_para, _payload.data(), sizeof(mech::ConfigPara));
  universal_queue.emplace(_payload.begin(), _payload.end());
  return true;
}

bool MechBaseParser::extractPSDefined(const std::vector<uint8_t>& _payload)
{
  if (_payload.size() != 5)
  {
    error_str =
      "ps defined data length error, payload size must be 5, payload actual size:" + std::to_string(_payload.size());
    return false;
  }
  universal_queue.emplace(_payload.begin(), _payload.end());
  return true;
}

bool MechBaseParser::extractData(const uint32_t _response_code, const std::vector<uint8_t>& _payload)
{
  switch (_response_code)
  {
  case mech::NET_CMD_TOP_BOARD_MULTI_READ_REGISTER:
  case mech::NET_CMD_BOTTOM_BOARD_MULTI_READ_REGISTER:
  {
    return extractMultiRegister(_payload);
  }
  case mech::NET_CMD_TOP_BOARD_CON_READ_REGISTER:
  case mech::NET_CMD_BOTTOM_BOARD_CON_READ_REGISTER:
  {
    return extractConRegister(_payload);
  }
  case mech::NET_CMD_CONFIG_READ_ALL:
  {
    return extractConfigPara(_payload);
  }
  case mech::NET_CMD_PS_DEFINED:
  {
    return extractPSDefined(_payload);
  }

  case mech::NET_CMD_CONFIG_SET_NETWORK:
  case mech::NET_CMD_BOTTOM_BOARD_MULTI_WRITE_REGISTER:
  case mech::NET_CMD_BOTTOM_BOARD_CON_WRITE_REGISTER:
  {
    return true;
  }

  case mech::NET_CMD_OTHER_FIRM_459_READ:
  case mech::NET_CMD_OTHER_FIRM_BOT_READ:
  case mech::NET_CMD_OTHER_ENCOD_CALIB_READ:
  case mech::NET_CMD_OTHER_FIRM_TOP_READ:
  {
    return extractConfigRegister(_payload);
  }

  default:
    error_str = "unimplemented response code: " + ProtoParser::hex(_response_code);
    return extractNormalPacket(_payload);
  }
  return true;
}

}  // namespace lidar
}  // namespace robosense
