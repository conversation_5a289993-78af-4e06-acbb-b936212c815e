﻿/******************************************************************************
 * Copyright 2023 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#include "protocol/bpearl_parser.h"
#include "protocol/protocol_common.h"

namespace robosense  //NOLINT(modernize-concat-nested-namespaces)
{
namespace lidar
{

std::string BpearlParser::getCurrentProtocolType() { return "BPearl4.0"; }

bool BpearlParser::packStartupCheck(uint32_t& _expected_packet_response_code, std::vector<uint8_t>& _packet)
{
  return packReadRegister(std::vector<uint32_t>({ bpearl::LINK_STATUS_ADDR }), _expected_packet_response_code, _packet);
}

bool BpearlParser::isLidarStartUp(const uint32_t _value) { return (_value & 0x1UL) == 1; }

bool BpearlParser::isValidMsop(const char* _packet)
{
  const mech::MsopPacket* pkt_data = static_cast<const mech::MsopPacket*>(static_cast<const void*>(_packet));

  return pkt_data->frame_flag == bpearl::MSOP_FRAME_FLAG;
}

}  // namespace lidar
}  // namespace robosense
