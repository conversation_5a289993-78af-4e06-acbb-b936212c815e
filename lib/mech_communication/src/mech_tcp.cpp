/******************************************************************************
 * Copyright 2023 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#include "mech_tcp.h"
#include "protocol/airy_crc32_parser.h"
#include "protocol/airy_parser.h"
#include "protocol/bpearl_parser.h"
#include "protocol/helios_parser.h"
#include "protocol/mech_base_parser.h"
#include "protocol/protocol_common.h"
#include "protocol/protocol_parser.h"
#include "protocol/ruby_old_parser.h"
#include "register/register_airy.h"
#include "rsfsc_log/rsfsc_log.h"
#include "rsfsc_log/rsfsc_log_macro.h"
#include <boost/asio/deadline_timer.hpp>
#include <boost/asio/use_future.hpp>
#include <boost/bind/bind.hpp>
#include <boost/exception/all.hpp>
#include <boost/system/error_code.hpp>
#include <boost/system/system_error.hpp>
#include <cstdio>  // for popen, pclose, _popen, _pclose
#include <cstdlib>
#include <cstring>

namespace robosense  //NOLINT(modernize-concat-nested-namespaces)
{
namespace lidar
{

MechTcp::MechTcp(ProtocolType _protocol_type, const int _index) : log_index_(_index)
{
  init(_protocol_type);
  ptr_io_service_ = std::make_unique<boost::asio::io_service>();
  ptr_tcp_client_ = std::make_unique<boost::asio::ip::tcp::socket>(*ptr_io_service_);
}

void MechTcp::setLogIndex(const int _index) { log_index_ = _index; }
int MechTcp::getLogIndex() const { return log_index_; }

void MechTcp::init(ProtocolType _protocol_type)
{
  // setLocalHostAddress("*************");

  switch (_protocol_type)
  {
  case ProtocolType::MECH_HELIOS:
  {
    ptr_proto_parser_ = std::make_shared<HeliosParser>();
    break;
  }
  case ProtocolType::MECH_RUBY:
  {
    ptr_proto_parser_ = std::make_shared<MechBaseParser>();
    break;
  }
  case ProtocolType::MECH_RUBY_OLD:
  {
    ptr_proto_parser_ = std::make_shared<RubyOldParser>();
    break;
  }
  case ProtocolType::MECH_BPEARL:
  {
    ptr_proto_parser_ = std::make_shared<BpearlParser>();
    break;
  }
  case ProtocolType::MECH_AIRY:
  {
    ptr_proto_parser_ = std::make_shared<AiryParser>();
    break;
  }
  case ProtocolType::MECH_AIRY_CRC32:
  {
    ptr_proto_parser_ = std::make_shared<AiryCrc32Parser>();
    break;
  }
  default:
    LOG_INDEX_ERROR(msg_header_ + "unsupported protocol type!");
    ptr_proto_parser_ = std::make_shared<HeliosParser>();
    break;
  }
  LOG_INDEX_DEBUG("MechTcp::MechTcp -> protocol parser {0} constructed", ptr_proto_parser_->getCurrentProtocolType());
}

MechTcp::~MechTcp()
{
  LOG_INDEX_DEBUG("MechTcp::~MechTcp -> destructed");
  disconnect();

  ptr_tcp_client_.reset();
  ptr_io_service_.reset();
}

std::string MechTcp::getCurrentProtocolType()
{
  if (ptr_proto_parser_ == nullptr)
  {
    return "Unknown";
  }
  return ptr_proto_parser_->getCurrentProtocolType();
}

bool MechTcp::wait(const uint32_t _msec)
{
  std::unique_lock<std::mutex> lock(mutex_);
  cv_.wait_for(lock, std::chrono::milliseconds(_msec), [&] { return is_abort_.load(); });

  return !is_abort_.load();
}

bool MechTcp::ping(const std::string& _ip, const std::string& _local_address)
{
  if (_ip.empty())
  {
    return false;
  }

#if defined(_WIN32)
  std::string cmd = "ping " + _ip + " -n 1 -l 1 -w 100 > nul ";
  FILE* pipe      = _popen(cmd.c_str(), "r");
#else
  std::string cmd = "ping -c 1 -s 1 -W 0.1 -I " + _local_address + " " + _ip + " > /dev/null 2>&1";
  FILE* pipe      = popen(cmd.c_str(), "r");
#endif

  if (pipe == nullptr)
  {
    return false;
  }

#if defined(_WIN32)
  int ret_code = _pclose(pipe);
  // 在 Windows 上，_pclose 返回的代码已经是子进程的退出代码，因此可以直接使用
  return ret_code == 0;
#else
  int ret_code    = pclose(pipe);
  // 在 Unix 系统上，pclose 返回的值是子进程的退出状态，需要使用 WEXITSTATUS 宏来提取退出码
  return ret_code != -1 && WEXITSTATUS(ret_code) == 0;
#endif
}

bool MechTcp::pingWait(const std::string& _ip, const std::string& _local_ip, const uint32_t _msec)
{
  // 如果ping失败，且时间超过_msec则return false，超时
  uint32_t time     = 0;
  uint32_t interval = 2000;
  while ((time < _msec) && !is_abort_)
  {
    if (ping(_ip, _local_ip))
    {
      return true;
    }
    LOG_INDEX_DEBUG("ping {} failed, wait {}ms, {}/{}s", _ip, interval, time / interval, _msec / interval);
    std::this_thread::sleep_for(std::chrono::milliseconds(interval));
    time += interval;
  }
  return false;
}

bool MechTcp::readConfigRegister(const uint32_t _cmd_type,
                                 // NOLINTNEXTLINE(bugprone-easily-swappable-parameters)
                                 std::vector<uint32_t>& _reg_addr,
                                 std::vector<uint32_t>& _reg_val,
                                 const uint32_t _msec)
{
  auto curr_time                                    = std::chrono::steady_clock::now();
  std::vector<uint8_t> packet                       = { 0, 0 };
  ptr_proto_parser_->is_config_register_read_finish = false;
  std::queue<uint32_t>().swap(ptr_proto_parser_->register_addr_queue);
  std::queue<uint32_t>().swap(ptr_proto_parser_->register_value_queue);

  if (!writeCmd(_cmd_type, packet))
  {
    if (ptr_proto_parser_->error_str != "response type error: 0x3，error type: 数据长度错误")
    {
      return false;
    }
  }

  while (std::chrono::steady_clock::now() - curr_time < std::chrono::milliseconds(_msec))
  {
    if (ptr_proto_parser_->is_config_register_read_finish)
    {
      while (!ptr_proto_parser_->register_addr_queue.empty())
      {
        _reg_addr.push_back(ptr_proto_parser_->register_addr_queue.front());
        ptr_proto_parser_->register_addr_queue.pop();
      }
      while (!ptr_proto_parser_->register_value_queue.empty())
      {
        _reg_val.push_back(ptr_proto_parser_->register_value_queue.front());
        ptr_proto_parser_->register_value_queue.pop();
      }
      return true;
    }
    std::this_thread::sleep_for(std::chrono::milliseconds(50));
  }

  LOG_INDEX_ERROR("time out, read config register failed, curr register queue size: {0}",
                  ptr_proto_parser_->register_addr_queue.size());
  return false;
}

bool MechTcp::setLocalHostAddress(const std::string& _local_host_address)
{

  boost::asio::ip::tcp::endpoint endpoint(boost::asio::ip::make_address(_local_host_address),
                                          0);  // 使用 IP 地址 ************* 和任意端口
  try
  {
    // 绑定 socket 到特定的本地地址
    ptr_tcp_client_->open(boost::asio::ip::tcp::v4());  // 确保使用正确的协议版本
    ptr_tcp_client_->bind(endpoint);
  }
  catch (const std::exception& err)
  {
    LOG_INDEX_ERROR("绑定本地ip失败: " + _local_host_address + ", error string: " + err.what());
    return false;
  }
  return true;
}

std::string MechTcp::getLocalHostAddress() const { return local_host_address_; }

bool MechTcp::isValidMsop(const char* _packet) { return ptr_proto_parser_->isValidMsop(_packet); }

bool MechTcp::setIP(const std::string& _ip)
{
  boost::system::error_code error_code;
  boost::asio::ip::address::from_string(_ip, error_code);
  if (error_code)
  {
    LOG_INDEX_ERROR("invalid ip address: " + _ip + ", err msg: " + error_code.message());
    return false;
  }
  ip_ = _ip;
  return true;
}

std::string MechTcp::getIP() const { return ip_; }

bool MechTcp::setPort(const uint16_t _port)
{
  port_ = _port;
  return true;
}

uint16_t MechTcp::getPort() const { return port_; }

bool MechTcp::isConnected() const
{
  if (ptr_tcp_client_ == nullptr)
  {
    LOG_INDEX_ERROR("connect error, ptr_socket is null");
    return false;
  }
  boost::system::error_code err_code;
  ptr_tcp_client_->remote_endpoint(err_code);

  return is_connect_success_ && !err_code;
}

bool MechTcp::connect(const uint32_t _msec) { return connect(ip_, port_, _msec); }

// NOLINTNEXTLINE(bugprone-easily-swappable-parameters)
bool MechTcp::connect(const std::string& _ip, const uint16_t _msop_port, const uint32_t _msec)
{
  if (is_connect_success_ || ptr_tcp_client_->is_open())
  {
    disconnect();
  }

  resetAbort();
  is_connect_success_ = false;
  if (!pingWait(_ip, local_host_address_, _msec))
  {
    LOG_INDEX_ERROR("ping failed, ip: " + _ip);
    return false;
  }

  msg_header_ = fmt::format("MechTcp [{}:{}] :: ", _ip, _msop_port);
  LOG_INDEX_INFO(msg_header_ + "ping success");

  startIOThread();

  boost::asio::ip::tcp::endpoint end_point(boost::asio::ip::address_v4::from_string(_ip), _msop_port);

  // 利用 promise/future 机制获取异步连接结果
  std::promise<boost::system::error_code> connect_promise;
  auto connect_future = connect_promise.get_future();
  boost::asio::steady_timer timer(*ptr_io_service_);
  bool timed_out = false;
  timer.expires_after(std::chrono::milliseconds(_msec));

  timer.async_wait([this, &timed_out](const boost::system::error_code& _ec) {
    if (!_ec)
    {
      timed_out = true;
      ptr_tcp_client_->cancel();
    }
  });

  // 发起异步连接操作
  ptr_tcp_client_->async_connect(
    end_point, [&connect_promise](const boost::system::error_code& _ec) { connect_promise.set_value(_ec); });

  // 等待连接结果
  auto err_code = connect_future.get();
  timer.cancel();

  if (timed_out)
  {
    LOG_INDEX_ERROR(msg_header_ + "连接超时，等待时间：{0}s", static_cast<float>(_msec) / 1000.F);
    disconnect();
    return false;
  }

  if (err_code)
  {
    LOG_INDEX_ERROR(msg_header_ + "连接失败，错误信息：" + err_code.message());
    disconnect();
    return false;
  }

  setClientOptions();
  startAsyncRead();

  is_connect_success_ = true;

  mech::ConfigPara config_para {};
  if (!readConfigParamater(config_para))
  {
    LOG_INDEX_ERROR("读取config para失败，重试连接");
    disconnect();
    return false;
  }

  LOG_INDEX_DEBUG(msg_header_ + "连接成功");

  ip_   = _ip;
  port_ = _msop_port;
  return is_connect_success_;
}

void MechTcp::setExpectedResponse(const uint32_t _expected_packet_response_code)
{
  std::lock_guard<std::mutex> locker(response_mutex_);
  expected_response_[_expected_packet_response_code] = false;
}

bool MechTcp::containsExpectedResponse(const uint32_t _expected_packet_response_code)
{
  return (expected_response_.find(_expected_packet_response_code) == expected_response_.end());
}

void MechTcp::eraseExpectedResponse(const uint32_t _expected_packet_response_code)
{
  std::lock_guard<std::mutex> locker(response_mutex_);
  expected_response_.erase(_expected_packet_response_code);
}

void MechTcp::notifyExpectedResponse(const uint32_t _expected_packet_response_code)
{
  std::lock_guard<std::mutex> locker(response_mutex_);
  if (expected_response_.find(_expected_packet_response_code) == expected_response_.end())
  {
    LOG_INDEX_ERROR(msg_header_ +
                    "notifyExpectedResponse not found: " + ProtoParser::hex(_expected_packet_response_code));
    cv_response_.notify_all();
    return;
  }
  expected_response_[_expected_packet_response_code] = true;
  cv_response_.notify_all();
}

bool MechTcp::setClientOptions()
{
  //socket setting
  ptr_tcp_client_->set_option(boost::asio::ip::tcp::socket::keep_alive(true));
  ptr_tcp_client_->set_option(boost::asio::ip::tcp::socket::send_buffer_size(61440));
  ptr_tcp_client_->set_option(boost::asio::ip::tcp::socket::receive_buffer_size(61440));
  ptr_tcp_client_->set_option(boost::asio::ip::tcp::no_delay(true));

  return true;
}

bool MechTcp::disconnect()
{
  {
    std::lock_guard<std::mutex> locker(tcp_mutex_);
    LOG_INDEX_DEBUG(msg_header_ + "call disconnect");
    is_abort_.store(true);
    cv_.notify_all();
    cv_response_.notify_all();
  }

  if (is_connect_success_)
  {
    boost::system::error_code error_code;
    // 尝试取消操作
    ptr_tcp_client_->cancel(error_code);
    if (error_code)
    {
      LOG_INDEX_ERROR(msg_header_ + " -> cancel failed: " + error_code.message());
    }
    // 执行 shutdown 操作，并传递 error_code 来捕获错误
    ptr_tcp_client_->shutdown(boost::asio::ip::tcp::socket::shutdown_both, error_code);
    if (error_code)
    {
      LOG_INDEX_ERROR(msg_header_ + " -> shutdown failed: " + error_code.message());
    }
    ptr_tcp_client_->close(error_code);
    if (error_code)
    {
      LOG_INDEX_ERROR(msg_header_ + " -> close failed: " + error_code.message());
    }
  }

  std::unique_lock<std::mutex> lock(mutex_);
  ptr_work_guard_.reset();
  if (io_thread_.joinable())
  {
    io_thread_.join();
  }
  is_connect_success_ = false;

  return true;
}

void MechTcp::abort()
{
  is_abort_.store(true);
  cv_.notify_all();
  cv_response_.notify_all();
}

void MechTcp::resetAbort() { is_abort_.store(false); }

void MechTcp::startIOThread()
{
  if (io_thread_.joinable())
  {
    return;
  }
  ptr_work_guard_ = std::make_unique<boost::asio::executor_work_guard<boost::asio::io_context::executor_type>>(
    ptr_io_service_->get_executor());

  io_thread_ = std::thread([this]() {
    LOG_INDEX_DEBUG(msg_header_ + "start io_service");
    this->ptr_io_service_->restart();
    this->ptr_io_service_->run();
    LOG_INDEX_DEBUG(msg_header_ + "stop io_service");
  });
}

void MechTcp::startAsyncRead()
{
  LOG_INDEX_TRACE(msg_header_ + "call startAsyncRead");
  ptr_tcp_client_->async_read_some(boost::asio::buffer(recv_buffer_),
                                   [this](const boost::system::error_code& _error, std::size_t _bytes_transferred) {
                                     handleRead(_error, _bytes_transferred);
                                   });
}

int MechTcp::write(const std::vector<uint8_t>& _data, const uint32_t _msec)
{
  std::chrono::milliseconds span(_msec);
  std::future<size_t> write_future =
    ptr_tcp_client_->async_write_some(boost::asio::buffer(_data), boost::asio::use_future);

  if (write_future.wait_for(span) == std::future_status::timeout)
  {
    LOG_INDEX_ERROR(msg_header_ + "write timeout: " + std::to_string(_msec) + "ms");
    ptr_tcp_client_->cancel();
    return -1;
  }
  try
  {
    size_t bytes_written = write_future.get();
    return static_cast<int>(bytes_written);
  }
  catch (const boost::system::system_error& error_code)
  {
    handleWriteError(error_code.code(), _msec);
    return -1;
  }
}

void MechTcp::writeSegmented(const std::vector<uint8_t>& _data)
{
  const size_t MAX_SEGMENT_SIZE = 1448;
  const uint32_t DELAY_MS       = 2;

  size_t data_size = _data.size();
  size_t processed = 0;

  while (processed < data_size)
  {
    // 计算当前段的大小
    size_t segment_size = std::min(MAX_SEGMENT_SIZE, data_size - processed);

    // 创建当前段
    std::vector<uint8_t> segment(
      _data.begin() + static_cast<std::vector<uint8_t>::difference_type>(processed),
      _data.begin() + static_cast<std::vector<uint8_t>::difference_type>(processed) + segment_size);

    // 发送当前段
    write(segment);

    // 如果还有数据要发送，则等待指定的延迟时间
    if (processed < data_size)
    {
      boost::asio::steady_timer timer(*ptr_io_service_, boost::asio::chrono::milliseconds(DELAY_MS));
      timer.wait();
    }
    // 更新处理进度
    processed += segment_size;
  }
}

void MechTcp::handleRead(const boost::system::error_code& _error, std::size_t _bytes_transferred)
{
  LOG_INDEX_TRACE(msg_header_ + "call handleRead");
  if (_error)
  {
    handleReadError(_error);
    return;
  }

  // 成功接收数据
  receive_buffer_.insert(receive_buffer_.end(), recv_buffer_.begin(), recv_buffer_.begin() + _bytes_transferred);
  LOG_INDEX_DEBUG(msg_header_ + "receive data: " + ProtoParser::hex(receive_buffer_));

  // 解析接收到的数据
  parser_result_ = ptr_proto_parser_->parseClientPacket(receive_buffer_);
  if (!parser_result_)
  {
    handleParseError();
  }

  handleParsedPayloads();

  // 继续异步读取
  startAsyncRead();
}

void MechTcp::handleReadError(const boost::system::error_code& _error)
{
  // 处理错误和断开连接的情况
  if (_error == boost::asio::error::operation_aborted)
  {
    LOG_INDEX_DEBUG(msg_header_ +
                    "tcp client receive abort, msg: " + std::string(boost::system::system_error(_error).what()));
  }
  else if (_error == boost::asio::error::eof)
  {
    LOG_INDEX_DEBUG(msg_header_ + "lidar-tcp normal disconnect from host! err_msg: " +
                    std::string(boost::system::system_error(_error).what()));
    // abort();
  }
  else
  {
    LOG_INDEX_ERROR(msg_header_ + "lidar-tcp abnormal disconnect from host! err_msg: " +
                    std::string(boost::system::system_error(_error).what()));
    // abort();
  }
}

void MechTcp::handleWriteError(const boost::system::error_code& _error, const uint32_t _msec)
{
  // 处理错误和断开连接的情况
  if (_error == boost::asio::error::operation_aborted)
  {
    LOG_INDEX_INFO(msg_header_ + "tcp client write time out : " + std::to_string(_msec) + "ms");
  }
  else if (_error != boost::asio::error::eof)
  {
    LOG_INDEX_ERROR(msg_header_ + "lidar-tcp normal disconnect from host! err_msg: " +
                    std::string(boost::system::system_error(_error).what()));
  }
  else
  {
    LOG_INDEX_ERROR(msg_header_ + "lidar-tcp abnormal disconnect from host! err_msg: " +
                    std::string(boost::system::system_error(_error).what()));
  }
}

void MechTcp::handleDisconnect(const std::string& _message)
{
  LOG_INDEX_DEBUG(msg_header_ + _message);
  is_connect_success_ = false;
}

void MechTcp::handleParseError() { LOG_INDEX_ERROR(msg_header_ + ptr_proto_parser_->error_str); }

void MechTcp::handleParsedPayloads()
{
  std::lock_guard<std::mutex> lock(ptr_proto_parser_->queue_mutex);
  while (!ptr_proto_parser_->payload_queue.empty())
  {
    auto payload_pair = ptr_proto_parser_->payload_queue.front();
    ptr_proto_parser_->payload_queue.pop();

    auto response_code = payload_pair.first;
    auto payload       = payload_pair.second;

    if (!ptr_proto_parser_->extractData(response_code, payload))
    {
      LOG_INDEX_ERROR("{} extract data failed, response_code: {:#x}, parser err: {}", msg_header_, response_code,
                      ptr_proto_parser_->error_str);
    }

    // notify expected response code
    notifyExpectedResponse(response_code);
  }
}

bool MechTcp::checkRegisterAddress(const std::vector<uint32_t>& _reg_addr,
                                   const std::queue<uint32_t>& _register_addr_queue)
{
  if (_register_addr_queue.empty())
  {
    // ignore check
    return true;
  }

  if (_reg_addr.size() != _register_addr_queue.size())
  {
    LOG_INDEX_ERROR(msg_header_ + "return size error! return: " + std::to_string(_register_addr_queue.size()) +
                    ", expect: " + std::to_string(_reg_addr.size()));
    return false;
  }

  return true;
}

void MechTcp::slotDisconnected()
{
  is_connect_success_ = false;
  LOG_INDEX_DEBUG(msg_header_ + "disconnect from host!");
}

bool MechTcp::waitForStartupComplete(const uint32_t _msec)
{
  auto start = std::chrono::steady_clock::now();  // Start timing

  bool is_startup_complete = isStartupComplete();
  while (!is_abort_.load() && !is_startup_complete)
  {
    auto elapsed =
      std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::steady_clock::now() - start).count();
    if (elapsed >= _msec)
    {
      break;
    }

    if (!wait(2000))
    {
      return false;
    }

    is_startup_complete = isStartupComplete();
  }

  if (!is_startup_complete)
  {
    LOG_INDEX_ERROR(msg_header_ + "startup timeout " + std::to_string(_msec) + "ms");
  }

  return is_startup_complete;
}

bool MechTcp::isStartupComplete()
{
  uint32_t reg_val = 0;

  std::vector<uint8_t> packet;
  uint32_t expected_packet_response_code = 0;

  if (!ptr_proto_parser_->packStartupCheck(expected_packet_response_code, packet))
  {
    LOG_INDEX_ERROR(msg_header_ + "pack startup check failed");
    return false;
  }

  if (!writeWaitResponse(packet, expected_packet_response_code, DEFAULT_TIMEOUT))
  {
    return false;
  }

  reg_val = ptr_proto_parser_->register_value_queue.front();
  ptr_proto_parser_->register_value_queue.pop();

  LOG_INDEX_DEBUG(msg_header_ + "startup check reg val: " + ProtoParser::hex(reg_val));
  return ptr_proto_parser_->isLidarStartUp(reg_val);
}

bool MechTcp::waitForTopStartUp(const uint32_t _msec)
{
  mech::ConfigPara config_para {};
  auto start = std::chrono::steady_clock::now();
  while (!is_abort_.load())
  {
    if (!readConfigParamater(config_para, 6000))
    {
      LOG_INDEX_ERROR(msg_header_ + "waitForTopStartUp 读取config参数失败");
      return false;
    }

    if (config_para.getPlVersion() != 0)
    {
      return true;
    }

    if (!wait(2000))
    {
      return false;
    }
    auto elapsed =
      std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::steady_clock::now() - start).count();
    if (elapsed >= _msec)
    {
      LOG_INDEX_ERROR(msg_header_ + "waitForTopStartUp timeout " + std::to_string(_msec) + "ms");
      return false;
    }
  }
  return false;
}

bool MechTcp::isPSDefinedReady(const uint32_t _msec)
{
  std::vector<uint8_t> packet;
  uint32_t expected_packet_response_code = 0;
  if (!ptr_proto_parser_->packPSDefinedReady(expected_packet_response_code, packet))
  {
    LOG_INDEX_ERROR(msg_header_ + "pack ps defined ready failed! err_msg: " + ptr_proto_parser_->error_str);
    return false;
  }

  if (!writeWaitResponse(packet, expected_packet_response_code, _msec))
  {
    return false;
  }

  if (ptr_proto_parser_->universal_queue.empty())
  {
    LOG_INDEX_ERROR(msg_header_ + "parse ps defined ready error! error_msg: " + ptr_proto_parser_->error_str);
    return false;
  }

  auto value_vec = ptr_proto_parser_->universal_queue.front();
  ptr_proto_parser_->universal_queue.pop();
  return ptr_proto_parser_->isPSDefinedReady(value_vec);
}

bool MechTcp::getIntensityCalibData(mech::IntensityData& _intensity_data, const uint32_t _msec)
{
  std::vector<uint8_t> packet;
  uint32_t expected_packet_response_code = 0;

  if (!ptr_proto_parser_->packReadIntensity(_intensity_data, expected_packet_response_code, packet))
  {
    LOG_INDEX_ERROR(msg_header_ + "pack read intensity failed");
    return false;
  }

  if (!writeWaitResponse(packet, expected_packet_response_code, _msec))
  {
    return false;
  }

  auto payload = ptr_proto_parser_->universal_queue.front();
  ptr_proto_parser_->universal_queue.pop();
  std::memcpy(&_intensity_data, payload.data(), payload.size());

  return true;
}

bool MechTcp::writeChnAngle(const std::vector<float>& _ver_angle_vec,
                            const std::vector<float>& _hor_angle_vec,
                            const uint32_t _msec)
{
  uint16_t angle_num = static_cast<uint16_t>(_ver_angle_vec.size());
  std::vector<uint8_t> data(sizeof(uint16_t) + (angle_num * (sizeof(float) * 2 + sizeof(uint16_t))));
  size_t index = 0;
  ProtoParser::copyToPayload(data, angle_num, index);
  for (size_t i = 0; i < angle_num; ++i)
  {
    ProtoParser::copyToPayload(data, static_cast<uint16_t>(i + 1), index);
    ProtoParser::copyToPayload(data, _ver_angle_vec[i], index);
    ProtoParser::copyToPayload(data, _hor_angle_vec[i], index);
  }
  return writeCmd(mech::NET_CMD_OTHER_CHANNEL_ANGLE_WRITE, data, _msec);
}

bool MechTcp::readChnAngle(std::vector<float>& _ver_angle_vec,
                           std::vector<float>& _hor_angle_vec,
                           const uint16_t& _angle_num,
                           const uint32_t _msec)
{
  std::vector<uint8_t> data(sizeof(uint16_t));
  size_t index = 0;
  ProtoParser::copyToPayload(data, _angle_num, index);
  if (!readCmd(mech::NET_CMD_OTHER_CHANNEL_ANGLE_READ, data, _msec))
  {
    LOG_INDEX_ERROR("读取通道角度失败");
    return false;
  }
  if (data.size() != _angle_num * (sizeof(float) * 2 + sizeof(uint16_t)))
  {
    LOG_INDEX_ERROR("通道角度数据长度错误, data size: {}", data.size());
    return false;
  }
  index                  = 0;
  uint16_t chn_num       = 0;
  float angle_vertical   = 0;
  float angle_horizontal = 0;
  std::vector<float> ver_angle_vec;
  std::vector<float> hor_angle_vec;
  for (size_t i = 0; i < _angle_num; ++i)
  {
    ProtoParser::copyFromPayload(data, chn_num, index);
    ProtoParser::copyFromPayload(data, angle_vertical, index);
    ProtoParser::copyFromPayload(data, angle_horizontal, index);
    ver_angle_vec.push_back(angle_vertical);
    hor_angle_vec.push_back(angle_horizontal);
  }
  _ver_angle_vec = ver_angle_vec;
  _hor_angle_vec = hor_angle_vec;
  return true;
}

bool MechTcp::writeLidarNet(const std::string& _ip,
                            const uint16_t _msop_port,   // NOLINT(bugprone-easily-swappable-parameters)
                            const uint16_t _difop_port,  // NOLINT(bugprone-easily-swappable-parameters)
                            const uint32_t _msec)
{
  boost::system::error_code error_code;
  boost::asio::ip::address_v4 address = boost::asio::ip::make_address_v4(_ip, error_code);
  uint32_t ip_addr                    = address.to_uint();

  if (error_code)
  {
    LOG_INDEX_ERROR("invalid ip address: {}", _ip);
  }

  // C0A8 means 192.168.x.x
  if ((ip_addr >> 16U) != 0xC0A8U)
  {
    LOG_INDEX_ERROR(msg_header_ + "only support 192.168.x.x , not support: " + _ip);
    return false;
  }

  // transfer to big endian
  ip_addr = htonl(ip_addr);

  std::vector<uint8_t> packet;
  uint32_t expected_packet_response_code = 0;

  if (!ptr_proto_parser_->packReadConfigPara(expected_packet_response_code, packet))
  {
    LOG_INDEX_ERROR(msg_header_ + "pack read config failed!");
    return false;
  }

  if (!writeWaitResponse(packet, expected_packet_response_code, _msec))
  {
    return false;
  }

  if (ptr_proto_parser_->universal_queue.empty())
  {
    LOG_INDEX_ERROR(msg_header_ + "parse config error");
    return false;
  }

  packet = ptr_proto_parser_->universal_queue.front();
  ptr_proto_parser_->universal_queue.pop();

  if (!ptr_proto_parser_->packWriteIpPort(ip_addr, _msop_port, _difop_port, expected_packet_response_code, packet))
  {
    LOG_INDEX_ERROR(msg_header_ + "pack net para failed!");
    return false;
  }

  if (auto ptr = std::dynamic_pointer_cast<HeliosParser>(ptr_proto_parser_))
  {
    if (!writeWaitResponse(packet, expected_packet_response_code, _msec))
    {
      return false;
    }
  }
  else
  {
    if (!writeWaitResponse(packet, expected_packet_response_code, _msec))
    {
      if (!wait(1000))
      {
        return false;
      }

      disconnect();

      return connect(_ip, _msop_port, 5000);
    }
  }
  return true;
}

void MechTcp::handleException()
{
  try
  {
    throw;  // rethrow the current exception
  }
  catch (const boost::exception& ex)
  {
    // handle Boost exception
    LOG_INDEX_ERROR(msg_header_ +
                    " sendData write data failed with boost::exception, ex msg: " + boost::diagnostic_information(ex));
  }
  catch (const boost::system::system_error& ex)
  {
    // handle Boost system error
    LOG_INDEX_ERROR(msg_header_ +
                    " write data failed with boost::system::system_error, ex msg: " + std::string(ex.what()));
  }
  catch (const std::exception& ex)
  {
    // handle standard exception
    LOG_INDEX_ERROR(msg_header_ + " write data failed with std::exception, ex msg: " + std::string(ex.what()));
  }
  catch (...)
  {
    // handle unknown exception
    LOG_INDEX_ERROR(msg_header_ + " write data failed with unknown exception");
  }
}

bool MechTcp::writeWaitResponse(const std::vector<uint8_t>& _packet,
                                // NOLINTNEXTLINE(bugprone-easily-swappable-parameters)
                                const uint32_t _expected_packet_response_code,
                                const uint32_t _msec)
{
  if (is_abort_.load())
  {
    LOG_INDEX_ERROR(msg_header_ + "lidar is abort!");
    return false;
  }

  std::lock_guard<std::mutex> locker(tcp_mutex_);
  ptr_proto_parser_->clear();

  if (!isConnected())
  {
    LOG_INDEX_ERROR(msg_header_ + "lidar is not connected!");
    // connect(ip_, port_);
    return false;
  }

  is_response_ = false;
  setExpectedResponse(_expected_packet_response_code);

  LOG_INDEX_DEBUG(msg_header_ + "write data: " + ProtoParser::hex(_packet));
  try
  {
    if (_expected_packet_response_code == helios::NET_CMD_ACK_TOP_GET_INTENSITY)
    {
      writeSegmented(_packet);
    }
    else
    {
      write(_packet, _msec);
    }
  }
  catch (...)
  {
    handleException();
    return false;
  }

  bool response_success = true;
  std::unique_lock<std::mutex> lock(response_mutex_);
  if (!cv_response_.wait_for(lock, std::chrono::milliseconds(_msec),
                             [&] { return expected_response_[_expected_packet_response_code] || is_abort_; }))
  {
    ptr_proto_parser_->clear();
    response_success = false;
    LOG_INDEX_ERROR(msg_header_ + "response timeout, response_code: 0x" +
                    ProtoParser::hex(_expected_packet_response_code) + ", timeout: " + std::to_string(_msec));
  }

  return response_success && parser_result_ && !is_abort_;
}
// NOLINTNEXTLINE(bugprone-easily-swappable-parameters)
template bool MechTcp::writeCmd<uint8_t>(const uint32_t _cmd_type, const uint8_t _value, const uint32_t _msec);
template bool MechTcp::writeCmd<uint16_t>(const uint32_t _cmd_type, const uint16_t _value, const uint32_t _msec);
template bool MechTcp::writeCmd<float>(const uint32_t _cmd_type, const float _value, const uint32_t _msec);
template bool MechTcp::writeCmd<uint32_t>(const uint32_t _cmd_type, const uint32_t _value, const uint32_t _msec);
template bool MechTcp::writeCmd<int>(const uint32_t _cmd_type, const int _value, const uint32_t _msec);
template <typename T>
bool MechTcp::writeCmd(const uint32_t _cmd_type, const T _value, const uint32_t _msec)
{
  std::vector<uint8_t> data(sizeof(T));
  std::memcpy(data.data(), &_value, sizeof(T));
  return writeCmd(_cmd_type, data, _msec);
}
bool MechTcp::writeCmd(const uint32_t _cmd_type, const std::vector<uint8_t>& _data, const uint32_t _msec)
{
  std::vector<uint8_t> packet;
  uint32_t expected_packet_response_code = 0;

  if (!ptr_proto_parser_->packWriteCmd(_cmd_type, _data, expected_packet_response_code, packet))
  {
    LOG_INDEX_ERROR(msg_header_ + "pack write cmd failed!");
    return false;
  }

  if (expected_packet_response_code >= mech::NET_CMD_FIRMWARE_UPDATE_APP &&
      expected_packet_response_code <= mech::NET_CMD_FIRMWARE_UPDATE_TOP)
  {
    expected_packet_response_code = mech::NET_CMD_FIRMWARE_UPDATE_ACK;
  }

  if (!writeWaitResponse(packet, expected_packet_response_code, _msec))
  {
    return false;
  }
  return parser_result_;
}

template bool MechTcp::readCmd<uint8_t>(const uint32_t _cmd_type, uint8_t& _value, const uint32_t _msec);
template bool MechTcp::readCmd<uint16_t>(const uint32_t _cmd_type, uint16_t& _value, const uint32_t _msec);
template bool MechTcp::readCmd<float>(const uint32_t _cmd_type, float& _value, const uint32_t _msec);
template bool MechTcp::readCmd<uint32_t>(const uint32_t _cmd_type, uint32_t& _value, const uint32_t _msec);
template bool MechTcp::readCmd<int>(const uint32_t _cmd_type, int& _value, const uint32_t _msec);
template <typename T>
bool MechTcp::readCmd(const uint32_t _cmd_type, T& _value, const uint32_t _msec)
{
  std::vector<uint8_t> data;
  if (!readCmd(_cmd_type, data, _msec))
  {
    return false;
  }
  if (data.size() != sizeof(T))
  {
    LOG_INDEX_ERROR(msg_header_ + "read cmd failed, size error! expect: 2" +
                    ", return: " + ProtoParser::hex(static_cast<uint32_t>(data.size())));
    LOG_INDEX_ERROR(msg_header_ + " return data: " + ProtoParser::hex(data));
    return false;
  }
  std::memcpy(&_value, data.data(), sizeof(T));
  return true;
}
bool MechTcp::readCmd(const uint32_t _cmd_type, std::vector<uint8_t>& _data, const uint32_t _msec)
{
  std::vector<uint8_t> packet;
  uint32_t expected_packet_response_code = 0;

  if (!ptr_proto_parser_->packReadCmd(_cmd_type, _data, expected_packet_response_code, packet))
  {
    LOG_INDEX_ERROR(msg_header_ + "pack read cmd failed! err_msg: " + ptr_proto_parser_->error_str);
    return false;
  }

  if (!writeWaitResponse(packet, expected_packet_response_code, _msec))
  {
    return false;
  }

  {
    std::lock_guard<std::mutex> lock(ptr_proto_parser_->queue_mutex);
    if (ptr_proto_parser_->universal_queue.empty())
    {
      LOG_INDEX_ERROR(msg_header_ + "read cmd failed! err_msg: " + ptr_proto_parser_->error_str);
      return false;
    }
    _data = ptr_proto_parser_->universal_queue.front();
  }

  return parser_result_;
}

template bool MechTcp::readMonitorData<ruby::MonitorData>(ruby::MonitorData& _monitor_data, const uint32_t _msec);
template <typename T>
bool MechTcp::readMonitorData(T& _monitor_data, const uint32_t _msec)
{
  std::vector<uint8_t> data;
  if (!readCmd(mech::NET_CMD_OTHER_MONITOR_DATA, data, _msec))
  {
    return false;
  }

  if (data.size() != sizeof(T))
  {
    LOG_INDEX_ERROR(msg_header_ + "read monitor data failed, size error! expect: " + std::to_string(sizeof(T)) +
                    ", return: " + ProtoParser::hex(static_cast<uint32_t>(data.size())));
    LOG_INDEX_ERROR(msg_header_ + " return data: " + ProtoParser::hex(data));
    return false;
  }

  std::memcpy(&_monitor_data, data.data(), sizeof(T));

  return parser_result_;
}

bool MechTcp::writeRegData(const std::vector<uint32_t>& _reg_addr,
                           const std::vector<uint32_t>& _reg_val,
                           const uint32_t _msec)
{
  if (_reg_addr.empty())
  {
    LOG_INDEX_ERROR(msg_header_ + "_reg_addr cannot be empty");
    return false;
  }

  if (_reg_addr.at(0) < mech::MIN_TOP_REG_ADDR)
  {
    return write459RegData(_reg_addr, _reg_val, _msec);
  }

  if (_reg_addr.at(0) < mech::MIN_REG_ADDR)
  {
    return writeTopRegData(_reg_addr, _reg_val, _msec);
  }

  std::vector<uint8_t> packet;
  uint32_t expected_packet_response_code = 0;

  if (!ptr_proto_parser_->packWriteRegister(_reg_addr, _reg_val, expected_packet_response_code, packet))
  {
    LOG_INDEX_ERROR(msg_header_ + "pack write multi register failed!");
    return false;
  }

  if (!writeWaitResponse(packet, expected_packet_response_code, _msec))
  {
    return false;
  }

  return parser_result_;
}

bool MechTcp::writeRegData(const uint32_t _reg_start_addr, const std::vector<uint32_t>& _reg_val, const uint32_t _msec)
{
  if (_reg_start_addr < mech::MIN_TOP_REG_ADDR)
  {
    return write459RegData(_reg_start_addr, _reg_val, _msec);
  }
  if (_reg_start_addr < mech::MIN_REG_ADDR)
  {
    return writeTopRegData(_reg_start_addr, _reg_val, _msec);
  }
  std::vector<uint8_t> packet {};
  uint32_t expected_packet_response_code = 0;
  if (!ptr_proto_parser_->packWriteConRegData(_reg_start_addr, _reg_val, expected_packet_response_code, packet))
  {
    LOG_INDEX_ERROR(msg_header_ + "pack write register failed!");
    return false;
  }
  if (!writeWaitResponse(packet, expected_packet_response_code, _msec))
  {
    return false;
  }
  return true;
}

bool MechTcp::write459RegData(const uint32_t _reg_start_addr,
                              const std::vector<uint32_t>& _reg_val,
                              const uint32_t _msec)
{
  for (uint32_t i = 0; i < _reg_val.size(); i++)
  {
    if (!writeRegData(_reg_start_addr + i, _reg_val.at(i), _msec))
    {
      return false;
    }
  }
  return true;
}

bool MechTcp::writeRegData(const std::vector<RegisterData>& _reg_data_vec, const uint32_t _msec)
{
  if (_reg_data_vec.empty())
  {
    LOG_INDEX_ERROR(msg_header_ + "_reg_addr cannot be empty");
    return false;
  }

  if (_reg_data_vec.at(0).address < mech::MIN_REG_ADDR)
  {
    return writeTopRegData(_reg_data_vec, _msec);
  }

  std::vector<uint8_t> packet;
  uint32_t expected_packet_response_code = 0;

  if (!ptr_proto_parser_->packWriteRegister(_reg_data_vec, expected_packet_response_code, packet))
  {
    LOG_INDEX_ERROR(msg_header_ + "pack write multi register failed!");
    return false;
  }

  if (!writeWaitResponse(packet, expected_packet_response_code, _msec))
  {
    return false;
  }

  return parser_result_;
}

// NOLINTNEXTLINE(bugprone-easily-swappable-parameters)
bool MechTcp::writeRegData(const uint32_t _reg_addr, const uint32_t _reg_val, const uint32_t _msec)
{
  if (_reg_addr < mech::MIN_TOP_REG_ADDR)
  {
    return write459RegData(_reg_addr, _reg_val, _msec);
  }
  if (_reg_addr < mech::MIN_REG_ADDR)
  {
    return writeTopRegData(_reg_addr, _reg_val, _msec);
  }

  return writeRegData(_reg_addr, std::vector<uint32_t>({ _reg_val }), _msec);
}

bool MechTcp::write459RegData(const std::vector<uint32_t>& _reg_addr,
                              const std::vector<uint32_t>& _reg_val,
                              const uint32_t _msec)
{
  for (uint32_t i = 0; i < _reg_addr.size(); i++)
  {
    if (!writeRegData(_reg_addr.at(i), _reg_val.at(i), _msec))
    {
      return false;
    }
  }
  return true;
}

// NOLINTNEXTLINE(bugprone-easily-swappable-parameters)
bool MechTcp::writeTopRegData(const std::vector<uint32_t>& _reg_addr,
                              const std::vector<uint32_t>& _reg_val,
                              const uint32_t _msec)
{
  std::vector<uint8_t> packet;
  uint32_t expected_packet_response_code = 0;

  if (!ptr_proto_parser_->packWriteTopRegister(_reg_addr, _reg_val, expected_packet_response_code, packet))
  {
    std::string reg_str;
    for (const auto& addr : _reg_addr)
    {
      reg_str += ",0x" + ProtoParser::hex(addr);
    }
    LOG_INDEX_ERROR(msg_header_ + "pack write top register" + reg_str + " failed!");
    return false;
  }

  if (!writeWaitResponse(packet, expected_packet_response_code, _msec))
  {
    return false;
  }

  return parser_result_;
}
bool MechTcp::writeTopRegData(const uint32_t _reg_start_addr,
                              const std::vector<uint32_t>& _reg_val,
                              const uint32_t _msec)
{
  std::vector<uint8_t> packet {};
  uint32_t expected_packet_response_code = 0;
  if (!ptr_proto_parser_->packWriteConTopRegData(_reg_start_addr, _reg_val, expected_packet_response_code, packet))
  {
    LOG_INDEX_ERROR(msg_header_ + "pack write top register failed!");
    return false;
  }
  if (!writeWaitResponse(packet, expected_packet_response_code, _msec))
  {
    return false;
  }
  return true;
}

bool MechTcp::writeTopRegData(const std::vector<RegisterData>& _reg_data_vec, const uint32_t _msec)
{
  std::vector<uint8_t> packet;
  uint32_t expected_packet_response_code = 0;

  if (!ptr_proto_parser_->packWriteTopRegister(_reg_data_vec, expected_packet_response_code, packet))
  {
    std::string reg_str;
    for (const auto& reg_data : _reg_data_vec)
    {
      reg_str += ",0x" + ProtoParser::hex(reg_data.address);
    }
    LOG_INDEX_ERROR(msg_header_ + "pack write top register" + reg_str + " failed!");
    return false;
  }

  if (!writeWaitResponse(packet, expected_packet_response_code, _msec))
  {
    return false;
  }

  return parser_result_;
}

// NOLINTNEXTLINE(bugprone-easily-swappable-parameters)
bool MechTcp::writeTopRegData(const uint32_t _reg_addr, const uint32_t _reg_val, const uint32_t _msec)
{
  return writeTopRegData(std::vector<uint32_t>({ _reg_addr }), std::vector<uint32_t>({ _reg_val }), _msec);
}
// NOLINTNEXTLINE(bugprone-easily-swappable-parameters)
bool MechTcp::writeTopPairRegData(const uint32_t _reg_addr, const uint16_t _reg_value, const uint32_t _msec)
{
  std::vector<uint32_t> reg_val_vec(
    { static_cast<uint32_t>(_reg_value) >> 8U, static_cast<uint32_t>(_reg_value & 0xffU) });
  return writeTopRegData(_reg_addr, reg_val_vec, _msec);
}

// NOLINTNEXTLINE(bugprone-easily-swappable-parameters)
bool MechTcp::write459RegData(const uint32_t _reg_addr, const uint32_t _reg_value, const uint32_t _msec)
{
  if (!writeTopPairRegData(airy::REG_SENSOR_SPI_ADDR_0, static_cast<uint16_t>(_reg_addr), _msec))
  {
    return false;
  }
  if (!writeTopRegData(airy::REG_SENSOR_SPI_WDATA, _reg_value, _msec))
  {
    return false;
  }
  if (!writeTopRegData(airy::REG_SENSOR_SPI_CTRL, 0, _msec))
  {
    return false;
  }
  if (!writeTopRegData(airy::REG_SENSOR_SPI_CTRL, 1, _msec))
  {
    return false;
  }
  return true;
  // return writeTopRegData({ airy::REG_SENSOR_SPI_ADDR_0, airy::REG_SENSOR_SPI_ADDR_1, airy::REG_SENSOR_SPI_WDATA,
  //                         airy::REG_SENSOR_SPI_CTRL,airy::REG_SENSOR_SPI_CTRL},
  //                       { _reg_addr, _reg_addr, _reg_value, 0, 1 },
  //                       _msec);
}

bool MechTcp::readTopRegData(const std::vector<uint32_t>& _reg_addr,
                             std::vector<uint32_t>& _reg_val,
                             const uint32_t _msec)
{
  std::vector<uint8_t> packet;
  uint32_t expected_packet_response_code = 0;

  if (!ptr_proto_parser_->packReadTopRegister(_reg_addr, expected_packet_response_code, packet))
  {
    std::string reg_str;
    for (const auto& addr : _reg_addr)
    {
      reg_str += ",0x" + ProtoParser::hex(addr);
    }
    LOG_INDEX_ERROR(msg_header_ + "pack read register " + reg_str + " failed!");
    return false;
  }

  if (!writeWaitResponse(packet, expected_packet_response_code, _msec))
  {
    return false;
  }

  {
    std::lock_guard<std::mutex> lock(ptr_proto_parser_->queue_mutex);
    while (!ptr_proto_parser_->register_value_queue.empty())
    {
      _reg_val.emplace_back(ptr_proto_parser_->register_value_queue.front());
      ptr_proto_parser_->register_value_queue.pop();
    }
  }

  if (_reg_val.size() != _reg_addr.size())
  {
    LOG_INDEX_ERROR(msg_header_ + "return size error! error_str: " + ptr_proto_parser_->error_str);
    return false;
  }

  return true;
}

bool MechTcp::read459RegData(const std::vector<uint32_t>& _reg_addr,
                             std::vector<uint32_t>& _reg_val,
                             const uint32_t _msec)
{
  _reg_val.clear();
  _reg_val.resize(_reg_addr.size());
  for (uint32_t i = 0; i < _reg_addr.size(); i++)
  {
    if (!read459RegData(_reg_addr.at(i), _reg_val.at(i), _msec))
    {
      return false;
    }
  }
  return true;
}

bool MechTcp::readTopRegData(const uint32_t _reg_addr, uint32_t& _reg_val, const uint32_t _msec)
{
  std::vector<uint32_t> reg_val_vec;
  if (!readTopRegData(_reg_addr, 1, reg_val_vec, _msec))
  {
    return false;
  }

  _reg_val = reg_val_vec.front();
  return true;
}

bool MechTcp::readTopPairRegData(const uint32_t _reg_addr, uint16_t& _reg_val, const uint32_t _msec)
{
  std::vector<uint32_t> reg_val_vec;
  if (!readTopRegData(_reg_addr, 2, reg_val_vec, _msec))
  {
    return false;
  }

  _reg_val = (reg_val_vec.at(1) + (reg_val_vec.at(0) << 8U)) & 0xffffU;
  return true;
}

bool MechTcp::read459RegData(const uint32_t _reg_addr, uint32_t& _reg_val, const uint32_t _msec)
{
  if (!writeTopPairRegData(airy::REG_SENSOR_SPI_ADDR_0, static_cast<uint16_t>(_reg_addr), _msec))
  {
    return false;
  }
  if (!writeTopRegData(airy::REG_SENSOR_SPI_CTRL, 0, _msec))
  {
    return false;
  }
  if (!writeTopRegData(airy::REG_SENSOR_SPI_CTRL, 2, _msec))
  {
    return false;
  }

  uint32_t reg_val = 0;
  if (!readTopRegData(airy::REG_SENSOR_SPI_RDATA, reg_val, _msec))
  {
    return false;
  }
  _reg_val = reg_val;
  return true;
}

bool MechTcp::readMixRegData(const std::vector<uint32_t>& _reg_addr,
                             std::vector<uint32_t>& _reg_val,
                             const uint32_t _msec)
{
  for (const auto& addr : _reg_addr)
  {
    uint32_t val = 0;
    if (addr > mech::MIN_REG_ADDR && addr < mech::MAX_REG_ADDR)
    {
      if (!readRegData(addr, val, _msec))
      {
        LOG_INDEX_ERROR(msg_header_ + "read register failed!");
        return false;
      }
    }
    else if (addr > mech::MIN_TOP_REG_ADDR && addr < mech::MAX_TOP_REG_ADDR)
    {
      if (!readTopRegData(addr, val, _msec))
      {
        LOG_INDEX_ERROR(msg_header_ + "read register failed!");
        return false;
      }
    }

    if (!wait(10))
    {
      return false;
    }
    _reg_val.emplace_back(val);
  }
  return true;
}

bool MechTcp::readRegData(const std::vector<uint32_t>& _reg_addr, std::vector<uint32_t>& _reg_val, const uint32_t _msec)
{
  if (_reg_addr.empty())
  {
    LOG_INDEX_ERROR(msg_header_ + "readRegData _reg_addr cannot be empty");
    return false;
  }
  if (_reg_addr.at(0) < mech::MIN_REG_ADDR)
  {
    return read459RegData(_reg_addr, _reg_val, _msec);
  }

  if (_reg_addr.at(0) < mech::MIN_REG_ADDR)
  {
    return readTopRegData(_reg_addr, _reg_val, _msec);
  }

  std::vector<uint8_t> packet;
  uint32_t expected_packet_response_code = 0;

  if (!ptr_proto_parser_->packReadRegister(_reg_addr, expected_packet_response_code, packet))
  {
    LOG_INDEX_ERROR(msg_header_ + "pack read register failed!");
    return false;
  }

  if (!writeWaitResponse(packet, expected_packet_response_code, _msec))
  {
    return false;
  }

  if (!checkRegisterAddress(_reg_addr, ptr_proto_parser_->register_addr_queue))
  {
    std::queue<uint32_t>().swap(ptr_proto_parser_->register_addr_queue);
    std::queue<uint32_t>().swap(ptr_proto_parser_->register_value_queue);
    return false;
  }

  while (!ptr_proto_parser_->register_value_queue.empty())
  {
    _reg_val.emplace_back(ptr_proto_parser_->register_value_queue.front());
    ptr_proto_parser_->register_value_queue.pop();
  }

  if (_reg_val.size() != _reg_addr.size())
  {
    LOG_INDEX_ERROR(msg_header_ + "return size error!" + ptr_proto_parser_->error_str);
    return false;
  }

  return true;
}

bool MechTcp::readRegData(const uint32_t _start_reg_addr,
                          const uint32_t _reg_number,
                          std::vector<uint32_t>& _reg_val,
                          const uint32_t _msec_10)
{
  if (_start_reg_addr < mech::MIN_TOP_REG_ADDR)
  {
    return read459RegData(_start_reg_addr, _reg_number, _reg_val, _msec_10);
  }
  if (_start_reg_addr < mech::MIN_REG_ADDR)
  {
    return readTopRegData(_start_reg_addr, _reg_number, _reg_val, _msec_10);
  }
  std::vector<uint8_t> packet {};
  uint32_t expected_packet_response_code = 0;
  if (!ptr_proto_parser_->packReadConRegData(_start_reg_addr, _reg_number, expected_packet_response_code, packet))
  {
    LOG_INDEX_ERROR(msg_header_ + "pack read register failed!");
    return false;
  }
  if (!writeWaitResponse(packet, expected_packet_response_code, _msec_10))
  {
    return false;
  }
  if (ptr_proto_parser_->register_value_queue.empty())
  {
    LOG_INDEX_ERROR(msg_header_ + "parse register value error");
    return false;
  }
  while (!ptr_proto_parser_->register_value_queue.empty())
  {
    _reg_val.emplace_back(ptr_proto_parser_->register_value_queue.front());
    ptr_proto_parser_->register_value_queue.pop();
  }
  return true;
}
bool MechTcp::readTopRegData(const uint32_t _start_reg_addr,
                             const uint32_t _reg_number,
                             std::vector<uint32_t>& _reg_val,
                             const uint32_t _msec_10)
{
  std::vector<uint8_t> packet {};
  uint32_t expected_packet_response_code = 0;
  if (!ptr_proto_parser_->packReadConTopRegData(_start_reg_addr, _reg_number, expected_packet_response_code, packet))
  {
    LOG_INDEX_ERROR(msg_header_ + "pack read register failed!");
    return false;
  }
  if (!writeWaitResponse(packet, expected_packet_response_code, _msec_10))
  {
    return false;
  }
  if (ptr_proto_parser_->register_value_queue.empty())
  {
    LOG_INDEX_ERROR(msg_header_ + "parse register value error");
    return false;
  }
  while (!ptr_proto_parser_->register_value_queue.empty())
  {
    _reg_val.emplace_back(ptr_proto_parser_->register_value_queue.front());
    ptr_proto_parser_->register_value_queue.pop();
  }
  return true;
}

bool MechTcp::read459RegData(const uint32_t _start_reg_addr,
                             const uint32_t _reg_number,
                             std::vector<uint32_t>& _reg_val,
                             const uint32_t _msec_10)
{
  if (_reg_number == 0)
  {
    LOG_INDEX_ERROR(msg_header_ + "read459PairRegData _reg_number cannot be 0");
    return false;
  }
  _reg_val.clear();
  _reg_val.resize(_reg_number);
  for (uint32_t i = 0; i < _reg_number; i++)
  {
    if (!read459RegData(_start_reg_addr + i, _reg_val.at(i), _msec_10))
    {
      return false;
    }
  }
  return true;
}

// NOLINTNEXTLINE(bugprone-easily-swappable-parameters)
bool MechTcp::startWriteTopFlash(const uint32_t _start_addr, const uint32_t _len, const uint32_t _msec)
{
  std::vector<uint8_t> packet {};
  ExpectedResp expected_resp;

  if (!ptr_proto_parser_->packStartWriteTopFlash(_start_addr, _len, expected_resp, packet))
  {
    LOG_INDEX_ERROR(msg_header_ + "pack start write top flash failed!");
    return false;
  }
  if (!writeWaitResponse(packet, expected_resp.cmd, _msec))
  {
    return false;
  }

  auto resp_data = ptr_proto_parser_->universal_queue.front();
  ptr_proto_parser_->universal_queue.pop();
  if (resp_data != expected_resp.data)
  {
    LOG_INDEX_ERROR(msg_header_ + "start write top flash failed! response: " + ProtoParser::hex(resp_data) +
                    ", expect: " + ProtoParser::hex(expected_resp.data));
    return false;
  }
  return true;
}
bool MechTcp::writeTopFlash(const uint32_t _pkt_count, const std::vector<uint8_t>& _data, const uint32_t _msec)
{
  std::vector<uint8_t> packet {};
  ExpectedResp expected_resp;

  if (!ptr_proto_parser_->packWriteTopFlash(_pkt_count, _data, expected_resp, packet))
  {
    LOG_INDEX_ERROR(msg_header_ + "pack write top flash failed!");
    return false;
  }
  if (!writeWaitResponse(packet, expected_resp.cmd, _msec))
  {
    return false;
  }
  auto resp_data = ptr_proto_parser_->universal_queue.front();
  ptr_proto_parser_->universal_queue.pop();
  if (resp_data != expected_resp.data)
  {
    LOG_INDEX_ERROR(msg_header_ + "write top flash failed! response: " + ProtoParser::hex(resp_data) +
                    ", expect: " + ProtoParser::hex(expected_resp.data));
    return false;
  }

  return true;
}
bool MechTcp::finishWriteTopFlash(const uint32_t _msec)
{
  std::vector<uint8_t> packet {};
  ExpectedResp expected_resp;

  if (!ptr_proto_parser_->packFinishWriteTopFlash(expected_resp, packet))
  {
    LOG_INDEX_ERROR(msg_header_ + "pack finish write top flash failed!");
    return false;
  }
  if (!writeWaitResponse(packet, expected_resp.cmd, _msec))
  {
    return false;
  }
  auto resp_data = ptr_proto_parser_->universal_queue.front();
  ptr_proto_parser_->universal_queue.pop();
  if (resp_data != expected_resp.data)
  {
    LOG_INDEX_ERROR(msg_header_ + "finish write top flash failed! response: " + ProtoParser::hex(resp_data) +
                    ", expect: " + ProtoParser::hex(expected_resp.data));
    return false;
  }
  return true;
}

bool MechTcp::readRegData(const uint32_t _reg_addr, uint32_t& _reg_val, const uint32_t _msec)
{
  if (_reg_addr < mech::MIN_TOP_REG_ADDR)
  {
    return read459RegData(_reg_addr, _reg_val, _msec);
  }
  if (_reg_addr < mech::MIN_REG_ADDR)
  {
    return readTopRegData(_reg_addr, _reg_val, _msec);
  }
  std::vector<uint32_t> reg_val_vec;
  if (!readRegData(_reg_addr, 1, reg_val_vec, _msec))
  {
    return false;
  }
  _reg_val = reg_val_vec.front();
  return true;
}

bool MechTcp::read459PairRegData(const uint32_t _reg_addr, uint32_t& _reg_val, const uint32_t _msec)
{
  auto reg_addr_high = _reg_addr + 1;
  auto reg_addr_low  = _reg_addr;

  uint32_t reg_val_high = 0;
  if (!read459RegData(reg_addr_high, reg_val_high, _msec))
  {
    LOG_INDEX_ERROR("读取reg_459寄存器失败");
    return false;
  }
  uint32_t reg_val_low = 0;
  if (!read459RegData(reg_addr_low, reg_val_low, _msec))
  {
    LOG_INDEX_ERROR("读取reg_459寄存器失败");
    return false;
  }
  _reg_val = (reg_val_high << 8U) | reg_val_low;
  LOG_INDEX_INFO("读取reg_459寄存器成功, [{:#x}*2, {:#x}]", _reg_addr, _reg_val);
  return true;
}

// NOLINTNEXTLINE(bugprone-easily-swappable-parameters)
bool MechTcp::getEyesSafe(uint32_t& _is_open, const uint32_t _msec)
{
  std::vector<uint8_t> packet;
  uint32_t expected_packet_response_code = 0;

  if (!ptr_proto_parser_->packGetEyesSafe(expected_packet_response_code, packet))
  {
    LOG_INDEX_ERROR(msg_header_ + "pack get eyes safe failed!");
    return false;
  }

  if (!writeWaitResponse(packet, expected_packet_response_code, _msec))
  {
    return false;
  }

  if (ptr_proto_parser_->register_value_queue.empty())
  {
    LOG_INDEX_ERROR(msg_header_ + "parse eyes safe status error");
    return false;
  }

  _is_open = ptr_proto_parser_->register_value_queue.front();
  ptr_proto_parser_->register_value_queue.pop();

  return true;
}

// NOLINTNEXTLINE(bugprone-easily-swappable-parameters)
bool MechTcp::setEyesSafe(const uint32_t _is_open, const uint32_t _msec)
{
  std::vector<uint8_t> packet;
  uint32_t expected_packet_response_code = 0;

  if (!ptr_proto_parser_->packSetEyesSafe(_is_open, expected_packet_response_code, packet))
  {
    LOG_INDEX_ERROR(msg_header_ + "pack set eyes safe failed!");
    return false;
  }

  if (!writeWaitResponse(packet, expected_packet_response_code, _msec))
  {
    return false;
  }

  return true;
}

bool MechTcp::writeZeroAngle(const float _angle, const uint32_t _msec)
{
  return writeCmd(mech::NET_CMD_BOTTOM_BOARD_WRITE_ZERO_ANGLE, _angle, _msec);
}
bool MechTcp::readZeroAngle(float& _angle, const uint32_t _msec)
{
  return readCmd(mech::NET_CMD_BOTTOM_BOARD_READ_ZERO_ANGLE, _angle, _msec);
}

// Explicit template instantiations for the types you want to support
template bool MechTcp::readConfigParamater<helios::ConfigPara>(helios::ConfigPara& _config_paramater,
                                                               const uint32_t _msec);
template bool MechTcp::readConfigParamater<mech::ConfigPara>(mech::ConfigPara& _config_paramater, const uint32_t _msec);
template <typename T>
bool MechTcp::readConfigParamater(T& _config_paramater, const uint32_t _msec)
{
  if (!(std::is_same<T, helios::ConfigPara>::value || std::is_same<T, mech::ConfigPara>::value))
  {
    LOG_INDEX_ERROR(msg_header_ + "read config parameter, unsupported data type!");
    return false;
  }

  std::vector<uint8_t> packet;
  uint32_t expected_packet_response_code = 0;

  if (!ptr_proto_parser_->packReadConfigPara(expected_packet_response_code, packet))
  {
    LOG_INDEX_ERROR(msg_header_ + "pack read config parameter failed!");
    return false;
  }

  if (!writeWaitResponse(packet, expected_packet_response_code, _msec))
  {
    return false;
  }

  if (ptr_proto_parser_->universal_queue.empty())
  {
    LOG_INDEX_ERROR(msg_header_ + "parse config error");
    return false;
  }

  auto payload = ptr_proto_parser_->universal_queue.front();
  ptr_proto_parser_->universal_queue.pop();

  if (payload.size() != sizeof(T))
  {
    LOG_INDEX_ERROR(msg_header_ + "receive data was not expected! data: " + ProtoParser::hex(payload));
    return false;
  }

  std::memcpy(&_config_paramater, payload.data(), sizeof(T));
  return true;
}

bool MechTcp::writeConfigParamater(const helios::ConfigPara& _config_paramater, const uint32_t _msec)
{
  std::vector<uint8_t> packet;
  uint32_t expected_packet_response_code = 0;

  if (!ptr_proto_parser_->packWriteConfigPara(_config_paramater, expected_packet_response_code, packet))
  {
    LOG_INDEX_ERROR(msg_header_ + "pack write config parameter failed");
    return false;
  }

  if (!writeWaitResponse(packet, expected_packet_response_code, _msec))
  {
    return false;
  }

  return true;
}

}  // namespace lidar
}  // namespace robosense