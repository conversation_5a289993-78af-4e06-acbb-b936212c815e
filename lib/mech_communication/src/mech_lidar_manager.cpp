﻿/******************************************************************************
 * Copyright 2023 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#include "mech_lidar_manager.h"

#include "mech_tcp.h"
#include "protocol/protocol_parser.h"
#include "rsfsc_log/rsfsc_log_macro.h"
#include <cmath>

#include "rsfsc_log/rsfsc_log.h"
// NOLINTNEXTLINE(modernize-concat-nested-namespaces)
namespace robosense
{
namespace lidar
{

MechLidarManager::MechLidarManager(ProtocolType _protocol_type, const int _index) : MechTcp(_protocol_type, _index) {}
MechLidarManager::~MechLidarManager() = default;

bool MechLidarManager::writeTopPairRegDataByName(const std::string& _reg_name, const uint16_t _value)
{
  ProtoParser::RegisterInfo register_info;
  if (!ptr_proto_parser_->getRegisterInfo(_reg_name, register_info))
  {
    LOG_INDEX_INFO("register info not found: " + _reg_name);
    return false;
  }
  return MechTcp::writeTopPairRegData(register_info.address, _value);
}
bool MechLidarManager::readTopPairRegDataByName(const std::string& _reg_name, uint16_t& _value)
{
  ProtoParser::RegisterInfo register_info;
  if (!ptr_proto_parser_->getRegisterInfo(_reg_name, register_info))
  {
    LOG_INDEX_INFO("register info not found: " + _reg_name);
    return false;
  }
  return MechTcp::readTopPairRegData(register_info.address, _value);
}
// bool MechLidarManager::writeRegData(const uint32_t _reg_addr, const uint32_t _reg_val, const uint32_t _msec)
// {
//   return MechTcp::writeRegData(_reg_addr, std::vector<uint32_t> { _reg_val }, _msec);
// }
// bool MechLidarManager::writeRegData(const std::string& _reg_name, const uint32_t _value)
// {
//   ProtoParser::RegisterInfo register_info;
//   if (!ptr_proto_parser_->getRegisterInfo(_reg_name, register_info))
//   {
//     LOG_INDEX_INFO("register info not found: " + _reg_name);
//     return false;
//   }
//   return MechTcp::writeRegData(register_info.address, _value);
// }
// bool MechLidarManager::writeRegData(const std::string& _reg_name, const std::vector<uint32_t>& _values)
// {
//   ProtoParser::RegisterInfo register_info;
//   if (!ptr_proto_parser_->getRegisterInfo(_reg_name, register_info))
//   {
//     LOG_INDEX_INFO("register info not found: " + _reg_name);
//     return false;
//   }
//   return MechTcp::writeRegData(register_info.address, _values);
// }
// bool MechLidarManager::readRegData(const uint32_t _reg_addr, uint32_t& _reg_val, const uint32_t _msec)
// {
//   std::vector<uint32_t> reg_val;
//   bool res = MechTcp::readRegData(_reg_addr, 1, reg_val, _msec);
//   if (res)
//   {
//     _reg_val = reg_val.at(0);
//   }
//   return res;
// }
// bool MechLidarManager::readRegData(const std::string& _reg_name, uint32_t& _value)
// {
//   ProtoParser::RegisterInfo register_info;
//   if (!ptr_proto_parser_->getRegisterInfo(_reg_name, register_info))
//   {
//     LOG_INDEX_INFO("register info not found: " + _reg_name);
//     return false;
//   }
//   return MechTcp::readRegData(register_info.address, _value);
// }
bool MechLidarManager::readRegDataEval(const std::string& _reg_name, double& _result)
{
  ProtoParser::RegisterInfo register_info;
  if (!ptr_proto_parser_->getRegisterInfo(_reg_name, register_info))
  {
    LOG_INDEX_INFO("register info not found: " + _reg_name);
    return false;
  }
  uint32_t reg_val = 0;
  if (!MechTcp::readRegData(register_info.address, reg_val))
  {
    return false;
  }

  if (!ptr_proto_parser_->evalRegisterInfo(reg_val, register_info, _result))
  {
    LOG_INDEX_ERROR("evalRegisterInfo failed: " + ptr_proto_parser_->error_str);
    return false;
  }
  return true;
}
void MechLidarManager::setRegAddr(const std::string& _reg_name, const ProtoParser::RegisterInfo& _register_info)
{
  ptr_proto_parser_->setRegAddr(_reg_name, _register_info);
}

bool MechLidarManager::txChannelOpenExclusively(const int _channel_num)
{
  std::string reg_name = "txchn_en";
  ProtoParser::RegisterInfo register_info;
  if (!ptr_proto_parser_->getRegisterInfo(reg_name, register_info))
  {
    LOG_INDEX_INFO("register info not found: " + reg_name);
    return false;
  }

  if (!txChannelCtrlAll(false))
  {
    LOG_INDEX_ERROR("Failed to close all tx channels");
    return false;
  }
  std::vector<uint8_t> packet;
  uint32_t expected_packet_response_code = 0;
  if (!ptr_proto_parser_->packCtrlTxChannelExclusively(_channel_num, true, expected_packet_response_code, packet))
  {
    LOG_INDEX_ERROR("Failed to pack ctrl tx channel exclusively: " + ptr_proto_parser_->error_str);
    return false;
  }
  if (!writeWaitResponse(packet, expected_packet_response_code))
  {
    return false;
  }
  return true;
}

bool MechLidarManager::txChannelCtrl(const int _channel_num, const bool _open)
{
  uint32_t addr = 0;
  if (!ptr_proto_parser_->getTxChannelRegAddr(_channel_num, addr))
  {
    LOG_INDEX_ERROR("Failed to get tx channel reg addr: " + ptr_proto_parser_->error_str);
    return false;
  }
  uint32_t curr_value = 0;
  if (!MechTcp::readRegData(addr, curr_value))
  {
    return false;
  }
  std::vector<uint8_t> packet;
  uint32_t expected_packet_response_code = 0;
  if (!ptr_proto_parser_->packCtrlTxChannel(_channel_num, _open, curr_value, expected_packet_response_code, packet))
  {
    LOG_INDEX_ERROR("Failed to pack ctrl tx channel: " + ptr_proto_parser_->error_str);
    return false;
  }
  if (!writeWaitResponse(packet, expected_packet_response_code))
  {
    return false;
  }
  return true;
}

bool MechLidarManager::txChannelCtrlAll(const bool _open)
{
  std::vector<uint8_t> packet;
  uint32_t expected_packet_response_code = 0;

  if (!ptr_proto_parser_->packCtrlTxChannelAll(_open, expected_packet_response_code, packet))
  {
    LOG_INDEX_ERROR("Failed to pack ctrl tx channel all: " + ptr_proto_parser_->error_str);
    return false;
  }
  if (!writeWaitResponse(packet, expected_packet_response_code))
  {
    return false;
  }
  return true;
}

// bool MechLidarManager::setTxChargeTime(const uint32_t _charge)
// {
//   std::vector<uint32_t> values(2);
//   values[0] = (_charge >> 8U) & 0xffU;
//   values[1] = _charge & 0xffU;
//   return writeRegData("t_tx_charge", values);
// }

bool MechLidarManager::getApdNHVol(float& _value)
{
  double value = NAN;
  if (!readRegDataEval("tem_n3v3", value))
  {
    return false;
  }
  _value = static_cast<float>(value);
  return true;
}

// bool MechLidarManager::setPwmNhvValue(uint32_t _value)
// {
//   std::vector<uint32_t> values(2);
//   values[0] = (_value >> 8U) & 0xffU;
//   values[1] = _value & 0xffU;
//   return writeRegData("pwm_nhv_value", values);
// }

}  // namespace lidar
}  // namespace robosense