﻿/******************************************************************************
 * Copyright 2022 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#include "protocol/airy_parser.h"
#include "protocol/protocol_common.h"
#include "protocol/protocol_parser.h"
#include <cstdint>
#include <gtest/gtest.h>

#include <iostream>
#include <mech_tcp.h>
#include <vector>

#include "rsfsc_log/rsfsc_log.h"

TEST(NopTest, TestOne) {}

TEST(MechTcp, TestDefineClass)
{
  robosense::lidar::MechTcp tcp_connect;
  EXPECT_EQ(false, tcp_connect.isConnected());
}

template <typename T>
bool compareVectors(const std::vector<T>& _expected, const std::vector<T>& _actual)
{
  if (_expected.size() != _actual.size())
  {
    return false;
  }

  auto mismatch_pair = std::mismatch(_expected.begin(), _expected.end(), _actual.begin());
  if (mismatch_pair.first != _expected.end())
  {
    std::cout << "First mismatch at index " << std::distance(_expected.begin(), mismatch_pair.first) << std::endl;
    std::cout << "Expected: " << static_cast<uint32_t>(*mismatch_pair.first)
              << ", Actual: " << static_cast<uint32_t>(*mismatch_pair.second) << std::endl;
    return false;
  }

  return true;
}

TEST(ProtocolParserTest, AiryPackCtrlTxChannelTest)
{
  robosense::lidar::AiryParser proto_parser;
  uint32_t cmd_type = 0;
  std::vector<uint8_t> packet;
  std::vector<uint8_t> tx_channel_open_all           = { 0xff, 0xff, 0x00, 0x00, 0x0c, 0x50, 0x07, 0x01, 0x00, 0x50,
                                               0x10, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0xfe, 0x01, 0xc3 };
  std::vector<uint8_t> tx_channel_close_all          = { 0xff, 0xff, 0x00, 0x00, 0x0c, 0x50, 0x07, 0x01, 0x00, 0x50,
                                                0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfe, 0x01, 0xc2 };
  std::vector<uint8_t> tx_channel_exclusively_open_0 = {
    0xff, 0xff, 0x00, 0x00, 0x38, 0x50, 0x02, 0x51, 0x10, 0x00, 0x00, 0x0c, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0xfe, 0x01, 0xf6
  };
  std::vector<uint8_t> tx_channel_exclusively_open_1 = {
    0xff, 0xff, 0x00, 0x00, 0x38, 0x50, 0x02, 0x51, 0x10, 0x00, 0x00, 0x0c, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0x00, 0x00, 0x00, 0xfe, 0x01, 0xf7
  };
  std::vector<uint8_t> tx_channel_exclusively_open_95 = {
    0xff, 0xff, 0x00, 0x00, 0x38, 0x50, 0x02, 0x51, 0x10, 0x00, 0x00, 0x0c, 0x00, 0x80, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfe, 0x02, 0x75
  };
  uint32_t tx_channel_current_open_value_0   = 0xaa;
  uint32_t tx_channel_current_close_value    = 0x55;
  std::vector<uint8_t> tx_channel_open_0     = { 0xff, 0xff, 0x00, 0x00, 0x0c, 0x50, 0x02, 0x5c, 0x10, 0x00,
                                             0x00, 0x01, 0x00, 0xab, 0x00, 0x00, 0x00, 0xfe, 0x02, 0x74 };
  std::vector<uint8_t> tx_channel_close_0    = { 0xff, 0xff, 0x00, 0x00, 0x0c, 0x50, 0x02, 0x5c, 0x10, 0x00,
                                              0x00, 0x01, 0x00, 0x54, 0x00, 0x00, 0x00, 0xfe, 0x02, 0x1d };
  uint32_t tx_channel_current_open_value_95  = 0x55;
  uint32_t tx_channel_current_close_value_95 = 0xaa;
  std::vector<uint8_t> tx_channel_open_95    = { 0xff, 0xff, 0x00, 0x00, 0x0c, 0x50, 0x02, 0x51, 0x10, 0x00,
                                              0x00, 0x01, 0x00, 0xd5, 0x00, 0x00, 0x00, 0xfe, 0x02, 0x93 };
  std::vector<uint8_t> tx_channel_close_95   = { 0xff, 0xff, 0x00, 0x00, 0x0c, 0x50, 0x02, 0x51, 0x10, 0x00,
                                               0x00, 0x01, 0x00, 0x2a, 0x00, 0x00, 0x00, 0xfe, 0x01, 0xe8 };

  EXPECT_EQ(true, proto_parser.packCtrlTxChannelAll(true, cmd_type, packet));
  EXPECT_EQ(tx_channel_open_all, packet);
  EXPECT_EQ(true, proto_parser.packCtrlTxChannelAll(false, cmd_type, packet));
  EXPECT_EQ(tx_channel_close_all, packet);
  EXPECT_EQ(true, proto_parser.packCtrlTxChannelExclusively(0, true, cmd_type, packet));
  EXPECT_EQ(tx_channel_exclusively_open_0, packet);
  EXPECT_TRUE(compareVectors(tx_channel_exclusively_open_0, packet));
  EXPECT_EQ(cmd_type, robosense::lidar::mech::NET_CMD_TOP_BOARD_CON_WRITE_REGISTER);
  EXPECT_EQ(true, proto_parser.packCtrlTxChannelExclusively(1, true, cmd_type, packet));
  EXPECT_EQ(tx_channel_exclusively_open_1, packet);
  EXPECT_TRUE(proto_parser.packCtrlTxChannelExclusively(95, true, cmd_type, packet));
  EXPECT_EQ(tx_channel_exclusively_open_95, packet);
  EXPECT_TRUE(proto_parser.packCtrlTxChannel(0, true, tx_channel_current_open_value_0, cmd_type, packet));
  EXPECT_EQ(robosense::lidar::mech::NET_CMD_TOP_BOARD_CON_WRITE_REGISTER, cmd_type);
  EXPECT_EQ(tx_channel_open_0, packet);
  EXPECT_TRUE(proto_parser.packCtrlTxChannel(95, true, tx_channel_current_open_value_95, cmd_type, packet));
  EXPECT_EQ(tx_channel_open_95, packet);
  EXPECT_TRUE(proto_parser.packCtrlTxChannel(0, false, tx_channel_current_close_value, cmd_type, packet));
  EXPECT_EQ(tx_channel_close_0, packet);
  EXPECT_TRUE(proto_parser.packCtrlTxChannel(95, false, tx_channel_current_close_value_95, cmd_type, packet));
  EXPECT_EQ(tx_channel_close_95, packet);
}

TEST(ProtocolParserTest, AiryPackWriteDigitalRegister)
{
  robosense::lidar::AiryParser proto_parser;
  uint32_t cmd_type = 0;
  std::vector<uint8_t> packet;
  uint32_t addr  = 0x904;
  uint32_t value = 0xc0;

  std::vector<uint8_t> expect_pack = { 0xff, 0xff, 0x00, 0x00, 0x2c, 0x50, 0x07, 0x05, 0x00, 0x01, 0x30, 0x00, 0x00,
                                       0x09, 0x00, 0x00, 0x00, 0x02, 0x30, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00, 0x03,
                                       0x30, 0x00, 0x00, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x00, 0x00,
                                       0x00, 0x00, 0x00, 0x30, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0xfe, 0x03, 0x4a };
  EXPECT_TRUE(proto_parser.packWriteDigitalRegister({ addr, value }, cmd_type, packet));
  EXPECT_EQ(robosense::lidar::mech::NET_CMD_TOP_BOARD_MULTI_WRITE_REGISTER, cmd_type);
  EXPECT_EQ(expect_pack, packet);
}

int main(int _argc, char** _ptr_argv)
{
  testing::InitGoogleTest(&_argc, _ptr_argv);
  return RUN_ALL_TESTS();
}
