﻿/******************************************************************************
 * Copyright 2022 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#include <QDateTime>
#include <QDir>
#include <QFile>
#include <QTemporaryDir>
#include <QTextStream>
#include <cctype>
#include <cmath>
#include <gtest/gtest.h>
#include <vector>

// 基本测试，验证静态库链接正常
TEST(BasicTest, StaticLibraryLinkage)
{
  // 简单的断言测试
  EXPECT_EQ(2 + 2, 4);
  EXPECT_TRUE(true);
  EXPECT_FALSE(false);
}

// 测试字符串操作
TEST(BasicTest, StringOperations)
{
  std::string test_str = "verification.csv";
  EXPECT_TRUE(test_str.find("verification") != std::string::npos);
  EXPECT_TRUE(test_str.find(".csv") != std::string::npos);
}

// 测试光学误差率计算逻辑（模拟 LidarManager::getDownOpticalError 中的逻辑）
TEST(LidarManagerLogicTest, OpticalErrorRateCalculation)
{
  // 模拟误差率计算逻辑
  auto calculateErrorRate = [](uint32_t error, uint32_t total) -> double {
    if (total == 0)
    {
      return NAN;
    }
    return static_cast<double>(error) / total;
  };

  // 测试正常情况
  EXPECT_DOUBLE_EQ(calculateErrorRate(10, 100), 0.1);
  EXPECT_DOUBLE_EQ(calculateErrorRate(0, 100), 0.0);
  EXPECT_DOUBLE_EQ(calculateErrorRate(100, 100), 1.0);

  // 测试边界情况
  EXPECT_TRUE(std::isnan(calculateErrorRate(10, 0)));
}

// 测试数据转换逻辑（模拟 LidarManager::readTopRegDataByKey 中的逻辑）
TEST(LidarManagerLogicTest, ByteArrayToUint32Conversion)
{
  // 模拟数据转换逻辑
  auto convertBytesToUint32 = [](const std::vector<uint8_t>& data) -> uint32_t {
    uint32_t result = 0;
    for (auto& item : data)
    {
      result = (result << 8U) | (item & 0xffU);
    }
    return result;
  };

  // 测试数据转换
  std::vector<uint8_t> data1 = { 0x12, 0x34, 0x56, 0x78 };
  EXPECT_EQ(convertBytesToUint32(data1), 0x12345678);

  std::vector<uint8_t> data2 = { 0xFF, 0xFF, 0xFF, 0xFF };
  EXPECT_EQ(convertBytesToUint32(data2), 0xFFFFFFFF);

  std::vector<uint8_t> data3 = { 0x00, 0x00, 0x00, 0x01 };
  EXPECT_EQ(convertBytesToUint32(data3), 0x00000001);
}

// 测试 MAC 地址格式验证（模拟 LidarManager 中的 MAC 地址处理）
TEST(LidarManagerLogicTest, MacAddressValidation)
{
  // 模拟 MAC 地址验证逻辑
  auto isValidMacAddress = [](const std::string& mac) -> bool {
    if (mac.length() != 17)
      return false;  // XX:XX:XX:XX:XX:XX

    for (size_t i = 0; i < mac.length(); ++i)
    {
      if (i % 3 == 2)
      {  // 冒号位置
        if (mac[i] != ':')
          return false;
      }
      else
      {  // 十六进制字符位置
        if (!std::isxdigit(mac[i]))
          return false;
      }
    }
    return true;
  };

  // 测试默认 MAC 地址
  EXPECT_TRUE(isValidMacAddress("40:2C:76:08:4A:CC"));

  // 测试其他有效 MAC 地址
  EXPECT_TRUE(isValidMacAddress("00:11:22:33:44:55"));
  EXPECT_TRUE(isValidMacAddress("FF:FF:FF:FF:FF:FF"));

  // 测试无效 MAC 地址
  EXPECT_FALSE(isValidMacAddress("40:2C:76:08:4A"));        // 太短
  EXPECT_FALSE(isValidMacAddress("40:2C:76:08:4A:CC:DD"));  // 太长
  EXPECT_FALSE(isValidMacAddress("40-2C-76-08-4A-CC"));     // 错误分隔符
  EXPECT_FALSE(isValidMacAddress("40:2G:76:08:4A:CC"));     // 无效字符
}

// 测试文件操作
TEST(FileOperationTest, BasicFileOperations)
{
  QTemporaryDir temp_dir;
  ASSERT_TRUE(temp_dir.isValid());

  QString file_path = QDir(temp_dir.path()).absoluteFilePath("test.csv");

  // 测试文件写入
  QFile file(file_path);
  ASSERT_TRUE(file.open(QIODevice::WriteOnly | QIODevice::Text));

  QTextStream stream(&file);
  stream << "Header1,Header2,Header3\n";
  stream << "Value1,Value2,Value3\n";
  file.close();

  // 测试文件读取
  ASSERT_TRUE(file.open(QIODevice::ReadOnly | QIODevice::Text));
  QTextStream read_stream(&file);
  QString content = read_stream.readAll();
  file.close();

  EXPECT_TRUE(content.contains("Header1,Header2,Header3"));
  EXPECT_TRUE(content.contains("Value1,Value2,Value3"));
}

int main(int _argc, char** _ptr_argv)
{
  testing::InitGoogleTest(&_argc, _ptr_argv);
  return RUN_ALL_TESTS();
}
