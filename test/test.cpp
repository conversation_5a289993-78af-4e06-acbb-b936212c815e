/******************************************************************************
 * Copyright 2022 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#include <QDateTime>
#include <QDir>
#include <QFile>
#include <QTemporaryDir>
#include <QTextStream>
#include <fmt/format.h>
#include <gtest/gtest.h>
#include <memory>

// 基本测试，验证静态库链接正常
TEST(BasicTest, StaticLibraryLinkage)
{
  // 简单的断言测试
  EXPECT_EQ(2 + 2, 4);
  EXPECT_TRUE(true);
  EXPECT_FALSE(false);
}

// 测试字符串操作
TEST(BasicTest, StringOperations)
{
  std::string test_str = "verification.csv";
  EXPECT_TRUE(test_str.find("verification") != std::string::npos);
  EXPECT_TRUE(test_str.find(".csv") != std::string::npos);
}

// 测试 fmt 格式化功能
TEST(FmtFormattingTest, HexadecimalFormatting)
{
  // 测试十六进制格式化
  std::string result = fmt::format("0x{:x}", 0x12345678);
  EXPECT_EQ(result, "0x12345678");

  // 测试大写十六进制
  std::string result_upper = fmt::format("0x{:X}", 0xabcdef);
  EXPECT_EQ(result_upper, "0xABCDEF");

  // 测试 CSV 行格式化
  std::string csv_line = fmt::format("{},{},0x{:x},0x{:x},0x{:x},{}\n", "2024-01-01 12:00:00.123", "test_register",
                                     0x1000, 0x12345678, 0x12345678, "PASS");

  std::string expected = "2024-01-01 12:00:00.123,test_register,0x1000,0x12345678,0x12345678,PASS\n";
  EXPECT_EQ(csv_line, expected);
}

// 测试文件操作
TEST(FileOperationTest, BasicFileOperations)
{
  QTemporaryDir temp_dir;
  ASSERT_TRUE(temp_dir.isValid());

  QString file_path = QDir(temp_dir.path()).absoluteFilePath("test.csv");

  // 测试文件写入
  QFile file(file_path);
  ASSERT_TRUE(file.open(QIODevice::WriteOnly | QIODevice::Text));

  QTextStream stream(&file);
  stream << "Header1,Header2,Header3\n";
  stream << "Value1,Value2,Value3\n";
  file.close();

  // 测试文件读取
  ASSERT_TRUE(file.open(QIODevice::ReadOnly | QIODevice::Text));
  QTextStream read_stream(&file);
  QString content = read_stream.readAll();
  file.close();

  EXPECT_TRUE(content.contains("Header1,Header2,Header3"));
  EXPECT_TRUE(content.contains("Value1,Value2,Value3"));
}

int main(int _argc, char** _ptr_argv)
{
  testing::InitGoogleTest(&_argc, _ptr_argv);
  return RUN_ALL_TESTS();
}
