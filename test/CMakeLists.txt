﻿project(test_main LANGUAGES CXX)
find_package(Threads REQUIRED)

# If GoogleTest is not found, fetch it using FetchContent
find_package(GTest QUIET)
if(NOT GTest_FOUND)
  include(FetchContent)
  FetchContent_Declare(
    googletest
    GIT_REPOSITORY ***********************:system_codebase/factory_tool/common_lib/googletest.git
    GIT_TAG release-1.12.1
    GIT_SHALLOW TRUE
    GIT_DEPTH 1
    GIT_CONFIG advice.detachedHead=false)

  # Download and configure GoogleTest
  # FetchContent_MakeAvailable(googletest)
  FetchContent_GetProperties(googletest)
  if(NOT googletest_POPULATED)
    FetchContent_Populate(googletest)
    add_subdirectory(${googletest_SOURCE_DIR} ${googletest_BINARY_DIR} EXCLUDE_FROM_ALL)
  endif()
endif()

add_executable(${PROJECT_NAME} test.cpp)
target_link_libraries(${PROJECT_NAME} gtest gtest_main Threads::Threads)
