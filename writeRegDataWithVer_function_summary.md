# writeRegDataWithVer 函数实现总结

## 功能概述

新增了 `LidarManager::writeRegDataWithVer` 函数，它是一个带有回读校验功能的寄存器写入函数。该函数不仅写入寄存器数据，还会自动进行回读校验，并将校验结果记录到 verification.csv 文件中。

## 函数签名

```cpp
bool writeRegDataWithVer(uint32_t _address, uint32_t _value, const QString& _reg_name = "");
```

### 参数说明
- `_address`: 寄存器地址
- `_value`: 要写入的值
- `_reg_name`: 寄存器名称（可选，如果为空则自动生成）

### 返回值
- `true`: 写入成功且回读校验通过（或未启用校验）
- `false`: 写入失败或回读校验失败

## 实现逻辑

### 1. 寄存器名称处理
```cpp
QString reg_name = _reg_name.isEmpty() ? QString("reg_0x%1").arg(_address, 0, 16) : _reg_name;
```
- 如果提供了寄存器名称，使用提供的名称
- 如果未提供，自动生成格式为 `reg_0x{address}` 的名称

### 2. 寄存器写入
```cpp
if (!MechTcp::writeRegData(_address, _value))
{
    LOG_INDEX_ERROR("[{}:0x{:x}]写入[0x{:x}]失败", reg_name.toStdString(), _address, _value);
    writeVerificationDataToFile(reg_name, _address, _value, 0, false);
    return false;
}
```
- 调用底层的 `MechTcp::writeRegData` 进行实际写入
- 写入失败时记录错误日志和校验文件
- 立即返回 false

### 3. 回读校验（可选）
```cpp
if (!csv_verification_file_path_.isEmpty())
{
    // 等待写入完成
    std::this_thread::sleep_for(std::chrono::milliseconds(10));
    
    uint32_t read_back_value = 0;
    bool verification_success = false;
    
    // 回读并比较
    if (MechTcp::readRegData(_address, read_back_value))
    {
        verification_success = (read_back_value == _value);
        // 记录日志...
    }
    
    // 记录校验结果
    writeVerificationDataToFile(reg_name, _address, _value, read_back_value, verification_success);
    
    // 校验失败则返回 false
    if (!verification_success)
    {
        return false;
    }
}
```

## 功能特性

### 1. 智能命名
- **自动生成**：未提供名称时自动生成 `reg_0x{address}` 格式
- **自定义名称**：支持提供有意义的寄存器名称
- **十六进制格式**：地址以十六进制显示，便于识别

### 2. 完整的错误处理
- **写入失败**：记录失败原因和校验文件
- **回读失败**：记录回读失败情况
- **校验失败**：记录期望值和实际值的差异

### 3. 可选的校验功能
- **路径控制**：通过 `csv_verification_file_path_` 控制是否启用校验
- **向后兼容**：路径为空时不影响基本写入功能
- **性能优化**：只在需要时进行回读操作

### 4. 详细的日志记录
- **成功日志**：记录写入成功和校验成功信息
- **失败日志**：详细记录失败原因和数据对比
- **统一格式**：与其他函数保持一致的日志格式

## 使用示例

### 基本使用（自动生成名称）
```cpp
// 写入寄存器 0x1000，值为 0x12345678
bool result = lidar_manager->writeRegDataWithVer(0x1000, 0x12345678);
if (!result) {
    // 处理写入或校验失败
}
```

### 使用自定义名称
```cpp
// 写入配置寄存器，使用有意义的名称
bool result = lidar_manager->writeRegDataWithVer(0x2000, 0xAABBCCDD, "config_register");
if (!result) {
    // 处理失败情况
}
```

## CSV 输出格式

### 成功案例
```csv
2024-01-01 12:00:00.123,config_register,0x2000,0xaabbccdd,0xaabbccdd,PASS
```

### 失败案例
```csv
2024-01-01 12:00:01.456,reg_0x1000,0x1000,0x12345678,0x12345679,FAIL
```

### 写入失败案例
```csv
2024-01-01 12:00:02.789,reg_0x3000,0x3000,0x87654321,0x0,FAIL
```

## 与现有函数的对比

| 功能 | `MechTcp::writeRegData` | `writeCsvData` | `writeRegDataWithVer` |
|------|-------------------------|----------------|----------------------|
| 基本写入 | ✅ | ✅ | ✅ |
| 批量写入 | ❌ | ✅ | ❌ |
| 回读校验 | ❌ | ✅ | ✅ |
| CSV 记录 | ❌ | ✅ | ✅ |
| 单个寄存器 | ✅ | ❌ | ✅ |
| 自定义名称 | ❌ | ✅ | ✅ |
| 错误处理 | 基本 | 完整 | 完整 |

## 适用场景

### 1. 单个寄存器写入
- 需要立即确认写入结果的场景
- 关键配置寄存器的设置
- 调试和测试过程中的寄存器操作

### 2. 实时校验需求
- 需要确保写入数据正确性的场景
- 安全关键的寄存器操作
- 质量保证和追踪需求

### 3. 问题排查
- 寄存器写入问题的诊断
- 硬件通信问题的定位
- 数据一致性验证

## 性能考虑

### 时间开销
- **基本写入**：与 `MechTcp::writeRegData` 相同
- **回读校验**：额外增加约 10ms + 一次读取操作时间
- **文件记录**：文件 I/O 操作时间（通常 < 1ms）

### 资源使用
- **内存**：临时字符串和变量，影响很小
- **磁盘**：每次调用增加一行 CSV 记录
- **网络**：额外一次寄存器读取操作

## 最佳实践

### 1. 命名规范
```cpp
// 推荐：使用有意义的名称
writeRegDataWithVer(0x1000, value, "motor_speed_config");

// 可接受：让系统自动生成
writeRegDataWithVer(0x1000, value);
```

### 2. 错误处理
```cpp
if (!writeRegDataWithVer(address, value, "important_config")) {
    LOG_ERROR("关键配置写入失败，需要人工干预");
    return false;
}
```

### 3. 批量操作
```cpp
// 对于多个相关寄存器
std::vector<std::pair<uint32_t, uint32_t>> registers = {
    {0x1000, 0x12345678},
    {0x1004, 0x87654321}
};

for (const auto& [addr, val] : registers) {
    if (!writeRegDataWithVer(addr, val)) {
        LOG_ERROR("寄存器 0x{:x} 写入失败", addr);
        break;
    }
}
```

这个新函数为单个寄存器的写入操作提供了完整的校验和记录功能，是对现有 CSV 批量写入功能的有力补充。
