# LidarManager 单元测试策略分析

## 依赖关系分析

### 1. 硬件依赖
- **TCP 网络连接**：继承自 `MechTcp`，需要真实的雷达硬件连接
- **UDP 通信**：`MechUdp` 用于接收 MSOP/DIFOP 数据包
- **网络接口**：需要特定的 IP 地址和端口配置

### 2. 外部系统依赖
- **文件系统**：CSV 文件读写、tcpdump 文件操作
- **系统工具**：tcpdump 网络抓包工具
- **Qt 框架**：QDateTime、QString、QFile 等

### 3. 应用程序依赖
- **WidgetLidarInfo**：UI 组件，显示雷达信息
- **CsvUtils**：CSV 文件解析器
- **应用程序全局状态**：`app()` 函数获取全局对象

### 4. 协议依赖
- **雷达协议**：MECH_AIRY、MECH_HELIOS 等特定协议
- **数据包格式**：MsopPacket96、DifopPacket 等

## 测试策略设计

### 策略 1：接口隔离 + Mock 对象
将 `LidarManager` 的核心逻辑与外部依赖分离，使用 Mock 对象替代真实依赖。

### 策略 2：分层测试
- **单元测试**：测试纯逻辑函数（如 CSV 校验格式化）
- **集成测试**：测试与 Mock 对象的交互
- **端到端测试**：使用真实硬件的完整测试

### 策略 3：依赖注入
通过构造函数或设置方法注入依赖，便于测试时替换为 Mock 对象。

## 推荐的测试实现方案

### 1. 创建可测试的接口抽象

```cpp
// 抽象网络接口
class INetworkInterface {
public:
    virtual ~INetworkInterface() = default;
    virtual bool connect(const std::string& ip, uint16_t port, uint32_t timeout) = 0;
    virtual bool writeRegData(uint32_t address, uint32_t value) = 0;
    virtual bool readRegData(uint32_t address, uint32_t& value) = 0;
    virtual bool isConnected() const = 0;
};

// 抽象文件接口
class IFileInterface {
public:
    virtual ~IFileInterface() = default;
    virtual bool writeToFile(const QString& path, const QString& content) = 0;
    virtual bool fileExists(const QString& path) const = 0;
};
```

### 2. 创建 Mock 实现

```cpp
class MockNetworkInterface : public INetworkInterface {
public:
    MOCK_METHOD(bool, connect, (const std::string& ip, uint16_t port, uint32_t timeout), (override));
    MOCK_METHOD(bool, writeRegData, (uint32_t address, uint32_t value), (override));
    MOCK_METHOD(bool, readRegData, (uint32_t address, uint32_t& value), (override));
    MOCK_METHOD(bool, isConnected, (), (const, override));
};

class MockFileInterface : public IFileInterface {
public:
    MOCK_METHOD(bool, writeToFile, (const QString& path, const QString& content), (override));
    MOCK_METHOD(bool, fileExists, (const QString& path), (const, override));
};
```

### 3. 重构 LidarManager 支持依赖注入

```cpp
class LidarManager {
private:
    std::unique_ptr<INetworkInterface> network_interface_;
    std::unique_ptr<IFileInterface> file_interface_;
    
public:
    // 生产环境构造函数
    LidarManager(rsfsc_lib::WidgetLidarInfo* lidar_info);
    
    // 测试环境构造函数
    LidarManager(std::unique_ptr<INetworkInterface> network,
                 std::unique_ptr<IFileInterface> file);
};
```

## 当前可行的测试方案

考虑到重构成本，我们可以采用以下渐进式方案：

### 阶段 1：测试纯逻辑函数
测试不依赖外部资源的函数，如：
- `writeVerificationDataToFile` 的格式化逻辑
- 数据转换和计算函数
- 配置参数解析

### 阶段 2：使用 Test Double 模式
为关键依赖创建测试替身：
- 文件操作的测试替身
- 网络操作的模拟器

### 阶段 3：集成测试
使用真实的文件系统但模拟网络连接进行测试。

## 立即可实施的测试用例

基于当前代码结构，我们可以立即实现以下测试：

1. **CSV 校验功能测试**
2. **配置参数解析测试**
3. **数据格式转换测试**
4. **错误处理逻辑测试**

这些测试可以在不修改现有代码的情况下实现，为后续的重构提供安全保障。
