﻿# cmake options
cmake_minimum_required(VERSION 3.16...3.27)

string(TIMESTAMP PROJECT_COMPILE_TIME %Y%m%d)
# 获取并格式化当前的日期和时间
string(TIMESTAMP PROJECT_COMPILE_DATETIME "%Y-%m-%d %H:%M:%S")

project(mech_aging VERSION 3.0.0.${PROJECT_COMPILE_TIME})

set(PROJECT_CODE "410")
set(PROJECT_NAME_EN "Airy Mech Aging")
set(PROJECT_NAME_ZH "Airy老化")

# =========================
# Option
# =========================
option(BUILD_MECH_AGING_UTEST "build gtest or not" ON)

# compile options
# =========================
# Set C++ Standard
# =========================
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)
set(CMAKE_EXE_LINKER_FLAGS "-Wl,--disable-new-dtags")
add_definitions(-Wall)

add_definitions(-DRSFSCLOG_UES_SPDLOG -DRSFSCLOG_USE_QT -DRSFSCLOG_ENABLE_INIT_PATH)
add_definitions(-DRSFSCLOG_INDEX_SUPPORT)
add_subdirectory(lib/rsfsc_lib)
find_package(RSFSCLog REQUIRED)
include_directories(lib)

# Detect Windows platform and set Windows-specific definitions
if(WIN32)
  add_definitions(-D_WIN32_WINNT=0x0A00)
endif()

# Check if MSVC is being used
if(MSVC)
  message(STATUS "MSVC detected")

  # Set UTF-8 encoding
  add_compile_options("/utf-8")

  # Set compiler flags for release and debug configurations
  set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} /O2 /W3 /P")
  string(APPEND CMAKE_CXX_FLAGS_DEBUG "/Od /W3 /Zi")
else()
  message(STATUS "Non-MSVC compiler detected")

  # General compiler options
  add_compile_options(-Wall)

  # Set compiler flags for release and debug configurations
  string(APPEND CMAKE_CXX_FLAGS_RELEASE " -O3")
  string(APPEND CMAKE_CXX_FLAGS_DEBUG " -O0 -g -ggdb")

  # Check if the compiler is Clang and set Clang-specific flags
  if(CMAKE_CXX_COMPILER_ID MATCHES "Clang")
    message(STATUS "Clang compiler detected")
    string(APPEND CMAKE_CXX_FLAGS_DEBUG " -fstandalone-debug")
  endif()
endif()

# must clean before build if you change this
option(SHOW_MESSAGE_ON_TERMINAL "show message on terminal or textbrowser" OFF)
option(SEND_MAIL_SUPPORT "if function and ui about send mail is enable" OFF)
option(MARKER_SUPPORT "if function and ui about marker is enable" OFF)

message("---------------------------------------------------------------------")
message("show message on terminal:      " ${SHOW_MESSAGE_ON_TERMINAL})
message("send mail support:             " ${SEND_MAIL_SUPPORT})
message("marker support:                " ${MARKER_SUPPORT})
message("---------------------------------------------------------------------")

find_package(Threads REQUIRED)

find_package(Git QUIET)
set(CMAKE_EXPORT_COMPILE_COMMANDS ON)

find_package(
  Qt5
  COMPONENTS Widgets Network SerialPort
  REQUIRED)

# definitions
add_definitions(-DQT_NO_KEYWORDS -g)

set(INSTALL_PREFIX_SHARE ${CMAKE_INSTALL_PREFIX}/share/${PROJECT_NAME}/)
set(CMAKE_INSTALL_RPATH ${CMAKE_INSTALL_PREFIX}/lib/${PROJECT_NAME} ${CMAKE_INSTALL_PREFIX}/lib/${PROJECT_NAME}/lib)

# file operation
string(TIMESTAMP PROJECT_COMPILE_TIME "%Y%m%d_%H%M%S")
find_package(Git QUIET)
execute_process(
  COMMAND ${GIT_EXECUTABLE} rev-parse --short HEAD
  WORKING_DIRECTORY "${CMAKE_CURRENT_SOURCE_DIR}"
  OUTPUT_VARIABLE PROJECT_COMPILE_COMMIT
  ERROR_QUIET OUTPUT_STRIP_TRAILING_WHITESPACE)
configure_file("${PROJECT_SOURCE_DIR}/include/config.h.in" "${PROJECT_SOURCE_DIR}/include/config.h")
execute_process(COMMAND ${GIT_EXECUTABLE} config core.hooksPath .githooks
                WORKING_DIRECTORY "${CMAKE_CURRENT_SOURCE_DIR}")

find_program(LSB_EXEC lsb_release)

if(LSB_EXEC MATCHES "NOTFOUND")
  message("\n lsb_release not found, please install using: \n\t sudo apt install lsb_release\n")
endif()

execute_process(
  COMMAND ${LSB_EXEC} -cs
  OUTPUT_VARIABLE LSB_CODENAME
  OUTPUT_STRIP_TRAILING_WHITESPACE)

qt5_wrap_cpp(MOC_ABOUT_FILES lib/rsfsc_lib/ui/widget_about.h)
add_library(ui_about ${MOC_ABOUT_FILES} lib/rsfsc_lib/ui/widget_about.cpp)
target_link_libraries(ui_about Qt5::Core Qt5::Widgets)

add_subdirectory(lib/mech_comm_lib)
add_subdirectory(lib/mech_comm_lib/rsfsc_limit)
add_subdirectory(lib/relay_controller_driver)
add_subdirectory(lib/mech_communication)

file(GLOB_RECURSE HEADER_FILES CONFIGURE_DEPENDS include/*.h include/*.hpp)
file(
  GLOB_RECURSE
  LIB_SOURCE_FILES
  CONFIGURE_DEPENDS
  src/*.cpp
  src/*.h
  src/*.hpp
  src/*.ui)
file(GLOB_RECURSE QT_UI_FILES CONFIGURE_DEPENDS ui/*.cpp ui/*.ui ui/*.h)
set(QT_RESOURCES resource/resource.qrc)
set(RSFSC_LIB_FILES lib/rsfsc_lib/include/widget_lidar_info.h)

# 从库源文件中排除 main.cpp
list(FILTER LIB_SOURCE_FILES EXCLUDE REGEX ".*main\\.cpp$")

# 创建静态库
add_library(${PROJECT_NAME}_lib STATIC ${HEADER_FILES} ${LIB_SOURCE_FILES} ${QT_UI_FILES} ${QT_RESOURCES}
                                       ${RSFSC_LIB_FILES})
set_target_properties(
  ${PROJECT_NAME}_lib
  PROPERTIES AUTOMOC ON
             AUTORCC ON
             AUTOUIC ON)

# 创建可执行文件
add_executable(${PROJECT_NAME} src/main.cpp)
set_target_properties(
  ${PROJECT_NAME}
  PROPERTIES AUTOMOC ON
             AUTORCC ON
             AUTOUIC ON)

# 静态库的包含目录和链接库
target_include_directories(${PROJECT_NAME}_lib PUBLIC include)
target_include_directories(${PROJECT_NAME}_lib SYSTEM PRIVATE lib/rsfsc_lib/include lib/rsfsc_lib/ui lib
                                                              ${Boost_INCLUDE_DIRS})
target_include_directories(${PROJECT_NAME}_lib PUBLIC src src/lidar_data_read/ ui)

target_link_libraries(
  ${PROJECT_NAME}_lib
  Qt5::Widgets
  Qt5::Network
  rsfsc_lib
  rsfsc_limit
  rsfsc_fsm
  ui_about
  relay_controller_driver_static
  mech_communication
  tcpdump_utils
  mech_comm_func
  mech_third_party
  Threads::Threads)

# 可执行文件的包含目录和链接库
target_include_directories(${PROJECT_NAME} PRIVATE include)
target_link_libraries(${PROJECT_NAME} ${PROJECT_NAME}_lib)

install(
  TARGETS ${PROJECT_NAME}
  PERMISSIONS
    OWNER_EXECUTE
    OWNER_WRITE
    OWNER_READ
    GROUP_EXECUTE
    GROUP_READ
    WORLD_EXECUTE
    WORLD_READ
  ARCHIVE DESTINATION ${CMAKE_INSTALL_PREFIX}/lib/${PROJECT_NAME}
  LIBRARY DESTINATION ${CMAKE_INSTALL_PREFIX}/lib/${PROJECT_NAME}
  RUNTIME DESTINATION ${CMAKE_INSTALL_PREFIX}/bin/${PROJECT_NAME})

install(
  DIRECTORY resource config
  DESTINATION ${CMAKE_INSTALL_PREFIX}/share/${PROJECT_NAME}
  USE_SOURCE_PERMISSIONS
  DIRECTORY_PERMISSIONS
    OWNER_EXECUTE
    OWNER_WRITE
    OWNER_READ
    GROUP_EXECUTE
    GROUP_READ
    WORLD_EXECUTE
    WORLD_READ)

# =========================
# Create Unit Test
# =========================
if(BUILD_MECH_AGING_UTEST)
  add_subdirectory(test)
endif(BUILD_MECH_AGING_UTEST)

# delete old deb TODO check if it is run by cpack.
file(
  GLOB_RECURSE deb_files
  LIST_DIRECTORIES false
  RELATIVE ${CMAKE_CURRENT_SOURCE_DIR}/release/ubuntu_2004
  ${PROJECT_NAME}*.deb)
foreach(deb_file IN LISTS deb_files)
  if(EXISTS "${CMAKE_CURRENT_SOURCE_DIR}/release/ubuntu_2004/${deb_file}")
    message("remove deb_file: " ${CMAKE_CURRENT_SOURCE_DIR}/release/${deb_file})
    file(REMOVE ${CMAKE_CURRENT_SOURCE_DIR}/release/${deb_file})
  endif()
endforeach()

set(CPACK_GENERATOR "DEB")
set(CPACK_DEBIAN_PACKAGE_NAME ${PROJECT_NAME})
set(CPACK_DEBIAN_PACKAGE_MAINTAINER "david.dai") # required
set(CPACK_DEBIAN_PACKAGE_ARCHITECTURE "amd64")
set(CPACK_PACKAGE_VERSION_MAJOR ${PROJECT_VERSION_MAJOR})
set(CPACK_PACKAGE_VERSION_MINOR ${PROJECT_VERSION_MINOR})
set(CPACK_PACKAGE_VERSION_PATCH ${PROJECT_VERSION_PATCH})
set(CPACK_PACKAGE_ICON resource/img/icon.png)
set(CPACK_OUTPUT_FILE_PREFIX "${CMAKE_CURRENT_SOURCE_DIR}/release/ubuntu_2004")
set(CPACK_PACKAGE_FILE_NAME "${PROJECT_NAME}_${PROJECT_VERSION}_${PROJECT_COMPILE_COMMIT}_${LSB_CODENAME}")

set(RELEASE_DIR ${CMAKE_CURRENT_SOURCE_DIR}/release/ubuntu_2004/)

configure_file(cmake/cpack/_install.sh ${CMAKE_CURRENT_BINARY_DIR}/install.sh @ONLY)
configure_file(cmake/cpack/script.desktop.in ${CMAKE_CURRENT_BINARY_DIR}/${PROJECT_NAME}.desktop @ONLY)
install(FILES ${CMAKE_CURRENT_BINARY_DIR}/${PROJECT_NAME}.desktop DESTINATION /usr/share/applications)

configure_file(${CMAKE_CURRENT_SOURCE_DIR}/cmake/post_cpack.cmake.in ${CMAKE_CURRENT_BINARY_DIR}/post_cpack.cmake @ONLY)
configure_file(${CMAKE_CURRENT_SOURCE_DIR}/cmake/pre_cpack.cmake.in ${CMAKE_CURRENT_BINARY_DIR}/pre_cpack.cmake @ONLY)

set(CPACK_PRE_BUILD_SCRIPTS "${CMAKE_CURRENT_BINARY_DIR}/pre_cpack.cmake")
set(CPACK_POST_BUILD_SCRIPTS "${CMAKE_CURRENT_BINARY_DIR}/post_cpack.cmake")

include(CPack) # must after the setting
