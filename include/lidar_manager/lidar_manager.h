/******************************************************************************
 * Copyright 2024 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai
 *
 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.
 *
 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONS<PERSON>QUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#ifndef LIDAR_MANAGER_H
#define LIDAR_MANAGER_H

#include "data_struct.h"
#include "mech_tcp.h"
#include "protocol/protocol_common.h"
#include "rsfsc_utils/tcpdump_utils.h"
#include "utils/csv_utils.h"
#include <QDateTime>
#include <QString>

// NOLINTNEXTLINE(modernize-concat-nested-namespaces)
namespace robosense
{
namespace lidar
{

namespace rsfsc_lib
{
class WidgetLidarInfo;
}  // namespace rsfsc_lib

class MechUdp;

class LidarManager : public MechTcp
{
public:
  explicit LidarManager(rsfsc_lib::WidgetLidarInfo* _lidar_info);
  explicit LidarManager(LidarManager&&)      = delete;
  explicit LidarManager(const LidarManager&) = delete;
  LidarManager& operator=(LidarManager&&) = delete;
  LidarManager& operator=(const LidarManager&) = delete;
  virtual ~LidarManager();

  void abort() override;

  rsfsc_lib::WidgetLidarInfo* getLidarInfo() { return lidar_info_; }
  LimitInfo getLimitInfo(const QString& _key);
  int getLidarIndex();
  QString getLidarInfoSn();

  void setMesMacAddress(const std::string& _mac_address) { mes_mac_address_ = _mac_address; }
  std::string getMesMacAddress() const { return mes_mac_address_; }

  uint32_t getLidarPsVersion();
  uint32_t getLidarPlVersion();

  bool readRegDataByKey(const QString& _key, uint32_t& _data, const int _timeout = 8000);
  bool readTopRegDataByKey(const QString& _key, uint32_t& _data, const uint32_t _byte_size, const int _timeout = 8000);
  bool writeRegDataByKey(const QString& _key, const uint32_t _data);

  bool readReg459(const uint32_t _reg_addr, uint32_t& _reg_val);
  bool readReg459Pair(const uint32_t _reg_addr, uint32_t& _reg_val);

  bool readVbdCurve(uint16_t& _v0, uint16_t& _v1, uint16_t& _v2);
  bool readVbd(uint32_t& _vbd_intercept_hex, uint32_t& _vbd_err_hex);

  bool connect(const uint32_t _msec = 50000);
  bool connect(const QString& _ip, const int _port, const int _timeout = 50000);
  bool connectAndWaitForTop(const uint32_t _msec = 50000);
  bool getConnectedIpPort(QString& _ip, int& _port);

  bool setPowerOn();
  bool changeLidarNet(const QString& _ip, const int _msop_port, const int _difop_port);

  bool writeCsvData(const QString& _key);
  bool writeCsvInitRegData();
  bool deInitLidarPara();

  void setCsvVerificationFilePath(const QString& _file_path) { csv_verification_file_path_ = _file_path; }
  QString getCsvVerificationFilePath() const { return csv_verification_file_path_; }

  bool startUpOpticalErrorTest();
  bool stopUpOpticalErrorTest();
  bool getUpOpticalError(uint32_t& _error, uint32_t& _total);
  bool startDownOpticalErrorTest();
  bool stopDownOpticalErrorTest();
  bool getDownOpticalError(uint32_t& _error, uint32_t& _total);
  void getOpticalErrorRate(double& _up_error_rate, double& _down_error_rate) const
  {
    _up_error_rate   = optical_up_error_rate_;
    _down_error_rate = optical_down_error_rate_;
  }

  bool getDifopData(const int _timeout = 60000);
  std::vector<MsopPacket96> getMsopData(const int _count, const int _timeout = 60000);

  bool getConfigParam(mech::ConfigPara& _config_para);
  std::string getConfigParamLidarSN();

  uint32_t getPsVersion() const { return ps_version_; }
  uint32_t getPlVersion() const { return pl_version_; }

  bool isNetNeedChange(bool& _is_need_change, const QString& _ip, const int _msop_port, const int _difop_port);
  bool startMonitorDifop();
  bool stopMonitorDifop();

  bool firmwareUpdate(const uint16_t& _cmd_type, const QString& _file_path);
  bool firmwareUpdateApp(const QString& _file_path);
  bool firmwareUpdateBot(const QString& _file_path);
  bool firmwareUpdateTop(const QString& _file_path);
  bool firmwareUpdateWriteConfig(const QString& _file_path);
  bool firmwareUpdateCheckConfig(const QString& _file_path, const QString& _read_val_file_path);

  bool writeTopFlash(const QByteArray& _data, const uint32_t _addr_start);

  bool startEncodCalib(const int _timeout_secs = 120);
  bool stopEncodCalib();
  bool getEncodCalibData(std::vector<uint32_t>& _coeff_vec, std::vector<uint32_t>& _insert_step);
  bool sendScaleAvgToMotor(const std::vector<uint32_t>& _scale_avg);

  void setReadDifopCallBack(const std::function<void(const DifopPacket&)>& _difop_cb) { difop_cb_ = _difop_cb; }
  void setReadMsopCallBack(const std::function<void(const MsopPacket96&)>& _msop_cb) { msop_cb_ = _msop_cb; }

  bool isLastDifopNotTimeout(const int _timeout_s);
  DifopInfo getLastDifopInfo() { return DifopInfo(difop_pkt_, last_difop_time_); }

  bool startTcpdumpBothOrgAndObjIPExcludeMSOP(const std::string& _dump_file_path);
  bool startTcpdumpExcludeMSOP(const std::string& _dump_file_path);
  void stopTcpdumpExcludeMSOP();

  bool startTcpdumpOnlyMSOP(const std::string& _dump_file_path);
  void stopTcpdumpOnlyMSOP();

private:
  QDateTime last_difop_time_;
  DifopPacket difop_pkt_;
  QDateTime last_msop_time_;
  // MsopPacket96 msop_pkt_;

  TcpdumpUtils tcpdump_exclude_msop_;
  TcpdumpUtils tcpdump_only_msop_;

  std::function<void(const DifopPacket&)> difop_cb_;
  std::function<void(const MsopPacket96&)> msop_cb_;

  rsfsc_lib::WidgetLidarInfo* lidar_info_;
  std::atomic<bool> is_abort_ { false };
  std::shared_ptr<CsvUtils> reg_csv_utils_ptr_;
  std::shared_ptr<MechUdp> difop_udp_client_;

  std::string lidar_sn_;
  std::string ip_addr_;
  std::string mes_mac_address_ = "40:2C:76:08:4A:CC";
  uint16_t msop_port_          = 0;
  uint16_t difop_port_         = 0;
  QString csv_verification_file_path_;
  uint32_t pl_version_       = 0;
  uint32_t ps_version_       = 0;
  uint32_t software_version_ = 0;
  uint32_t motor_version_    = 0;
  mech::ConfigPara config_para_;

  double optical_up_error_rate_   = NAN;
  double optical_down_error_rate_ = NAN;

  std::mutex mtx_msop_;
  std::condition_variable cv_msop_;

private:
  bool writeVerificationDataToFile(const QString& _reg_name,
                                   uint32_t _address,
                                   uint32_t _write_value,
                                   uint32_t _read_value,
                                   bool _is_match);
};

}  // namespace lidar
}  // namespace robosense
#endif  // LIDAR_MANAGER_H