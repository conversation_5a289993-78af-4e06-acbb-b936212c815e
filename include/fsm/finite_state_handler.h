﻿/******************************************************************************
* Copyright 2022 RoboSense All rights reserved.
* Suteng Innovation Technology Co., Ltd. www.robosense.ai

* This software is provided to you directly by RoboSense and might
* only be used to access RoboSense LiDAR. Any compilation,
* modification, exploration, reproduction and redistribution are
* restricted without RoboSense's prior consent.

* THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
* WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
* OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
* DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
* INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
* (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
* SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
* HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
* STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
* IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
* POSSIBILITY OF SUCH DAMAGE.
*****************************************************************************/

#ifndef FINITE_STATE_HANDLER_H
#define FINITE_STATE_HANDLER_H

#include "data_struct.h"
#include "rsfsc_log/rsfsc_log_macro.h"
#include "utils/decl_name.h"
#include <condition_variable>
#include <cstdint>
#include <memory>
#include <mutex>
#include <vector>

namespace robosense  // NOLINT(modernize-concat-nested-namespaces)
{
namespace lidar
{

class LidarManager;

namespace rsfsc_lib
{
class WidgetLidarInfo;
}  // namespace rsfsc_lib

//class FiniteStateHandler;
/**
 * @class FiniteStateHandler 
 * @brief 状态处理器
 */
class FiniteStateHandler
{
  DECL_CLASSNAME(FiniteStateHandler)
public:
  explicit FiniteStateHandler(const ActionState& _state) : bind_state_(_state) {};
  FiniteStateHandler(const FiniteStateHandler&) = default;
  FiniteStateHandler& operator=(const FiniteStateHandler&) = default;
  FiniteStateHandler(FiniteStateHandler&&)                 = default;
  FiniteStateHandler& operator=(FiniteStateHandler&&) = default;
  virtual ~FiniteStateHandler() noexcept              = default;

  /**
  * @brief 状态处理函数
  * @return 返回下一个状态
  * @note 每一个子类都必须实现该方法
  */
  virtual int handleState()                                                                                     = 0;
  virtual void transmitMember(const ActionState _curr_state, std::shared_ptr<FiniteStateHandler> _next_handler) = 0;
  virtual void abort()                                                                                          = 0;

  ActionState getStateId() { return bind_state_; }

private:
  ActionState bind_state_ { STATE_END };
};

template <typename WorkModelT>
class WorkHandler : public FiniteStateHandler
{
  IMPL_CLASSNAME(WorkHandler)
public:
  explicit WorkHandler(const ActionState _state) : FiniteStateHandler(_state) {};
  ~WorkHandler() override         = default;
  WorkHandler(const WorkHandler&) = default;
  WorkHandler& operator=(const WorkHandler&) = default;
  WorkHandler(WorkHandler&&) noexcept        = default;
  WorkHandler& operator=(WorkHandler&&) noexcept = default;

  /**
     * @brief 状态处理函数
     * @return 返回下一个状态
     * @note 每一个子类都必须实现该方法
     */
  int handleState() override = 0;
  std::vector<std::shared_ptr<WorkModelT>>& getWorkModelVec() { return work_model_ptr_vec_; };
  std::shared_ptr<WorkModelT> getWorkModel() { return work_model_ptr_vec_[0]; };
  std::shared_ptr<LidarManager> getLidarManager() { return work_model_ptr_vec_[0]->getLidarManager(); };
  rsfsc_lib::WidgetLidarInfo* getLidarInfo() { return getLidarManager()->getLidarInfo(); };
  int getLidarIndex() { return work_model_ptr_vec_[0]->getLidarIndex(); };
  int getLogIndex() { return getLidarIndex(); }
  // std::shared_ptr<ParaInfo> getParaInfo() { return getWorkModel()->getParaInfo(); };
  // QVariant getParaProp(const QString& _key) { return getParaInfo()->getProperty(_key); };

  void setWorkModelVec(const std::vector<std::shared_ptr<WorkModelT>> _work_model_ptr_vec)
  {
    work_model_ptr_vec_ = _work_model_ptr_vec;
  }
  void setLastMachineState(const ActionState _state) { last_state_ = _state; }
  void transmitMember(const ActionState _curr_state, std::shared_ptr<FiniteStateHandler> _next_handler) override
  {
    std::shared_ptr<WorkHandler<WorkModelT>> next_work_handler =
      std::dynamic_pointer_cast<WorkHandler<WorkModelT>>(_next_handler);
    next_work_handler->setWorkModelVec(this->work_model_ptr_vec_);
    next_work_handler->setLastMachineState(_curr_state);
    if (getStateId() != _next_handler->getStateId())
    {
      this->work_model_ptr_vec_.clear();
    }
  };
  ActionState getLastMachineState() { return last_state_; }

  void abort() override
  {
    {
      std::unique_lock<std::mutex> locker(sleep_mutex_);
      is_abort_ = true;
    }
    getWorkModel()->abort();
    sleep_cv_.notify_all();
  }

  bool msleep(uint32_t _msec)
  {
    LOG_INDEX_DEBUG("sleep {} ms", _msec);
    std::unique_lock<std::mutex> lock(sleep_mutex_);
    return (!is_abort_ && !sleep_cv_.wait_for(lock, std::chrono::milliseconds(_msec), [&] { return is_abort_; }));
  }

  bool sleep(uint32_t _sec)
  {
    LOG_INDEX_DEBUG("sleep {} s", _sec);
    std::unique_lock<std::mutex> lock(sleep_mutex_);
    return (!is_abort_ && !sleep_cv_.wait_for(lock, std::chrono::seconds(_sec), [&] { return is_abort_; }));
  }

  bool& isAbort() { return is_abort_; }
  void resetAbort() { is_abort_ = false; }

private:
  std::vector<std::shared_ptr<WorkModelT>> work_model_ptr_vec_;
  std::condition_variable sleep_cv_;
  std::mutex sleep_mutex_;
  ActionState last_state_ { STATE_END };

  bool is_abort_ { false };
};

}  // namespace lidar
}  // namespace robosense

#endif  // FINITE_STATE_HANDLER_H
