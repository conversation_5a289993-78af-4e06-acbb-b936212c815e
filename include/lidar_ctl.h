﻿/******************************************************************************
 * Copyright 2022 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#ifndef LIDAR_CTL_H
#define LIDAR_CTL_H

#include "fsm/finite_state_handler.h"
#include "utils/decl_name.h"
#include "work_model/aging_work_model.h"
#include <QMutex>
#include <QMutexLocker>
#include <QWaitCondition>
#include <cstdint>
#include <string>
#include <vector>

// NOLINTNEXTLINE(modernize-concat-nested-namespaces)
namespace robosense
{

namespace lidar
{
/*修改为支持状态机的形式*/

class InterruptException : public std::exception
{
public:
  explicit InterruptException(const std::string& _msg) : msg_(_msg) {}
  explicit InterruptException(const QString& _msg) : msg_(_msg.toStdString()) {}
  explicit InterruptException(const char* _msg) : msg_(_msg) {}

  [[nodiscard]] const char* what() const noexcept override { return msg_.c_str(); }

private:
  std::string msg_;
};

class AgingWorkHandler : public WorkHandler<AgingWorkModel>
{
  IMPL_CLASSNAME(AgingWorkHandler)
public:
  explicit AgingWorkHandler(const ActionState& _state) : WorkHandler<AgingWorkModel>(_state) {}
  AgingWorkHandler(const AgingWorkHandler&) = delete;
  AgingWorkHandler(AgingWorkHandler&&)      = delete;
  AgingWorkHandler& operator=(const AgingWorkHandler&) = delete;
  AgingWorkHandler& operator=(AgingWorkHandler&&) = delete;
  ~AgingWorkHandler() override;

  int handleState() override;
  virtual int handleEnter() { return 0; }
  virtual int handle() = 0;
  virtual int handleExit() { return 0; }

  [[nodiscard]] ActionState getNextState() const { return next_state_; }

  ParaInfo& getParaInfo() { return getWorkModel()->getParaInfo(); }

  // 自定义函数
  std::string pingWait(const std::vector<std::string>& _ip_vec, uint32_t _msec);
  bool connectLidar(const QString& _ip, int _port, int _timeout);
  bool connectLidarCurrIP();
  bool connectLidarInfoIP();

  // 默认等待Aging ip
  bool waitForStartUpComplete();
  bool waitForStartUpCompleteByLidarInfo();
  bool waitForStartUpComplete(const QString& _ip, const int _port, const int _timeout);

  bool waitForOneMsopPacket(const QString& _ip, const int _port, const int _timeout = 50000);

  void abortAndMaintainEnv(const QString& _msg);
  void setWorkModelFailMsg(const QString& _fail_label, std::string_view _fail_msg);
  void setWorkModelFailMsg(std::string_view _fail_msg);

private:
  ActionState next_state_ = STATE_END;
  std::mutex mutex_;
  std::condition_variable condition_;
};

class CheckMes : public AgingWorkHandler
{
  IMPL_CLASSNAME(CheckMes)

public:
  CheckMes() : AgingWorkHandler(STATE_CHECK_MES) {}
  int handleEnter() override;
  int handleExit() override;
  int handle() override;
};

class ConnectLidar : public AgingWorkHandler
{
  IMPL_CLASSNAME(ConnectLidar)

public:
  ConnectLidar() : AgingWorkHandler(STATE_AGING_CONNECT_LIDAR) {}
  int handle() override;
};

class ChangeLidarIp : public AgingWorkHandler
{
  IMPL_CLASSNAME(ChangeLidarIp)

public:
  ChangeLidarIp() : AgingWorkHandler(STATE_CHANGE_LIDAR_IP) {}
  int handle() override;
};

class InitLidar : public AgingWorkHandler
{

  IMPL_CLASSNAME(InitLidar)

public:
  InitLidar() : AgingWorkHandler(STATE_INIT_LIDAR) {}
  int handle() override;
};

/**************************固件升级状态*************************************/
class FirmwareUpdateUnzip : public AgingWorkHandler
{
  IMPL_CLASSNAME(FirmwareUpdateUnzip)
public:
  FirmwareUpdateUnzip() : AgingWorkHandler(STATE_FIRMWARE_UPDATE_UNZIP) {}
  int handle() override;
};
class FirmwareUpdateApp : public AgingWorkHandler
{
  IMPL_CLASSNAME(FirmwareUpdateApp)
public:
  FirmwareUpdateApp() : AgingWorkHandler(STATE_FIRMWARE_UPDATE_APP) {}
  int handleEnter() override;
  int handle() override;
};
class FirmwareUpdateBot : public AgingWorkHandler
{
  IMPL_CLASSNAME(FirmwareUpdateBot)
public:
  FirmwareUpdateBot() : AgingWorkHandler(STATE_FIRMWARE_UPDATE_BOT) {}
  int handle() override;
};
class FirmwareUpdateTop : public AgingWorkHandler
{
  IMPL_CLASSNAME(FirmwareUpdateTop)
public:
  FirmwareUpdateTop() : AgingWorkHandler(STATE_FIRMWARE_UPDATE_TOP) {}
  int handle() override;
};
class FirmwareUpdateWriteConfig : public AgingWorkHandler
{
  IMPL_CLASSNAME(FirmwareUpdateWriteConfig)
public:
  FirmwareUpdateWriteConfig() : AgingWorkHandler(STATE_FIRMWARE_UPDATE_WRITE_CONFIG) {}
  int handle() override;
};
class FirmwareUpdateReboot : public AgingWorkHandler
{
  IMPL_CLASSNAME(FirmwareUpdateReboot)
public:
  FirmwareUpdateReboot() : AgingWorkHandler(STATE_FIRMWARE_UPDATE_REBOOT) {}
  int handle() override;
};
class FirmwareUpdateCheck : public AgingWorkHandler
{
  IMPL_CLASSNAME(FirmwareUpdateCheck)
public:
  FirmwareUpdateCheck() : AgingWorkHandler(STATE_FIRMWARE_UPDATE_CHECK) {}
  int handle() override;
  int handleExit() override;
};
class ClearCalibData : public AgingWorkHandler
{
  IMPL_CLASSNAME(ClearCalibData)
public:
  ClearCalibData() : AgingWorkHandler(STATE_CLEAR_CALIB_DATA) {}
  int handleEnter() override;
  int handle() override;
  int handleExit() override;
};
/**************************固件升级状态*************************************/

/**************************码盘标定状态*************************************/
class EncodingMotorStable : public AgingWorkHandler
{
  IMPL_CLASSNAME(EncodingMotorStable)
public:
  EncodingMotorStable() : AgingWorkHandler(STATE_ENCODING_CALIB_MOTOR_STABLE) {}
  int handle() override;
};
class EncodingCollect : public AgingWorkHandler
{
  IMPL_CLASSNAME(EncodingCollect)
public:
  EncodingCollect() : AgingWorkHandler(STATE_ENCODING_CALIB_COLLECT) {}
  int handle() override;
  int handleEnter() override;
  int handleExit() override;
};
class EncodingProcessAndWrite : public AgingWorkHandler
{
  IMPL_CLASSNAME(EncodingProcessAndWrite)
public:
  EncodingProcessAndWrite() : AgingWorkHandler(STATE_ENCODING_CALIB_PROCESS_WRITE) {}
  int handle() override;
  int handleEnter() override;
  int handleExit() override;
};
/**************************码盘标定状态*************************************/

class ChnAngleWrite : public AgingWorkHandler
{
  IMPL_CLASSNAME(ChnAngleWrite)
public:
  ChnAngleWrite() : AgingWorkHandler(STATE_CHN_ANGLE_WRITE) {}
  int handle() override;
};

class VbdCalib : public AgingWorkHandler
{
  IMPL_CLASSNAME(VbdCalib)
public:
  VbdCalib() : AgingWorkHandler(STATE_VBD_CALIB) {}
  int handle() override;
};

class InitReadAgingData : public AgingWorkHandler
{
  IMPL_CLASSNAME(InitReadAgingData)

public:
  explicit InitReadAgingData() : AgingWorkHandler(STATE_INIT_READ_DATA) {}
  int handle() override;
};

class DeInitReadAgingData : public AgingWorkHandler
{
  IMPL_CLASSNAME(DeInitReadAgingData)

public:
  explicit DeInitReadAgingData() : AgingWorkHandler(STATE_DEINIT_READ_DATA) {}
  int handle() override;
};

class ReadAgingData : public AgingWorkHandler
{
  IMPL_CLASSNAME(ReadAgingData)
public:
  explicit ReadAgingData() : AgingWorkHandler(STATE_READ_DATA) {}
  int handle() override;

private:
  bool getOpticalErrorRate();
  bool setMesData(bool _is_success);
  bool getAgingData();

  void writeAgingData();
};

class AgingCooling : public AgingWorkHandler
{
  IMPL_CLASSNAME(AgingCooling)
public:
  explicit AgingCooling() : AgingWorkHandler(STATE_COOLING) {}
  int handle() override;
};

class StressTest : public AgingWorkHandler
{
  IMPL_CLASSNAME(StressTest)

public:
  explicit StressTest() : AgingWorkHandler(STATE_STRESS_TEST) {}

  int waitAndValidateMsopPacket(uint32_t _timeout);
  bool checkData();
  int handle() override;

private:
};

class RestoreLidarIp : public AgingWorkHandler
{
  IMPL_CLASSNAME(RestoreLidarIp)
public:
  explicit RestoreLidarIp() : AgingWorkHandler(STATE_RESTORE_LIDAR_IP) {}
  int handle() override;
};

class SuccessHandler : public AgingWorkHandler
{
  IMPL_CLASSNAME(SuccessHandler)
public:
  SuccessHandler() : AgingWorkHandler(STATE_SUCCESS) {}
  int handleState() override;
  int handle() override;
};

class AbortHandler : public AgingWorkHandler
{
  IMPL_CLASSNAME(AbortHandler)

public:
  AbortHandler() : AgingWorkHandler(STATE_ABORT) {}
  int handleState() override;
  int handle() override;
};

class FailHandler : public AgingWorkHandler
{
  IMPL_CLASSNAME(FailHandler)
public:
  explicit FailHandler() : AgingWorkHandler(STATE_FAIL) {}
  int handleState() override;
  int handle() override;
};

class FinalHandler : public AgingWorkHandler
{
  IMPL_CLASSNAME(FinalHandler)
public:
  explicit FinalHandler() : AgingWorkHandler(STATE_FINAL) {}
  int handleState() override;
  int handle() override;
};

}  // namespace lidar
}  // namespace robosense
#endif  // LIDAR_CTR_H
