﻿/******************************************************************************
 * Copyright 2020 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#ifndef DATA_STRUCT_H
#define DATA_STRUCT_H

#include "protocol/protocol_parser.h"
#include "rsfsc_log/rsfsc_log.h"
#include <QHostAddress>
#include <QObject>
#include <QString>
#include <QVariant>
#include <qdatetime.h>

#if defined(_MSC_VER)  // MSVC
#  include <intrin.h>
#  define BSWAP16 _byteswap_ushort
#  define BSWAP32 _byteswap_ulong
#  define BSWAP64 _byteswap_uint64
#elif defined(__GNUC__) || defined(__clang__)  // GCC or Clang
#  define BSWAP16 __builtin_bswap16
#  define BSWAP32 __builtin_bswap32
#  define BSWAP64 __builtin_bswap64
#else
#  error "Unsupported compiler"
#endif

// NOLINTNEXTLINE(modernize-concat-nested-namespaces)
namespace robosense
{
namespace lidar
{
enum ActionState
{
  STATE_CHECK_MES,                     // 检查MES
  STATE_AGING_CONNECT_LIDAR,           // 老化连接雷达
  STATE_FIRMWARE_UPDATE_UNZIP,         // 固件升级解压文件
  STATE_FIRMWARE_UPDATE_APP,           // 固件升级APP
  STATE_FIRMWARE_UPDATE_BOT,           // 固件升级底板
  STATE_FIRMWARE_UPDATE_TOP,           // 固件升级顶板
  STATE_FIRMWARE_UPDATE_WRITE_CONFIG,  // 固件升级写入配置
  STATE_FIRMWARE_UPDATE_REBOOT,        // 固件升级重启
  STATE_FIRMWARE_UPDATE_CHECK,         // 固件升级校验
  STATE_CLEAR_CALIB_DATA,              // 清除标定数据
  STATE_CHANGE_LIDAR_IP,               // 修改雷达IP
  STATE_ENCODING_CALIB_MOTOR_STABLE,   // 码盘标定就绪，等待电机稳定
  STATE_ENCODING_CALIB_COLLECT,        // 码盘标定采集数据
  STATE_ENCODING_CALIB_PROCESS_WRITE,  // 码盘标定计算
  STATE_ENCODING_CALIB_WRITE,          // 码盘标定写入寄存器固化
  STATE_CHN_ANGLE_WRITE,               // 角度写入
  STATE_VBD_CALIB,                     // VBD标定
  STATE_INIT_LIDAR,                    // 初始化雷达
  STATE_INIT_READ_DATA,                // 读取数据前的初始化
  STATE_READ_DATA,                     // 读取数据并校验检查
  STATE_DEINIT_READ_DATA,              // 读取数据后的反初始化
  STATE_STRESS_TEST,                   // 压测
  STATE_RESTORE_LIDAR_IP,              // 恢复雷达IP
  STATE_COOLING,                       // 冷却
  STATE_FAIL,                          // 失败
  STATE_ABORT,                         // 中断
  STATE_SUCCESS,                       // 成功
  STATE_FINAL,                         // 结束
  STATE_END = -1
};
enum EncodeCalibState
{
  ENCODE_CALIB_IDLE,
  ENCODE_CALIB_WAIT,
  ENCODE_CALIB_BUSY,
  ENCODE_CALIB_COLLECT,
  ENCODE_CALIB_PASS,
  ENCODE_CALIB_NG,
  ENCODE_CALIB_SKIP,
  ENCODE_CALIB_ABORT
};
enum FirmwareUpdateState
{
  FIRMWARE_UPDATE_IDLE,
  FIRMWARE_UPDATE_WAIT,
  FIRMWARE_UPDATE_BUSY,
  FIRMWARE_UPDATE_UNZIP,
  FIRMWARE_UPDATE_APP,
  FIRMWARE_UPDATE_BOT,
  FIRMWARE_UPDATE_TOP,
  FIRMWARE_UPDATE_WRITE_CONFIG,
  FIRMWARE_UPDATE_REBOOT,
  FIRMWARE_UPDATE_CHECK,
  FIRMWARE_UPDATE_PASS,
  FIRMWARE_UPDATE_NG,
  FIRMWARE_UPDATE_SKIP,
  FIRMWARE_UPDATE_ABORT
};
enum AgingState
{
  AGING_IDLE,
  AGING_WAIT,
  AGING_BUSY,
  AGING_COOLING,
  AGING_PASS,
  AGING_NG,
  AGING_SKIP,
  AGING_ABORT
};
enum StressState
{
  STRESS_IDLE,
  STRESS_WAIT,
  STRESS_BUSY,
  STRESS_PASS,
  STRESS_NG,
  STRESS_SKIP,
  STRESS_ABORT
};

enum RunState
{
  RUN_IDLE,
  RUN_BUSY,
  RUN_PASS,
  RUN_NG,
  RUN_ABORT
};

enum class ConnectionStatus
{
  UNINITIALIZED,
  CONNECTED,
  DISCONNECTED,
  LAGGING
};

struct ParaInfo
{
  int lidar_index;
  int row_num;
  int col_num;
  ProtocolType lidar_type;

  QString org_ip     = "*************";
  int org_msop_port  = 6699;
  int org_difop_port = 7788;

  bool fsm_change_ip;
  bool fsm_firmware_update;
  bool fsm_clear_calib_data;
  bool fsm_encoding_calib;
  bool fsm_chn_angle_write;
  bool fsm_vbd_calib;
  bool fsm_stress_test;
  bool fsm_aging;

  QString firmware_dir;
  bool is_use_zip_file;
  QString firmware_zip_file_path;

  int ping_timeout;
  int start_up_max_time;

  bool is_maintain_fail_env;

  // aging para
  int aging_time_secs;
  int aging_check_interval;
  int difop_timeout;
  int cooling_time_secs;

  // 监控msop数量
  int check_msop_size;

  // 码盘标定参数
  int encod_calib_collect_num;

  // VBD标定参数
  float vbd_cal_k;

  // 需要压测的次数
  int stress_num;
  // 压测重新上电时间
  int stress_toggle_interval;
  // 压测上电后等待的时间
  int stress_wait_start_time;
  // // 压测时候雷达转速
  // int stress_motor_speed;
  // 压测允许失败
  bool stress_allow_fail;
};

constexpr uint32_t MSOP_PKT_HEAD        = 0x55aa055a;
constexpr uint32_t MSOP_PKT_HEAD_LITTLE = 0x5a05aa55;

constexpr uint32_t ADDR_COEFF     = 0x83c05000;
constexpr uint32_t ADDR_STEP      = 0x83c05400;
constexpr uint32_t ADDR_INFO_LOCK = 0x83c04300;
constexpr uint32_t ADDR_SCALE     = 0x83c05800;
constexpr size_t ENCODE_NUM       = 99;

#pragma pack(push, 1)
struct DistArea
{
  uint16_t dist;
  uint8_t refl;
  void toBigEndian() { dist = BSWAP16(dist); }
};
struct DataBlock96
{
  uint16_t ide1;
  uint16_t azimuth1;
  std::array<DistArea, 48> dist_refl1;
  uint16_t ide2;
  uint16_t azimuth2;
  std::array<DistArea, 48> dist_refl2;
  void toBigEndian()
  {
    ide1     = BSWAP16(ide1);
    azimuth1 = BSWAP16(azimuth1);
    for (auto& dist_refl : dist_refl1)
    {
      dist_refl.toBigEndian();
    }
    ide2     = BSWAP16(ide2);
    azimuth2 = BSWAP16(azimuth2);
    for (auto& dist_refl : dist_refl2)
    {
      dist_refl.toBigEndian();
    }
  }
};
union MsopPacket96
{
  struct
  {
    uint32_t pkt_head;
    uint32_t rev0;
    uint32_t pktcnt_toptobot;
    uint32_t pktcnt_bottops;
    uint16_t data_type;
    uint16_t rev1;
    std::array<uint8_t, 6> timestamp_sec;
    uint32_t timestamp_nano;
    uint8_t rev2;
    uint8_t lidar_type;
    uint8_t lidar_model;
    std::array<uint8_t, 5> rev3;
    uint16_t rx_temp;
    uint16_t topboard_temp;
    std::array<DataBlock96, 4> data_block;
    std::array<uint8_t, 6> tails;
    std::array<uint8_t, 16> rev4;
  };
  std::array<char, 1248> arr;

  void toBigEndian()
  {
    pkt_head        = BSWAP32(pkt_head);
    rev0            = BSWAP32(rev0);
    pktcnt_toptobot = BSWAP32(pktcnt_toptobot);
    pktcnt_bottops  = BSWAP32(pktcnt_bottops);
    data_type       = BSWAP16(data_type);
    rev1            = BSWAP16(rev1);
    timestamp_nano  = BSWAP32(timestamp_nano);
    rx_temp         = BSWAP16(rx_temp);
    topboard_temp   = BSWAP16(topboard_temp);

    std::reverse(timestamp_sec.begin(), timestamp_sec.end());

    for (auto& block : data_block)
    {
      block.toBigEndian();
    }
  }
  void toLittleEndian() { toBigEndian(); }
};

enum class GpsBaudRate : uint8_t
{
  BAUD_1200    = 0x00,
  BAUD_2400    = 0x01,
  BAUD_4800    = 0x02,
  BAUD_9600    = 0x03,
  BAUD_14400   = 0x04,
  BAUD_19200   = 0x05,
  BAUD_38400   = 0x06,
  BAUD_43000   = 0x07,
  BAUD_57600   = 0x08,
  BAUD_76800   = 0x09,
  BAUD_115200  = 0x0A,
  BAUD_128000  = 0x0B,
  BAUD_230400  = 0x0C,
  BAUD_256000  = 0x0D,
  BAUD_460800  = 0x0E,
  BAUD_921600  = 0x0F,
  BAUD_1382400 = 0x10
};
enum class EchoMode : uint8_t
{
  DUAL_ECHO = 0x00,
  STRONGEST = 0x04,
  LAST      = 0x05,
  FIRST     = 0x06
};
enum class TimeSyncMode : uint8_t
{
  GPS    = 0x00,
  E2E    = 0x01,
  P2P    = 0x02,
  GPTP   = 0x03,
  E2E_L2 = 0x04
};

constexpr uint64_t DIFOP_PKT_HEAD = 0xA5FF005A11115555;
constexpr uint16_t DIFOP_PKT_TAIL = 0x0ff0;
union DifopPacket
{
  struct
  {
    uint64_t pkt_head;                // 0x55aa055a
    uint16_t motor_set_speed;         // 电机设置转速 300/600/1200
    uint32_t ip_src;                  // 以太网IP源地址
    uint32_t ip_dst;                  // 以太网IP目标地址
    std::array<uint8_t, 6> mac_addr;  // 雷达MAC地址
    uint16_t msop_port;               // MSOP端口
    uint16_t reserve;
    uint16_t difop_port;  // DIFOP端口
    uint16_t reserve1;
    uint16_t fov_start;  // FOV起始角度, 0-359, 精度0.01°
    uint16_t fov_end;    // FOV结束角度, 0-359, 精度0.01°
    uint16_t reserve2;
    uint16_t lock_phase;                // 锁相相位, 0-360, 精度1°
    uint8_t top_firmware_reserve;       // 主板固件版本
    uint32_t top_firmware_version;      // 主板固件版本
    uint8_t bot_firmware_reserve;       // 底板固件版本
    uint32_t bot_firmware_version;      // 底板固件版本
    uint8_t app_firmware_reserve;       // APP固件版本
    uint32_t app_firmware_version;      // APP固件版本
    uint8_t motor_firmware_reserve;     // 电机固件版本
    uint32_t motor_firmware_version;    // 电机固件版本
    uint8_t cgi_firmware_reserve;       // cgi固件版本
    uint32_t cgi_firmware_version;      // cgi固件版本
    std::array<uint8_t, 223> reserve3;  // 预留
    GpsBaudRate gps_baud_rate;          // GPS同步模式下的GPRMC波特率
    std::array<uint8_t, 3> reserve4;
    std::array<uint8_t, 6> sn;    // 产品序列号
    uint16_t zero_angle;          // 零度角标定值, 0-359.99, 单位0.01度
    EchoMode echo_mode;           // 回波模式 0x00:Dual, 0x04:Strongest, 0x05:Last, 0x06:First
    TimeSyncMode time_sync_mode;  // 时间同步方式设置
    uint8_t time_sync_status;     // 时间同步状态, 0x00:未同步, 0x01:同步成功
    // std::array<uint8_t, 10> time;  // 时间, UTC时间格式, 前6个byte为秒时间戳, 后4个byte为微秒时间戳
    uint64_t time_sec : 48;  // 时间, UTC时间格式, 前6个byte为秒时间戳, 后4个byte为微秒时间戳
    uint32_t time_nano;      // 时间, UTC时间格式, 前6个byte为秒时间戳, 后4个byte为微秒时间戳
    uint8_t reserve5;
    std::array<uint8_t, 19> reserve6;
    std::array<uint8_t, 4> reserve7;
    uint8_t motor_dir;        // 电机正转反转标志, 0x00:正转, 0x01:反转
    uint32_t total_run_time;  // 设备运行总时间, 单位为分钟, 溢出后重新统计
    std::array<uint8_t, 9> reserve8;
    uint16_t reboot_count;  // 设备启动次数, 1-65535循环计数
    std::array<uint8_t, 4> reserve9;
    struct
    {
      uint8_t pps_lock : 1;
      uint8_t gprmc_lock : 1;
      uint8_t utc_lock : 1;
      uint8_t gprmc_input : 1;
      uint8_t pps_input : 1;
      uint8_t reserve_gps : 3;
    };  // GPS状态
    std::array<uint8_t, 8> reserve10;
    std::array<uint8_t, 5> reserve11;
    uint16_t realtime_phase;  // 实时相位, 单位度
    uint16_t realtime_speed;  // 电机实时转速, 单位RPM
    uint32_t start_time;      // 电机启动时间, 单位ms
    std::array<uint8_t, 3> reserve12;
    std::array<uint8_t, 86> gprmc;              // GPRMC
    std::array<uint8_t, 288> vertical_calib;    // 垂直角校准
    std::array<uint8_t, 288> horizontal_calib;  // 水平角校准
    uint16_t top_input_vol;                     // 主板总输入电压, DIFOP: Data/100, 阈值范围: 9~32V
    uint16_t top_3v8;                           // 主板3.8v电压, DIFOP: Data/100, 阈值范围: 3.5~4.1V
    uint16_t top_3v3;                           // 主板3.3v电压, DIFOP: Data/100, 阈值范围: 3~3.6V
    uint16_t reserve13;
    uint16_t reserve14;
    uint16_t top_1v1;  // 主板接收1.1V电压, DIFOP: Data/100, 阈值范围: 1.0~1.2V
    uint16_t reserve15;
    int16_t top_neg_vol;      // 主板接收负压, DIFOP: Data/100, 阈值范围: -25V~-11V
    uint16_t top_3v3_rx;      // 主板接收3.3V电压, DIFOP: Data/100, 阈值范围: 3.2V~3.8V
    uint16_t top_charge_vol;  // 主板发射充能电压, DIFOP: Data/100, 阈值范围: 15V~35V
    uint16_t reserve16;
    uint16_t total_input_vol;  // 整机输入电压, DIFOP: Data/100, 阈值范围: 9V~32V
    uint16_t bot_12v;          // 底板12V电压, DIFOP: Data/100, 阈值范围: 11V~13V
    uint16_t bot_mcu_0v85;     // 底板MCU0.85V电压, DIFOP: Data/100, 阈值范围: 0.8V~0.9V
    uint16_t bot_fpga_1v;      // 底板FPGA内核1V, DIFOP: Data/100, 阈值范围: 0.9V~1.1V
    uint16_t total_input_cur;  // 整机输入电流, DIFOP: Data/100, 整机输入电压*总输入电流
    int16_t top_fpga_temp;     // 主板fpga内核温度, DIFOP: Data/100, 阈值范围: -40度~120度
    uint16_t reserve17;
    int16_t top_tx_temp;        // 主板发射温度, DIFOP: Data/100, 阈值范围: -40度~120度
    int16_t top_rx_459_temp_n;  // 主板RX-459温度N端, DIFOP: Data/100, 阈值范围: -40度~120度
    int16_t top_rx_459_temp_p;  // 主板RX-459温度P端, DIFOP: Data/100, 阈值范围: -40度~120度
    int16_t bot_imu_temp;       // 底板IMU温度, DIFOP: Data/100, 阈值范围: -40度~120度
    int16_t bot_fpga_temp;      // 底板fpga内核温度, DIFOP: Data/100, 阈值范围: -40度~120度
    uint16_t total_power;       // 整机功率, DIFOP: Data/100, 整机输入电压*总输入电流
    float q_x;                  // imu标定数据
    float q_y;                  // imu标定数据
    float q_z;                  // imu标定数据
    float q_w;                  // imu标定数据
    float x;                    // imu标定数据
    float y;                    // imu标定数据
    float z;                    // imu标定数据
    std::array<uint8_t, 126> reserve18;
    uint16_t tail;  // 帧尾, 0x0F 0xF0
  };
  std::array<char, 1248> arr;

  void toBigEndian()
  {
    pkt_head               = BSWAP64(pkt_head);
    motor_set_speed        = BSWAP16(motor_set_speed);
    ip_src                 = BSWAP32(ip_src);
    ip_dst                 = BSWAP32(ip_dst);
    msop_port              = BSWAP16(msop_port);
    difop_port             = BSWAP16(difop_port);
    fov_start              = BSWAP16(fov_start);
    fov_end                = BSWAP16(fov_end);
    lock_phase             = BSWAP16(lock_phase);
    top_firmware_version   = BSWAP32(top_firmware_version);
    bot_firmware_version   = BSWAP32(bot_firmware_version);
    app_firmware_version   = BSWAP32(app_firmware_version);
    motor_firmware_version = BSWAP32(motor_firmware_version);
    cgi_firmware_version   = BSWAP32(cgi_firmware_version);
    zero_angle             = BSWAP16(zero_angle);
    time_sec               = BSWAP64(time_sec);
    time_nano              = BSWAP32(time_nano);
    total_run_time         = BSWAP32(total_run_time);
    reboot_count           = BSWAP16(reboot_count);
    realtime_phase         = BSWAP16(realtime_phase);
    realtime_speed         = BSWAP16(realtime_speed);
    top_input_vol          = BSWAP16(top_input_vol);
    top_3v8                = BSWAP16(top_3v8);
    top_3v3                = BSWAP16(top_3v3);
    top_1v1                = BSWAP16(top_1v1);
    top_neg_vol            = BSWAP16(top_neg_vol);
    top_3v3_rx             = BSWAP16(top_3v3_rx);
    top_charge_vol         = BSWAP16(top_charge_vol);
    total_input_vol        = BSWAP16(total_input_vol);
    bot_12v                = BSWAP16(bot_12v);
    bot_mcu_0v85           = BSWAP16(bot_mcu_0v85);
    bot_fpga_1v            = BSWAP16(bot_fpga_1v);
    total_input_cur        = BSWAP16(total_input_cur);
    top_fpga_temp          = BSWAP16(top_fpga_temp);
    top_tx_temp            = BSWAP16(top_tx_temp);
    top_rx_459_temp_n      = BSWAP16(top_rx_459_temp_n);
    top_rx_459_temp_p      = BSWAP16(top_rx_459_temp_p);
    bot_imu_temp           = BSWAP16(bot_imu_temp);
    bot_fpga_temp          = BSWAP16(bot_fpga_temp);
    total_power            = BSWAP16(total_power);
    tail                   = BSWAP16(tail);
  }
};

struct GdiRegU8
{
  uint8_t reg_flag;
  uint8_t reg_addr_high;
  uint8_t reg_addr_low;
  uint8_t reg_data;

  // NOLINTNEXTLINE(bugprone-easily-swappable-parameters)
  void setData(const uint32_t _addr, const uint8_t _data)
  {
    reg_flag      = (_addr >> 16U) & 0xffU;
    reg_addr_high = (_addr >> 8U) & 0xffU;
    reg_addr_low  = _addr & 0xffU;
    reg_data      = _data;
  }
};
struct GdiRegU16
{
  GdiRegU8 reg_high;
  GdiRegU8 reg_low;

  // NOLINTNEXTLINE(bugprone-easily-swappable-parameters)
  void setData(const uint32_t _addr, const uint16_t _data)
  {
    reg_high.setData(_addr, static_cast<uint16_t>(_data >> 8U) & 0xffU);
    reg_low.setData(_addr + 1, _data & 0xffU);
  }
};
struct GdiRegU24
{
  GdiRegU8 reg_high;
  GdiRegU8 reg_mid;
  GdiRegU8 reg_low;

  // NOLINTNEXTLINE(bugprone-easily-swappable-parameters)
  void setData(const uint32_t _addr, const uint32_t _data)
  {
    reg_high.setData(_addr, static_cast<uint16_t>(_data >> 16U) & 0xffU);
    reg_mid.setData(_addr + 1, static_cast<uint16_t>(_data >> 8U) & 0xffU);
    reg_low.setData(_addr + 2, _data & 0xffU);
  }
};
union CustomGdiReg
{
  struct
  {
    GdiRegU16 vbd_err;
    GdiRegU24 vbd_intercept;
    GdiRegU8 refl_calib_version_reg;
  };

  std::array<char, 1024> arr;
};

union CustomGdiFeild
{
  struct
  {
    CustomGdiReg custom_gdi_reg;
    uint16_t gdi_crc;
  };

  std::array<char, 65536> arr;
  [[nodiscard]] static uint32_t getStartAddr() { return 0xfb0000; }
};
#pragma pack(pop)

// NOLINTNEXTLINE(cppcoreguidelines-macro-usage)
#define DECLARE_PROPERTY(TYPE, NAME, FUNC_NAME)                                  \
  Q_PROPERTY(TYPE NAME READ get##FUNC_NAME WRITE set##FUNC_NAME)                 \
  Q_PROPERTY(QVariant NAME##type READ get##FUNC_NAME##Type)                      \
public:                                                                          \
  TYPE get##FUNC_NAME() const { return NAME##_; }                                \
  void set##FUNC_NAME(const TYPE& _value) { (NAME##_) = _value; }                \
                                                                                 \
  QVariant get##FUNC_NAME##Type() const { return QVariant::fromValue(NAME##_); } \
                                                                                 \
private:                                                                         \
  TYPE NAME##_

class DifopInfo : public QObject
{
  Q_OBJECT
public:
  explicit DifopInfo(const DifopPacket& _difop_pkt = DifopPacket(),
                     const QDateTime& _date_time   = QDateTime::currentDateTime(),
                     QObject* _parent              = nullptr) :
    QObject(_parent)
  {
    setDateTime(_date_time);
    setDifopPacket(_difop_pkt);
  };
  DifopInfo(const DifopInfo&) = delete;
  DifopInfo(DifopInfo&&)      = delete;
  DifopInfo& operator=(const DifopInfo&) = delete;
  DifopInfo& operator=(DifopInfo&&) = delete;
  ~DifopInfo() override             = default;

  DECLARE_PROPERTY(QString, sn, Sn);
  DECLARE_PROPERTY(int, motor_set_speed, MotorSetSpeed) {};
  DECLARE_PROPERTY(QString, ip_src, IpSrc);
  DECLARE_PROPERTY(QString, ip_dst, IpDst);
  DECLARE_PROPERTY(QString, mac_addr, MacAddr);
  DECLARE_PROPERTY(int, msop_port, MsopPort) {};
  DECLARE_PROPERTY(int, difop_port, DifopPort) {};
  DECLARE_PROPERTY(int, fov_start, FovStart) {};
  DECLARE_PROPERTY(int, fov_end, FovEnd) {};
  DECLARE_PROPERTY(int, lock_phase, LockPhase) {};
  DECLARE_PROPERTY(uint32_t, top_firmware_version, TopFirmwareVersion) {};
  DECLARE_PROPERTY(uint32_t, bot_firmware_version, BotFirmwareVersion) {};
  DECLARE_PROPERTY(uint32_t, app_firmware_version, AppFirmwareVersion) {};
  DECLARE_PROPERTY(uint32_t, motor_firmware_version, MotorFirmwareVersion) {};
  DECLARE_PROPERTY(uint32_t, cgi_firmware_version, CgiFirmwareVersion) {};
  DECLARE_PROPERTY(int, gps_baud_rate, GpsBaudRate) {};
  DECLARE_PROPERTY(uint64_t, time_sec, TimeSec) {};
  DECLARE_PROPERTY(uint32_t, time_nano, TimeNano) {};
  DECLARE_PROPERTY(int, time_sync_status, TimeSyncStatus) {};
  DECLARE_PROPERTY(int, motor_dir, MotorDir) {};
  DECLARE_PROPERTY(uint32_t, total_run_time, TotalRunTime) {};
  DECLARE_PROPERTY(int, reboot_count, RebootCount) {};
  DECLARE_PROPERTY(int, pps_lock, PpsLock) {};
  DECLARE_PROPERTY(int, gprmc_lock, GprmcLock) {};
  DECLARE_PROPERTY(int, utc_lock, UtcLock) {};
  DECLARE_PROPERTY(int, gprmc_input, GprmcInput) {};
  DECLARE_PROPERTY(int, pps_input, PpsInput) {};
  DECLARE_PROPERTY(int, realtime_phase, RealtimePhase) {};
  DECLARE_PROPERTY(int, realtime_speed, RealtimeSpeed) {};
  DECLARE_PROPERTY(uint32_t, start_time, StartTime) {};
  DECLARE_PROPERTY(float, top_input_vol, TopInputVol) {};
  DECLARE_PROPERTY(float, top_3v8, Top3v8) {};
  DECLARE_PROPERTY(float, top_3v3, Top3v3) {};
  DECLARE_PROPERTY(float, top_1v1, Top1v1) {};
  DECLARE_PROPERTY(float, top_neg_vol, TopNegVol) {};
  DECLARE_PROPERTY(float, top_3v3_rx, Top3v3Rx) {};
  DECLARE_PROPERTY(float, top_charge_vol, TopChargeVol) {};
  DECLARE_PROPERTY(float, total_input_vol, TotalInputVol) {};
  DECLARE_PROPERTY(float, bot_12v, Bot12v) {};
  DECLARE_PROPERTY(float, bot_mcu_0v85, BotMcu0v85) {};
  DECLARE_PROPERTY(float, bot_fpga_1v, BotFpga1v) {};
  DECLARE_PROPERTY(float, total_input_cur, TotalInputCur) {};
  DECLARE_PROPERTY(float, top_fpga_temp, TopFpgaTemp) {};
  DECLARE_PROPERTY(float, top_tx_temp, TopTxTemp) {};
  DECLARE_PROPERTY(float, top_rx_459_temp_n, TopRx459TempN) {};
  DECLARE_PROPERTY(float, top_rx_459_temp_p, TopRx459TempP) {};
  DECLARE_PROPERTY(float, bot_imu_temp, BotImuTemp) {};
  DECLARE_PROPERTY(float, bot_fpga_temp, BotFpgaTemp) {};
  DECLARE_PROPERTY(float, total_power, TotalPower) {};

public:
  void setDifopPacket(const DifopPacket& _difop_pkt)
  {
    sn_                     = QString::fromStdString(fmt::format("{:X}", fmt::join(_difop_pkt.sn, "")));
    motor_set_speed_        = _difop_pkt.motor_set_speed;
    ip_src_                 = QHostAddress(_difop_pkt.ip_src).toString();
    ip_dst_                 = QHostAddress(_difop_pkt.ip_dst).toString();
    mac_addr_               = QString::fromStdString(fmt::format("{:X}", fmt::join(_difop_pkt.mac_addr, ":")));
    msop_port_              = _difop_pkt.msop_port;
    difop_port_             = _difop_pkt.difop_port;
    top_firmware_version_   = _difop_pkt.top_firmware_version;
    bot_firmware_version_   = _difop_pkt.bot_firmware_version;
    app_firmware_version_   = _difop_pkt.app_firmware_version;
    motor_firmware_version_ = _difop_pkt.motor_firmware_version;
    cgi_firmware_version_   = _difop_pkt.cgi_firmware_version;
    gps_baud_rate_          = static_cast<int>(_difop_pkt.gps_baud_rate);
    time_sec_               = _difop_pkt.time_sec;
    time_nano_              = _difop_pkt.time_nano;
    time_sync_status_       = _difop_pkt.time_sync_status;
    motor_dir_              = _difop_pkt.motor_dir;
    total_run_time_         = _difop_pkt.total_run_time;
    reboot_count_           = _difop_pkt.reboot_count;
    pps_lock_               = _difop_pkt.pps_lock;
    gprmc_lock_             = _difop_pkt.gprmc_lock;
    utc_lock_               = _difop_pkt.utc_lock;
    gprmc_input_            = _difop_pkt.gprmc_input;
    pps_input_              = _difop_pkt.pps_input;
    realtime_phase_         = _difop_pkt.realtime_phase;
    realtime_speed_         = _difop_pkt.realtime_speed;
    start_time_             = _difop_pkt.start_time;
    top_input_vol_          = static_cast<float>(_difop_pkt.top_input_vol / 100.0);
    top_3v8_                = static_cast<float>(_difop_pkt.top_3v8 / 100.0);
    top_3v3_                = static_cast<float>(_difop_pkt.top_3v3 / 100.0);
    top_1v1_                = static_cast<float>(_difop_pkt.top_1v1 / 100.0);
    top_neg_vol_            = static_cast<float>(_difop_pkt.top_neg_vol / 100.0);
    top_3v3_rx_             = static_cast<float>(_difop_pkt.top_3v3_rx / 100.0);
    top_charge_vol_         = static_cast<float>(_difop_pkt.top_charge_vol / 100.0);
    total_input_vol_        = static_cast<float>(_difop_pkt.total_input_vol / 100.0);
    bot_12v_                = static_cast<float>(_difop_pkt.bot_12v / 100.0);
    bot_mcu_0v85_           = static_cast<float>(_difop_pkt.bot_mcu_0v85 / 100.0);
    bot_fpga_1v_            = static_cast<float>(_difop_pkt.bot_fpga_1v / 100.0);
    total_input_cur_        = static_cast<float>(_difop_pkt.total_input_cur / 100.0);
    top_fpga_temp_          = static_cast<float>(_difop_pkt.top_fpga_temp / 100.0);
    top_tx_temp_            = static_cast<float>(_difop_pkt.top_tx_temp / 100.0);
    top_rx_459_temp_n_      = static_cast<float>(_difop_pkt.top_rx_459_temp_n / 100.0);
    top_rx_459_temp_p_      = static_cast<float>(_difop_pkt.top_rx_459_temp_p / 100.0);
    bot_imu_temp_           = static_cast<float>(_difop_pkt.bot_imu_temp / 100.0);
    bot_fpga_temp_          = static_cast<float>(_difop_pkt.bot_fpga_temp / 100.0);
    total_power_            = static_cast<float>(_difop_pkt.total_power / 100.0);
  }

  [[nodiscard]] QDateTime getDateTime() const { return datetime_; }
  void setDateTime(const QDateTime& _datetime) { datetime_ = _datetime; }

  [[nodiscard]] bool isValid() const { return datetime_.isValid(); }

private:
  QDateTime datetime_;
};

}  // namespace lidar
}  // namespace robosense

#endif  // DATA_STRUCT_H
