﻿/******************************************************************************
 * Copyright 2020 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#ifndef SINGLETON_H
#define SINGLETON_H

/**
 * @file singleton.h
 * @brief 单例
 * @code 单例使用例程
 * class Dummy: public Singleton<Dummy>{
 * DECL_SINGLETON(Dummy)
 * public:
 *     virtual ~Dummy();
 *     ...
 * };
 * Dummy::Dummy()
 * {
 * }
 * @endcode
 */
namespace robosense  // NOLINT(modernize-concat-nested-namespaces)
{
namespace lidar
{
/** 单例模板类子类构造函数定义,用于定义单例模板类子类的构造函数 */
#define DECL_SINGLETON(InstanceClass)    \
private:                                 \
  friend class Singleton<InstanceClass>; \
  InstanceClass();

/**
 * @brief 单例模板类
 * @tparam T 
 */
template <typename T>
class Singleton
{
public:
  static T& getInstance()
  {
    static T instance;
    return instance;
  }
  virtual ~Singleton() = default;

protected:
  Singleton() {};

public:
  Singleton(Singleton&&) = delete;
  Singleton& operator=(Singleton&&) = delete;
  Singleton(const Singleton&)       = delete;
  Singleton& operator=(const Singleton&) = delete;
};

}  // namespace lidar

}  // namespace robosense
#endif
