﻿/******************************************************************************
 * Copyright 2024 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai
 *
 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.
 *
 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONS<PERSON>QUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#include "aging_data_model_view.h"
#include "rsfsc_log/rsfsc_log_macro.h"
#include <QHostAddress>

// NOLINTNEXTLINE(modernize-concat-nested-namespaces)
namespace robosense
{
namespace lidar
{

AgingDataModel::AgingDataModel(QObject* _parent) : QStandardItemModel(_parent)
{
  setHorizontalHeaderLabels({ "字段属性", "数值" });

  appendRow({ new QStandardItem("SN"), new QStandardItem() });
  appendRow({ new QStandardItem("设置转速"), new QStandardItem() });
  appendRow({ new QStandardItem("IP"), new QStandardItem() });
  appendRow({ new QStandardItem("dst IP"), new QStandardItem() });
  appendRow({ new QStandardItem("MAC地址"), new QStandardItem() });
  appendRow({ new QStandardItem("MSOP"), new QStandardItem() });
  appendRow({ new QStandardItem("DIFOP"), new QStandardItem() });
  appendRow({ new QStandardItem("主板固件版本"), new QStandardItem() });
  appendRow({ new QStandardItem("底板固件版本"), new QStandardItem() });
  appendRow({ new QStandardItem("APP固件版本"), new QStandardItem() });
  appendRow({ new QStandardItem("电机固件版本"), new QStandardItem() });
  appendRow({ new QStandardItem("CGI固件版本"), new QStandardItem() });
  appendRow({ new QStandardItem("运行时间"), new QStandardItem() });
  appendRow({ new QStandardItem("启动次数"), new QStandardItem() });
  appendRow({ new QStandardItem("实时转速"), new QStandardItem() });
  appendRow({ new QStandardItem("主板主电压1.1V"), new QStandardItem() });
  appendRow({ new QStandardItem("主板主电压3.3V"), new QStandardItem() });
  appendRow({ new QStandardItem("主板主电压3.8V"), new QStandardItem() });
  appendRow({ new QStandardItem("主板主电压负压"), new QStandardItem() });
  appendRow({ new QStandardItem("主板接收电压3.3V"), new QStandardItem() });
  appendRow({ new QStandardItem("主板发射充能电压"), new QStandardItem() });
  appendRow({ new QStandardItem("整机输入电压"), new QStandardItem() });
  appendRow({ new QStandardItem("底板12V电压"), new QStandardItem() });
  appendRow({ new QStandardItem("底板MCU0.85V电压"), new QStandardItem() });
  appendRow({ new QStandardItem("底板FPGA内核1V电压"), new QStandardItem() });
  appendRow({ new QStandardItem("整机输入电流"), new QStandardItem() });
  appendRow({ new QStandardItem("主板FPGA内核温度"), new QStandardItem() });
  appendRow({ new QStandardItem("主板发射温度"), new QStandardItem() });
  appendRow({ new QStandardItem("主板RX-459温度N端"), new QStandardItem() });
  appendRow({ new QStandardItem("主板RX-459温度P端"), new QStandardItem() });
  appendRow({ new QStandardItem("底板IMU温度"), new QStandardItem() });
  appendRow({ new QStandardItem("底板FPGA内核温度"), new QStandardItem() });
  appendRow({ new QStandardItem("整机功率"), new QStandardItem() });
};
AgingDataModel::~AgingDataModel() = default;

void AgingDataModel::updateModel(const DifopPacket& _difop_pkt)
{
  int count = 0;
  DifopInfo difop_info(_difop_pkt);
  setData(index(count++, 1), difop_info.getSn());
  setData(index(count++, 1), difop_info.getMotorSetSpeed());
  setData(index(count++, 1), difop_info.getIpSrc());
  setData(index(count++, 1), difop_info.getIpDst());
  setData(index(count++, 1), difop_info.getMacAddr());
  setData(index(count++, 1), difop_info.getMsopPort());
  setData(index(count++, 1), difop_info.getDifopPort());
  setData(index(count++, 1), QString::fromStdString(fmt::format("{:#x}", difop_info.getTopFirmwareVersion())));
  setData(index(count++, 1), QString::fromStdString(fmt::format("{:#x}", difop_info.getBotFirmwareVersion())));
  setData(index(count++, 1), QString::fromStdString(fmt::format("{:#x}", difop_info.getAppFirmwareVersion())));
  setData(index(count++, 1), QString::fromStdString(fmt::format("{:#x}", difop_info.getMotorFirmwareVersion())));
  setData(index(count++, 1), QString::fromStdString(fmt::format("{:#x}", difop_info.getCgiFirmwareVersion())));
  setData(index(count++, 1), difop_info.getTotalRunTime());
  setData(index(count++, 1), difop_info.getRebootCount());
  setData(index(count++, 1), difop_info.getRealtimeSpeed());
  setData(index(count++, 1), difop_info.getTop1v1());
  setData(index(count++, 1), difop_info.getTop3v3());
  setData(index(count++, 1), difop_info.getTop3v8());
  setData(index(count++, 1), difop_info.getTopNegVol());
  setData(index(count++, 1), difop_info.getTop3v3Rx());
  setData(index(count++, 1), difop_info.getTopChargeVol());
  setData(index(count++, 1), difop_info.getTotalInputVol());
  setData(index(count++, 1), difop_info.getBot12v());
  setData(index(count++, 1), difop_info.getBotMcu0v85());
  setData(index(count++, 1), difop_info.getBotFpga1v());
  setData(index(count++, 1), difop_info.getTotalInputCur());
  setData(index(count++, 1), difop_info.getTopFpgaTemp());
  setData(index(count++, 1), difop_info.getTopTxTemp());
  setData(index(count++, 1), difop_info.getTopRx459TempN());
  setData(index(count++, 1), difop_info.getTopRx459TempP());
  setData(index(count++, 1), difop_info.getBotImuTemp());
  setData(index(count++, 1), difop_info.getBotFpgaTemp());
  setData(index(count++, 1), difop_info.getTotalPower());
}

}  // namespace lidar
}  // namespace robosense