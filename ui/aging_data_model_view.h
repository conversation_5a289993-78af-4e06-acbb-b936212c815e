﻿/******************************************************************************
 * Copyright 2024 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai
 *
 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.
 *
 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONS<PERSON>QUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#ifndef AGING_AGING_DATA_MODEL_VIEW_H
#define AGING_AGING_DATA_MODEL_VIEW_H

#include "data_struct.h"
#include <QStandardItemModel>

// NOLINTNEXTLINE(modernize-concat-nested-namespaces)
namespace robosense
{
namespace lidar
{

class AgingDataModel : public QStandardItemModel
{
  Q_OBJECT
public:
  explicit AgingDataModel(QObject* _parent = nullptr);
  explicit AgingDataModel(AgingDataModel&&)      = delete;
  explicit AgingDataModel(const AgingDataModel&) = delete;
  AgingDataModel& operator=(AgingDataModel&&) = delete;
  AgingDataModel& operator=(const AgingDataModel&) = delete;
  ~AgingDataModel() override;

  void updateModel(const DifopPacket& _difop_pkt);

private:
  DifopPacket difop_pkt_;
};

}  // namespace lidar
}  // namespace robosense
#endif  // AGING_AGING_DATA_MODEL_VIEW_H