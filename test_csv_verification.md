# CSV 回读校验功能测试文档

## 功能概述

在 `LidarManager::writeCsvData` 函数中添加了回读校验功能，实现以下特性：

1. **路径管理**：在 `Path` 结构中存储 CSV 校验文件路径
2. **回读校验**：写入寄存器后立即回读并比较数据
3. **数据记录**：将写入和读取的数据记录到 CSV 文件中
4. **智能跳过**：CMD 寄存器完全跳过校验，不记录到文件
5. **可选功能**：当路径为空时不执行回读校验

## 实现细节

### 1. Path 结构扩展
- 在 `AgingWorkModel::Path` 结构中添加 `QString csv_verification_file_path`

### 2. 新增成员变量
- `QString csv_verification_file_path_`：存储 CSV 校验文件路径

### 3. 新增方法
- `setCsvVerificationFilePath(const QString& _file_path)`：设置校验文件路径
- `getCsvVerificationFilePath() const`：获取校验文件路径
- `writeVerificationDataToFile(...)`：私有方法，记录校验数据到文件

### 4. 修改的函数
- `AgingWorkModel::checkAllState()`：设置 CSV 校验文件路径为 `data_dir/verification.csv`
- `LidarManager::writeCsvData(const QString& _key)`：添加回读校验逻辑

## 校验流程

1. **写入数据**：按原有逻辑写入寄存器数据
2. **回读验证**：
   - 等待 10ms 确保写入完成
   - 回读寄存器数据
   - 比较写入值和读取值
3. **记录结果**：将校验结果写入 CSV 文件

## CSV 文件格式

```csv
Timestamp,Register_Name,Address,Write_Value,Read_Value,Match_Status
2024-01-01 12:00:00.123,reg_name,0x1000,0x12345678,0x12345678,PASS
2024-01-01 12:00:00.456,reg_name2,0x1004,0x87654321,0x87654320,FAIL
```

## 特殊处理

1. **CMD 寄存器**：完全跳过回读校验，不记录到文件（因为 CMD 寄存器通常不支持回读）
2. **写入失败**：记录写入失败的情况到文件
3. **回读失败**：记录回读失败的情况

## 日志输出

- 成功校验：`LOG_INDEX_INFO` 记录成功信息
- 校验失败：`LOG_INDEX_ERROR` 记录失败信息
- 整体结果：如果存在校验失败项，输出错误日志

## 使用方式

1. **自动启用**：在 work model 的 `checkAllState` 阶段自动设置路径
2. **手动控制**：可通过 `setCsvVerificationFilePath("")` 禁用功能
3. **文件位置**：校验文件保存在数据目录下的 `verification.csv`

## 代码优化

### fmt 格式化优化
- 使用 `fmt::format` 替代 Qt 的 `QString::arg` 方法
- 提高字符串格式化性能和可读性
- 统一格式化风格，与项目其他部分保持一致

**优化前（Qt arg）：**
```cpp
stream << QString("%1,%2,0x%3,0x%4,0x%5,%6\n")
          .arg(timestamp)
          .arg(_reg_name)
          .arg(_address, 0, 16)
          .arg(_write_value, 0, 16)
          .arg(_read_value, 0, 16)
          .arg(match_status);
```

**优化后（fmt）：**
```cpp
std::string csv_line = fmt::format("{},{},0x{:x},0x{:x},0x{:x},{}\n",
                                   timestamp.toStdString(),
                                   _reg_name.toStdString(),
                                   _address,
                                   _write_value,
                                   _read_value,
                                   match_status);
stream << QString::fromStdString(csv_line);
```

## 兼容性

- 向后兼容：当路径为空时，功能不启用，不影响原有逻辑
- 性能影响：每个寄存器增加约 10ms 的回读时间
- 错误处理：回读失败不影响写入操作的返回结果
- 格式化性能：使用 fmt 库提高字符串格式化效率

## 测试建议

1. 验证正常写入和回读场景
2. 测试 CMD 寄存器的特殊处理
3. 验证文件记录的正确性
4. 测试路径为空时的兼容性
5. 验证 fmt 格式化的正确性和性能
